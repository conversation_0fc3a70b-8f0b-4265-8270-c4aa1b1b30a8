import { ref } from "vue";
import { getRoleList } from "@/api/role";

export function useColumns() {
  const dataList = ref([]);
  getRoleList({}).then((res: any) => {
    console.log("API返回数据：", res.data.roles);
    dataList.value = res.data.roles || res.data;
  });
  // 表格的表头内容
  const columns: TableColumnList = [
    {
      label: "角色名称",
      prop: "name"
    },
    {
      label: "描述",
      prop: "description"
    },
    {
      label: "账号状态",
      prop: "disabled",
      slot: "disabled"
    },
    {
      label: "操作",
      fixed: "right",
      width: 240,
      slot: "operation"
    }
  ];
  // 获取用户列表
  const refreshTable = () => {
    getRoleList({}).then((res: any) => {
      dataList.value = res.data.roles || res.data;
    });
  };
  return {
    columns,
    dataList,
    refreshTable
  };
}
