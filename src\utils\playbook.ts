import { ElMessage } from "element-plus";

/**
 * 参数唯一性校验器
 * @param tableData 表格数据
 * @returns 校验结果
 */
export function validateUniqueParameterNames(tableData: any[]) {
  const nameMap = new Map<string, number>();

  // 收集所有参数名
  const duplicateNames = tableData
    .map(item => item.name?.trim())
    .filter(name => {
      if (!name) return false;
      const count = nameMap.get(name) || 0;
      nameMap.set(name, count + 1);
      return count >= 1;
    });

  // 检查重复项
  if (duplicateNames.length > 0) {
    const uniqueNames = Array.from(new Set(duplicateNames));
    ElMessage.error({
      message: `参数名不能重复：${uniqueNames.join(", ")}`,
      duration: 3000,
      type: "error"
    });
    console.error("参数名重复错误:", uniqueNames);
    return false;
  }

  return true;
}
