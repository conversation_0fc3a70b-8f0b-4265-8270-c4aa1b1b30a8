import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";
// 获取授权信息
export const getLicenseData = (data: any) => {
  return http.post(baseUrlApi("license/show"), { data });
};
// 导入授权
export const importLicense = (formData: FormData) => {
  return http.post(baseUrlApi("license/verify"), undefined, {
    headers: {
      "Content-Type": "multipart/form-data"
    },
    data: formData
  });
};
