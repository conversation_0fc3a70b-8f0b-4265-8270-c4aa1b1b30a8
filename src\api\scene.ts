import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

// 获取场景列表
export const getSceneList = (data: any) => {
  return http.post(baseUrlApi("scene/all"), { data });
};

// 获取当前场景
export const getSceneName = (data: any) => {
  return http.post(baseUrlApi("user/scene-list"), { data });
};

// 添加场景
export const addScene = (data: any) => {
  return http.post(baseUrlApi("scene/add"), { data });
};
// 更新场景
export const editScene = (data: any) => {
  return http.post(baseUrlApi("scene/update"), { data });
};
// 设置当前场景
export const setCurrentScene = (data: any) => {
  return http.post(baseUrlApi("scene/current"), { data });
};
