import { http } from "@/utils/http";
import { baseUrlApi } from "./utils"; // 获取剧本列表

// 获取剧本列表
export const apiGetPlaybookList = (data: any) => {
  return http.post(baseUrlApi("playbook/list"), { data });
};

// 新建剧本
export const apiCreatePlaybook = (data: any) => {
  return http.post(baseUrlApi("playbook/new"), { data });
};

// 获取工具当前版本的所有动作列表
export const apiGetToolList = (data: any) => {
  return http.post(baseUrlApi("tools/all"), { data });
};

// 获取具体工具的所有版本列表
export const apiGetToolVersionsList = (data: any) => {
  return http.post(baseUrlApi("tools/versions"), { data });
};

// 获取工具当前版本的所有动作列表
export const apiGetActionList = (data: any) => {
  return http.post(baseUrlApi("tools/actions"), { data });
};

// 获取剧本版本列表
export const apiGetPlaybookVersionsList = (data: any) => {
  return http.post(baseUrlApi("playbook/versions"), { data });
};

// 修改剧本的基本信息
export const apiPlaybookEdit = (data: any) => {
  return http.post(baseUrlApi("playbook/edit"), { data });
};

// 删除剧本
export const apiDeletePlaybook = (data: any) => {
  return http.post(baseUrlApi("playbook/del"), { data });
};

// 获取剧本的输入参数
export const apiGetPlaybookInput = (data: any) => {
  return http.post(baseUrlApi("playbook/input"), { data });
};

// 获取工具的资源详情（需要工具查看权限）
export const apiGetResourceDetailed = (data: any) => {
  return http.post(baseUrlApi("tools/resource/detailed"), { data });
};

// 发布剧本
export const apiPlaybookPublish = (data: any) => {
  return http.post(baseUrlApi("playbook/publish"), { data });
};

//修改剧本流程图内容,修改已发布的剧本会自动生成一个新的版本
export const apiPlaybookEditFlow = (data: any) => {
  return http.post(baseUrlApi("playbook/edit-flow"), { data });
};

//删除剧本版本
export const apiDeletePlaybookVersion = (data: any) => {
  return http.post(baseUrlApi("playbook/del-version"), { data });
};

//获取所有场景 (需要场景查看权限)
export const apiGetSceneAll = (data: any) => {
  return http.post(baseUrlApi("scene/all"), { data });
};

//获取标签列表（需要剧本查看权限）
export const apiGetPlaybookTagList = (data: any) => {
  return http.post(baseUrlApi("playbook/tag/list"), { data });
};

//新建标签（需要剧本新增权限）
export const apiPlaybookTagNew = (data: any) => {
  return http.post(baseUrlApi("playbook/tag/new"), { data });
};

//删除标签（需要剧本删除权限）
export const apiPlaybookTagRemove = (data: any) => {
  return http.post(baseUrlApi("playbook/tag/remove"), { data });
};

//设置当前场景 (需要场景更新权限)
export const apiSceneCurrent = (data: any) => {
  return http.post(baseUrlApi("scene/current"), { data });
};

//删除场景 (需要场景删除权限)
export const apiSceneRemove = (data: any) => {
  return http.post(baseUrlApi("scene/remove"), { data });
};

//获取剧本权限（需要剧本查看权限）
export const apiGetPlaybookPermissionsSelect = (data: any) => {
  return http.post(baseUrlApi("playbook/permissions/select"), { data });
};

//新增剧本权限（需要剧本更新权限）
export const apiPlaybookPermissionsAdd = (data: any) => {
  return http.post(baseUrlApi("playbook/permissions/add"), { data });
};

// 获取自定义权限数据
export const apiGetPlaybookPermissionsCurrent = (data: any) => {
  return http.post(baseUrlApi("playbook/permissions/current"), { data });
};

// 获取所有全局变量信息（需要var:r权限）
export const apiGetVarList = (data: any) => {
  return http.post(baseUrlApi("var/list"), { data });
};
