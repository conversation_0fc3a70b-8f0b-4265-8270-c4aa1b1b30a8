FROM --platform=$BUILDPLATFORM node:20-alpine AS build-stage

WORKDIR /app

RUN corepack enable
RUN corepack prepare pnpm@latest --activate

COPY .npmrc package.json pnpm-lock.yaml ./
RUN pnpm install --frozen-lockfile --registry=https://mirrors.huaweicloud.com/repository/npm/

COPY . .
RUN pnpm build

FROM nginx:1.27.4 AS production-stage

COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
COPY lion.conf /etc/nginx/conf.d/default.conf
COPY key.pem /etc/nginx/key.pem
COPY cert.pem /etc/nginx/cert.pem

ENV TZ="Asia/Shanghai"

CMD ["nginx", "-g", "daemon off;"]
