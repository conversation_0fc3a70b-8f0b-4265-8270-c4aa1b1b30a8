import { HtmlNode, HtmlNodeModel } from "@logicflow/core";
import { createApp, h } from "vue";
import EndNode from "@/views/playbook/playbookEdit/icon/EndNode.vue";

class EndNodeView extends HtmlNode {
  isMounted: boolean;
  r: any;
  app: any;

  constructor(props) {
    super(props);
    this.isMounted = false;
    this.r = h(EndNode, {
      properties: props.model.getProperties(),
      text: props.model.inputData,
      model: props.model,
      graphModel: props.graphModel,
      onBtnCopyClick: () => {
        props.graphModel.eventCenter.emit("custom:onBtnCopyClick", {
          props: this.props
        });
      },
      onBtnDelClick: () => {
        props.graphModel.eventCenter.emit("custom:onBtnDelClick", {
          props: this.props
        });
      }
    });
    this.app = createApp({
      render: () => this.r
    });
  }

  shouldUpdate() {
    if (this.preProperties && this.preProperties === this.currentProperties)
      return;
    this.preProperties = this.currentProperties;
    return true;
  }

  setHtml(rootEl) {
    if (!this.isMounted) {
      this.isMounted = true;
      const node = document.createElement("div");
      rootEl.appendChild(node);
      this.app.mount(node);
    } else {
      this.r.component.props.properties = this.props.model.getProperties();
    }
  }
}

class EndNodeModel extends HtmlNodeModel {
  setAttributes() {
    this.width = 100;
    this.height = 100;
    this.text.editable = false;
    this.inputData = this.text.value;
  }

  // 定义结束节点只有左锚点. 锚点位置通过中心点和宽度算出来。
  getDefaultAnchor() {
    const { width, x, y, id } = this;
    return [
      {
        x: x - width / 2,
        y,
        name: "left",
        id: `${id}_1`
      }
    ];
  }

  getConnectedSourceRules() {
    const rules = super.getConnectedSourceRules();
    const geteWayOnlyAsTarget = {
      message: "结束节点只能连入，不能连出！",
      validate: (source: EndNodeModel) => {
        let isValid = true;
        if (source) {
          isValid = false;
        }
        return isValid;
      }
    };
    rules.push(geteWayOnlyAsTarget);
    return rules;
  }

  getOutlineStyle() {
    const style = super.getOutlineStyle();
    style.stroke = "none";
    style.hover.stroke = "red";
    return style;
  }
}

export default {
  type: "end-node",
  view: EndNodeView,
  model: EndNodeModel
};
