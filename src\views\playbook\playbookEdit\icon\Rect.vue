<template>
  <svg>
    <g style="visibility: visible" transform="translate(0.5,0.5)">
      <rect
        fill="#ffffff"
        height="27.2"
        pointer-events="all"
        stroke="#000000"
        stroke-width="1.3"
        width="27.2"
        x="2.38"
        y="1.36"
      />
    </g>
  </svg>
</template>
<script setup></script>
<style scoped>
.svg-node {
  left: 1px;
  top: 1px;
  width: 32px;
  height: 30px;
  display: block;
  position: relative;
  overflow: hidden;
}
</style>
