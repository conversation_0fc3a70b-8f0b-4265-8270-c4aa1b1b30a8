<template>
  <div class="logicflow-sidebar">
    <div>
      <h1 class="node-category-title">自定义节点</h1>
      <el-row class="node-category">
        <el-col
          :span="8"
          class="node-item"
          @mousedown="dragInNode('action-node')"
        >
          <IconRectRadius class="svg-node" />
        </el-col>
        <el-col
          :span="8"
          class="node-item"
          @mousedown="dragInNode('aggregation-node')"
        >
          <IconTriangle class="svg-node" />
        </el-col>
        <el-col
          :span="8"
          class="node-item"
          @mousedown="dragInNode('judge-node')"
        >
          <IconDiamond class="svg-node" />
        </el-col>
        <el-col
          :span="8"
          class="node-item"
          @mousedown="dragInNode('approval-node')"
        >
          <approval class="svg-node" />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import LogicFlow from "@logicflow/core";
import IconRectRadius from "@/views/playbook/playbookEdit/icon/RectRadius.vue";
import IconTriangle from "@/views/playbook/playbookEdit/icon/Triangle.vue";
import IconDiamond from "@/views/playbook/playbookEdit/icon/Diamond.vue";
import approval from "@/views/playbook/playbookEdit/icon/approval.vue";

const props = defineProps({
  lf: {
    type: LogicFlow
  }
});

const emit = defineEmits(["dragInNode"]);

const dragInNode = type => {
  emit("dragInNode", type);
};
</script>

<style lang="scss" scoped>
.logicflow-sidebar {
  user-select: none;
  border: 1px solid black;
  .node-category-title {
    margin: 0;
    font-size: 15px;
    display: block;
    border-bottom: 1px solid black;
    line-height: 30px;
    margin-bottom: 10px;
    padding-left: 10px;
  }
  .node-category {
    border-bottom: 1px solid black;
    .node-item {
      width: 70px;
      height: 70px;
      display: flex;
      .svg-node {
        left: 10px;
        top: 1px;
        width: 32px;
        height: 30px;
        display: block;
        position: relative;
        overflow: hidden;
        cursor: pointer;
      }
    }
  }
}
</style>
