<template>
  <el-dialog
    v-model="dialogVisible"
    :close-on-click-modal="false"
    :modal="true"
    class="custom-dialog"
    top="4vh"
    width="1400px"
    @close="handleClose"
  >
    <template #header>
      <div class="custom-dialog-header">任务管理 —— 执行记录</div>
    </template>
    <!-- default插槽内容区，main-content包裹所有内容 -->
    <template #default>
      <div v-loading="isLoading" class="main-content">
        <div class="main-row">
          <!-- 左侧选择列表 -->
          <div class="left-list">
            <el-scrollbar class="left-scrollbar">
              <!-- 空状态显示 -->
              <el-empty
                v-if="flatLogs.length === 0 && LogsTotal === 0"
                :image-size="80"
                description="暂无执行记录"
              />
              <!-- 有数据时显示列表 -->
              <template v-else>
                <div
                  v-for="log in uniqueRunLogs"
                  :key="log.run_id"
                  :class="{ 'active-log': log.run_id === currentRunId }"
                  class="name-block"
                  style="cursor: pointer"
                  @click="currentRunId = log.run_id"
                >
                  <span>{{ task.playbook ? "剧本" : "动作" }}</span>
                  {{ log.playbook_name }}&nbsp;&nbsp;&nbsp;
                  {{ `${formatDate(log.start_time)}` }}
                </div>
                <div style="text-align: center; margin: 12px 0">
                  <el-button
                    v-if="LogsPage < totalPages"
                    :loading="isLoadingMore"
                    plain
                    type="primary"
                    @click="loadMoreLogs"
                  >
                    加载更多
                  </el-button>
                  <span
                    v-else-if="flatLogs.length > 0"
                    style="color: #aaa; font-size: 13px"
                    >没有更多了</span
                  >
                </div>
              </template>
            </el-scrollbar>
          </div>
          <!-- 右侧详情 -->
          <div class="right-detail">
            <el-scrollbar class="right-scrollbar">
              <!-- 空状态显示 -->
              <el-empty
                v-if="flatLogs.length === 0 && LogsTotal === 0"
                :image-size="100"
                description="暂无执行记录详情"
              />
              <!-- 有数据时显示详情 -->
              <div v-else-if="flatLogs.length > 0">
                <!-- 信息展示：三列布局 -->
                <div class="info-row">
                  <div class="info-block">
                    <div class="info-label">开始时间</div>
                    <div class="info-value">
                      {{ formatDate(currentLog.start_time) }}
                    </div>
                  </div>
                  <div class="info-block">
                    <div class="info-label">耗时</div>
                    <div class="info-value">
                      {{
                        getDuration(currentLog.start_time, currentLog.end_time)
                      }}
                    </div>
                  </div>
                  <div class="info-block">
                    <div class="info-label">状态</div>
                    <div class="info-value">
                      <span
                        :class="getStatusClass(currentLog.status)"
                        class="status-tag"
                        >{{ getStatusText(currentLog.status) }}</span
                      >
                    </div>
                  </div>
                </div>
                <!-- <div class="section-title">
                  <span class="section-text">执行参数</span>
                  <span class="section-line" />
                </div>
                <div class="result-card">
                  <div class="card-body">
                    <template
                      v-if="
                        currentLog.args &&
                        Object.keys(currentLog.args).length > 0
                      "
                    >
                      <div class="input-params">
                        <json-viewer
                          :value="currentLog.args"
                          sort
                          style="background-color: #f5f7fa"
                        />
                      </div>
                    </template>
                    <template v-else> 无参数</template>
                  </div>
                </div> -->

                <div class="section-title">
                  <span class="section-text">执行结果</span>
                  <span class="section-line" />
                </div>
                <div class="logs-list">
                  <Runlog
                    :collapse-key="currentLog.run_id"
                    :default-open="true"
                    :items="getCurrentRunLogs()"
                    :show-summary="false"
                    :title="currentLog.playbook_name || '执行详情'"
                  />
                </div>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, defineEmits, defineProps, reactive, ref, watch } from "vue";
import { getTimedRunLog } from "@/api/timed";
import dayjs from "dayjs";
import { Runlog } from "@/components/Rerunlog";
import JsonViewer from "vue-json-viewer";
const props = defineProps({
  visible: Boolean,
  task: Object,
  taskList: Array // 传入所有任务列表
});
const emit = defineEmits(["update:visible"]);
const dialogVisible = ref(props.visible);
const isLoading = ref(false);

const selectedTask = ref(
  props.task || (props.taskList && props.taskList[0]) || {}
);

watch(
  () => props.visible,
  val => {
    dialogVisible.value = val;
    if (val) {
      TasksLogs();
    }
  }
);

// 日志数据
const LogsData = ref([]);
// 当前页
const LogsPage = ref(1);
// 每页数量
const LogsSize = ref(20);
// 总数量
const LogsTotal = ref(0);
// 当前日志id
const currentRunId = ref("");
// 平铺日志
const flatLogs = computed(() => {
  if (!LogsData.value?.data) return [];
  return LogsData.value.data.flat();
});
// 唯一日志
const uniqueRunLogs = computed(() => {
  // 优先取 action-node，没有就取第一个
  const map = new Map();
  for (const log of flatLogs.value) {
    if (!map.has(log.run_id)) {
      // 优先 action-node
      if (log.node_type === "action-node") {
        map.set(log.run_id, log);
      } else {
        map.set(log.run_id, log);
      }
    } else if (
      log.node_type === "action-node" &&
      map.get(log.run_id).node_type !== "action-node"
    ) {
      // 如果之前不是 action-node，现在是，则替换
      map.set(log.run_id, log);
    }
  }
  return Array.from(map.values());
});
// 当前日志
const currentLog = computed(
  () =>
    flatLogs.value.find(log => log.run_id === currentRunId.value) ||
    flatLogs.value[0] ||
    {}
);
// 加载更多
const isLoadingMore = ref(false);
// 总页数
const totalPages = computed(() => {
  if (!LogsTotal.value || !LogsSize.value) return 1;
  return Math.ceil(LogsTotal.value / LogsSize.value);
});
// 获取任务日志
const TasksLogs = async () => {
  isLoading.value = true;
  try {
    const res = await getTimedRunLog({
      id: props.task.id,
      size: LogsSize.value
    });
    if (res.code === 0) {
      LogsData.value = res.data;
      LogsPage.value = res.data.page;
      LogsSize.value = res.data.size;
      LogsTotal.value = res.data.total;
      if (flatLogs.value.length > 0) {
        currentRunId.value = flatLogs.value[0].run_id;
      }
    }
  } finally {
    isLoading.value = false;
  }
};

const handleClose = () => {
  emit("update:visible", false);
};
// 加载更多
const loadMoreLogs = async () => {
  if (isLoadingMore.value) return;
  if (LogsPage.value >= totalPages.value) return;
  isLoadingMore.value = true;
  const nextPage = LogsPage.value + 1;
  const res = await getTimedRunLog({
    id: props.task.id,
    page: nextPage,
    size: LogsSize.value
  });
  if (res.code === 0 && res.data?.data) {
    LogsData.value.data = LogsData.value.data.concat(res.data.data);
    LogsPage.value = nextPage;
  }
  isLoadingMore.value = false;
};

const formatDate = date => {
  return date ? dayjs(date).format("YYYY-MM-DD HH:mm:ss") : "-";
};

const getDuration = (start, end) => {
  if (start && end) {
    const durationMs = dayjs(end).diff(dayjs(start));
    const durationSeconds = durationMs / 1000;
    return `${durationSeconds} 秒`;
  }
  return "-";
};

const getStatusClass = status => {
  return status === 0 ? "success" : "fail";
};

const getStatusText = status => {
  return status === 0 ? "成功" : "失败";
};

const getResultOutput = result => {
  try {
    if (!result) return "暂无结果";
    const parsedResult = JSON.parse(result);
    return parsedResult.output || "暂无输出";
  } catch (error) {
    console.error("解析result失败:", error);
    return result || "暂无结果";
  }
};

const getCurrentRunLogs = () => {
  if (!currentRunId.value || !flatLogs.value.length) return [];

  // 获取当前run_id对应的所有日志记录
  const currentRunLogs = flatLogs.value.filter(
    log => log.run_id === currentRunId.value
  );

  // 节点排序：开始节点在第一个，结束节点在最后，动作节点在中间
  const sortOrder = { "start-node": 0, "action-node": 1, "end-node": 2 };

  return currentRunLogs.slice().sort((a, b) => {
    const orderA = sortOrder[a.node_type] ?? 99;
    const orderB = sortOrder[b.node_type] ?? 99;
    // 如果类型一样，按时间排序
    if (orderA === orderB) {
      const timeA = new Date(a.start_time || a.ctime || 0).getTime();
      const timeB = new Date(b.start_time || b.ctime || 0).getTime();
      return timeA - timeB;
    }
    return orderA - orderB;
  });
};
</script>

<style scoped>
.custom-dialog {
  height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

:deep(.el-dialog__body) {
  flex: 1 1 0%;
  display: flex;
  min-height: 0;
  height: 100%;
  padding: 0 !important; /* 避免多余内边距 */
}

.main-content {
  flex: 1 1 0%;
  display: flex;
  min-height: 0;
  height: 100%;
}

.main-row {
  flex: 1 1 0%;
  display: flex;
  min-height: 0;
  height: 700px;
  width: 100%;
  overflow: hidden;
}

.left-list,
.right-detail {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.left-list {
  width: 300px;
  background: #fafafa;
  border-right: 1px solid #e8e8e8;
}

.right-detail {
  flex: 1 1 0%;
  background: #fff;
}

.left-scrollbar,
.right-scrollbar {
  flex: 1 1 0%;
  min-height: 0;
  height: 100%;
  overflow: auto;
}

.right-scrollbar {
  padding: 16px;
}

/* 覆盖 el-dialog header 的 padding */
:deep(.el-dialog__header) {
  padding: 10px 16px !important;
  background: #f0f2f5;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.custom-dialog-header {
  font-size: 18px;
  font-weight: 500;
  color: #1890ff;
}

.name-block {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin: 8px 0;
  cursor: pointer;
  transition: background 0.3s;
}

.name-block:hover {
  background: #e6f7ff;
}

.name-block.active-log {
  background: #e6f7ff;
  font-weight: bold;
}

.name-block span {
  display: inline-block;
  width: 28px; /* 宽度增加以适应两个汉字 */
  height: 28px; /* 高度同步增加 */
  background: #1890ff;
  color: #fff;
  border-radius: 4px; /* 圆角矩形替代圆形 */
  text-align: center;
  line-height: 28px; /* 行高与高度匹配 */
  margin-right: 8px;
  font-size: 12px;
  white-space: nowrap;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.info-block {
  text-align: center;
  flex: 1;
}

.info-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.info-value {
  font-size: 16px;
  color: #333;
}

.status-tag {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  color: #fff;
}

.success {
  background: #52c41a;
}

.fail {
  background: #f5222d;
}

.section-title {
  display: flex;
  align-items: center;
  margin: 16px 0 8px;
  font-size: 16px;
  font-weight: 500;
  color: #1890ff;
}

.section-line {
  flex: 1;
  height: 2px;
  background: #1890ff;
  margin-left: 8px;
}

.logs-list {
  margin-top: 16px;
}

.result-card {
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #e8e8e8;
  font-size: 16px;
  font-weight: 500;
}

.card-type {
  color: #1890ff;
}

.card-status {
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
}

.card-body {
  padding: 16px;
}

.el-descriptions {
  margin-top: 8px;
}

.el-descriptions__label {
  font-weight: 500;
  color: #666;
}

.el-descriptions__content {
  color: #333;
}

.load-more-btn {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  background: #fafafa;
  z-index: 2;
  padding-bottom: 8px;
}

/* 空状态样式 */
:deep(.el-empty) {
  padding: 40px 20px;
}

:deep(.el-empty__description) {
  margin-top: 16px;
  color: #909399;
  font-size: 14px;
}
.input-params {
  max-height: 500px;
  overflow-y: auto;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
  font-family: "Fira Mono", "Consolas", monospace;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
