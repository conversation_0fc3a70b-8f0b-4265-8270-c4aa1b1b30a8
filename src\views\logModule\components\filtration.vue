<template>
  <div class="filtration-dialog-content">
    <div class="condition-title">条件过滤</div>
    <div class="condition-builder">
      <div class="conditions-container">
        <!-- 条件列表 -->
        <div v-if="conditions.length > 0" class="condition-section">
          <div
            v-for="(condition, index) in conditions"
            :key="index"
            class="condition-item"
          >
            <div class="condition-row">
              <!-- 只有 index > 0 时显示 connect_type 下拉 -->
              <el-select
                v-if="index > 0"
                v-model="condition.connect_type"
                class="condition-connect"
                placeholder="选择判断条件"
              >
                <el-option
                  v-for="option in getConnectTypes()"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
              <div v-else style="width: 100px" />

              <div class="condition-name">
                <el-select
                  v-model="condition.name"
                  placeholder="选择字段"
                  filterable
                >
                  <el-option
                    v-for="field in fields"
                    :key="field.value"
                    :label="field.label"
                    :value="field.value"
                  />
                </el-select>
              </div>

              <el-select
                v-model="condition.type"
                class="condition-type"
                placeholder="选择条件类型"
                @change="onConditionTypeChange(condition)"
              >
                <el-option
                  v-for="option in getConditionTypes()"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>

              <div class="condition-value">
                <el-input
                  v-model="condition.value"
                  :disabled="
                    condition.type === 'exists' || condition.type === '!exists'
                  "

                />
              </div>

              <el-button circle type="danger" @click="removeCondition(index)">
                <el-icon>
                  <Delete />
                </el-icon>
              </el-button>
            </div>
          </div>
        </div>
        <!-- 添加条件按钮 -->
        <div class="add-condition-wrapper">
          <el-button plain type="primary" @click="addCondition">
            <el-icon>
              <Plus />
            </el-icon>
            添加判断条件
          </el-button>
        </div>
      </div>
    </div>
    <div class="dialog-footer">
      <el-button @click="onCancel">取消</el-button>
      <el-button type="primary" @click="onConfirm">添加过滤器</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { Delete, Plus } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";

const emit = defineEmits(["confirm", "cancel"]);
const props = defineProps({
  fields: { type: Array, default: () => [] }
});

const conditions = ref([{ type: "", name: "", value: "" }]);

// 获取条件类型选项
const getConditionTypes = () => {
  return [
    { label: "存在", value: "exists" },
    { label: "不存在", value: "!exists" },
    { label: "等于", value: "==" },
    { label: "不等于", value: "!=" }
  ];
};

// 获取判断条件类型选项
const getConnectTypes = () => {
  return [
    { label: "且", value: "AND" },
    { label: "或", value: "OR" }
  ];
};

// 添加条件
const addCondition = () => {
  conditions.value.push({
    type: "",
    name: "",
    value: "",
    connect_type: "AND"
  });
};

// 移除条件
const removeCondition = index => {
  if (conditions.value.length > 1) {
    conditions.value.splice(index, 1);
    // 删除后只剩一条，确保第一条没有 connect_type
    if (
      conditions.value.length === 1 &&
      "connect_type" in conditions.value[0]
    ) {
      delete conditions.value[0].connect_type;
    }
  }
};

// 条件类型变化处理
const onConditionTypeChange = condition => {
  if (condition.type === "exists" || condition.type === "!exists") {
    condition.value = "";
  }
};

// 重置所有数据
const resetData = () => {
  conditions.value = [{ type: "", name: "", value: "" }];
};

const onConfirm = () => {
  // 验证条件
  for (const condition of conditions.value) {
    // 1. 校验字段必选
    if (!condition.name || String(condition.name).trim() === "") {
      ElMessage.error("字段名称必须选择");
      return;
    }
    // 2. 校验类型必选
    if (!condition.type || String(condition.type).trim() === "") {
      ElMessage.error("条件类型必须选择");
      return;
    }
    // 3. 除了exists和!exists，value必填
    if (
      condition.type !== "exists" &&
      condition.type !== "!exists" &&
      (condition.value === undefined ||
        condition.value === null ||
        String(condition.value).trim() === "")
    ) {
      ElMessage.error("条件参数不能为空");
      return;
    }
  }
  console.log(conditions.value);
  emit("confirm", {
    conditions: conditions.value
  });
};

// 取消时重置数据
const onCancel = () => {
  resetData();
  emit("cancel");
};
</script>

<style scoped>
.condition-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.filtration-dialog-content {
  padding: 8px 0 0 0;
}

.condition-builder {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  width: 100%;
}

.conditions-container {
  .add-condition-wrapper {
    margin-bottom: 20px;
    text-align: center;
  }

  .condition-section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    background-color: #fafafa;

    .condition-item {
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }

      .condition-row {
        display: flex;
        gap: 10px;
        align-items: center;

        .condition-connect {
          width: 100px;
          flex-shrink: 0;
        }

        .condition-type {
          width: 160px;
          flex-shrink: 0;
        }

        .condition-name,
        .condition-value {
          flex: 1;
          min-width: 0;
        }
      }
    }
  }
}

.dialog-footer {
  margin-top: 32px;
  text-align: right;
}
</style>
