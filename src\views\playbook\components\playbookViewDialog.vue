<template>
  <div>
    <!-- 流程图预览dialog -->
    <el-dialog
      v-model="isShowViewPlaybook"
      style="width: 1500px; height: 750px"
    >
      <template #header>
        <div>预览流程图</div>
      </template>
      <div class="view-div">
        <div ref="viewLogicflowRef" class="view-logicflow" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { apiGetPlaybookVersionsList } from "@/api/playbook";
import LogicFlow from "@logicflow/core";
import { computed, ref } from "vue";
import { registerCustomElement } from "../playbookEdit/node";

//使用v-model约定命名
const props = defineProps({
  modelValue: { type: Boolean, default: false }, // 固定为modelValue
  SSEResult: { type: Object, default: () => ({}) }
});

const emit = defineEmits(["update:modelValue"]);

const lf = ref<LogicFlow>();
const viewLogicflowRef = ref();
const playbookDetail = ref();
const isShowViewPlaybook = computed({
  get: () => props.modelValue,
  set: val => emit("update:modelValue", val)
});

//初始数据
let data = {
  nodes: [
    {
      type: "start-node",
      x: 100,
      y: 100
    },
    {
      type: "end-node",
      x: 1000,
      y: 100
    }
  ]
};

//打开预览流程图dialog并渲染流程图数据
const viewLogicflowData = row => {
  playbookDetail.value = [];
  initLogicFlow(row);
  isShowViewPlaybook.value = true;
};

//渲染流程图数据
const initLogicFlow = async row => {
  console.log(row.flow_id);
  //获取剧本的详细信息
  if (row.flow_id) {
    let res: any = await apiGetPlaybookVersionsList({
      playbook_id: row.flow_id
    });
    const result = res.data.find(item => item.version_id === row.version_id);
    playbookDetail.value = result;
    console.log(playbookDetail.value);
  }

  lf.value = new LogicFlow({
    container: viewLogicflowRef.value,
    width: 1450,
    height: 650,
    grid: {
      visible: false,
      size: 15
    },
    isSilentMode: true //静默模式
  });
  registerCustomElement(lf.value); //注册自定义节点和边
  lf.value.setDefaultEdgeType("vue-edge"); //边的类型
  //如果是已有剧本，则渲染已有剧本的流程图信息，否则默认为初始数据
  if (Object.entries(playbookDetail.value.flow_json).length > 0) {
    playbookDetail.value.flow_json.nodes.forEach(node => {
      // //基于锚点的位置更新边的路径
      // lf.value.getNodeModelById(node.id).updateField();
      //在渲染流程图之前，先把初始化isWebSocket和scale的值
      if (node.properties && node.properties.isWebSocket !== undefined) {
        node.properties.isWebSocket = false;
        node.properties.scale = 1;
      }
    });
    lf.value.render(playbookDetail.value.flow_json);
  } else {
    lf.value.render(data);
  }
  lf.value.translateCenter(); //将图形移动到画布中央
};

//对外暴露方法
defineExpose({
  viewLogicflowData
});
</script>

<style lang="scss" scoped></style>
