<script lang="ts" setup>
import { ref } from "vue";
import { message } from "@/utils/message";
import { ReQrcode } from "@/components/ReQrcode";
import { editUserMfa } from "@/api/userList";
import { useColumns } from "../columns";

const { fetchUserData } = useColumns();

const visible = ref(false);
const qrcodeText = ref("");
const currentUser = ref<any>(null);

// 打开弹窗
const openDialog = (row: any) => {
  currentUser.value = row;
  // 这里应该调用后端接口获取二维码内容
  qrcodeText.value = "这里应该是后端返回的二维码内容"; // 示例内容
  visible.value = true;
};

// 确认开启多因素认证
const handleConfirm = () => {
  editUserMfa({
    username: currentUser.value?.username,
    mfa_enable: true
  })
    .then((res: any) => {
      if (res.code === 0) {
        message("多因素认证开启成功", { type: "success" });
        visible.value = false;
        fetchUserData(1, 15, "");
      } else {
        message(res.message || "开启失败", { type: "error" });
      }
    })
    .catch(() => {
      message("开启失败", { type: "error" });
    });
};

defineExpose({
  openDialog
});
</script>

<template>
  <el-dialog v-model="visible" title="多因素认证" width="400px">
    <div class="text-center">
      <p class="mb-4">请使用 Google Authenticator 扫描以下二维码</p>
      <ReQrcode
        :options="{
          color: {
            dark: '#55D187',
            light: '#ffffff'
          }
        }"
        :text="qrcodeText"
        :width="200"
      />
      <p class="mt-4 text-gray-500">
        扫描完成后，请在手机上确认，然后点击确定按钮
      </p>
    </div>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </template>
  </el-dialog>
</template>
