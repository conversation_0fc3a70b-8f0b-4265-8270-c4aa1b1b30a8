import type LogicFlow from "@logicflow/core";
import { h, render } from "vue";
import ContextMenu from "./contextMenu.vue";

const COMMON_TYPE_KEY = "menu-common";
const NEXT_X_DISTANCE = 300;
const NEXT_Y_DISTANCE = 120;

class ContextPad {
  static pluginName: string;
  menuTypeMap: Map<any, any>;
  lf: LogicFlow;
  __menuDOM: HTMLDivElement;
  container: any;
  _activeData: any;
  isShow: any;

  constructor({ lf }) {
    this.menuTypeMap = new Map();
    this.lf = lf;
    this.__menuDOM = document.createElement("div");
    this.__menuDOM.className = "lf-inner-context";
    this.menuTypeMap.set(COMMON_TYPE_KEY, []);
  }

  render(lf, container) {
    this.container = container;
    lf.on("node:click", ({ data }) => {
      this._activeData = data;
      // 循环体节点不显示右键菜单，因为已经有内置的删除按钮
      if (data.type !== "loop-body" && data.type !== "end-node") {
        this.createContextMenu();
      }
    });
    lf.on("edge:click", ({ data }) => {
      this._activeData = data;
      this.createEdgeContextMenu();
    });
    lf.on("blank:click", () => {
      this.hideContextMenu();
    });
  }

  //根据节点类型添加快捷菜单
  setContextMenuByType = (type, menus) => {
    this.menuTypeMap.set(type, menus);
  };

  /**
   * 隐藏菜单
   */
  hideContextMenu() {
    this.__menuDOM.innerHTML = "";
    this.__menuDOM.style.display = "none";
    if (this.isShow) {
      this.container.removeChild(this.__menuDOM);
    }
    this.lf.off(
      "node:delete,edge:delete,node:drag,graph:transform",
      this.listenDelete
    );
    //菜单隐藏时,恢复缩放功能
    this.lf.updateEditConfig({
      stopZoomGraph: false
    });
    this.isShow = false;
  }

  /**
   * 显示指定元素菜单
   * @param data 节点id、节点类型、菜单位置
   */
  showContextMenu(data) {
    if (!data || !data.id) {
      console.warn("请检查传入的参数");
      return;
    }
    this._activeData = data;
    //显示菜单后创建菜单
    this.createContextMenu();
  }

  //看不懂
  setContextMenuItems(menus) {
    this.menuTypeMap.set(COMMON_TYPE_KEY, menus);
  }

  /**
   * 获取新菜单位置
   */
  getContextMenuPosition() {
    const data = this._activeData;
    console.log("_activeData:", this._activeData);
    const Model = this.lf.graphModel.getElement(data.id);
    if (!Model) {
      console.warn(`找不到元素${data.id}`);
      return;
    }
    let x;
    let y;
    //获取边的X和Y
    if (Model.BaseType === "edge") {
      x = Number.MIN_SAFE_INTEGER;
      y = Number.MAX_SAFE_INTEGER;
      const edgeData = Model.getData();
      x = Math.max(edgeData.startPoint.x, x);
      y = Math.min(edgeData.startPoint.y, y);
      x = Math.max(edgeData.endPoint.x, x);
      y = Math.min(edgeData.endPoint.y, y);
      console.log(x, y);
      if (edgeData.pointsList) {
        edgeData.pointsList.forEach(point => {
          x = Math.max(point.x, x);
          y = Math.min(point.y, y);
        });
        console.log(x, y);
      }
    }
    //获取节点的X和Y
    if (Model.BaseType === "node") {
      console.log("node:", Model);
      x = data.x + Model.width / 2;
      y = data.y - 250; // 在节点上方显示菜单
      console.log(x, y);
    }
    return this.lf.graphModel.transformModel.CanvasPointToHtmlPoint([x, y]);
  }

  //创建菜单
  createContextMenu() {
    const { isSilentMode } = this.lf.options;
    // 静默模式不显示菜单
    if (isSilentMode) {
      return;
    }
    let items = this.menuTypeMap.get(this._activeData.type) || [];
    console.log("items:", items);
    items = items.concat(this.menuTypeMap.get(COMMON_TYPE_KEY));
    console.log("items:", items);

    // 清空旧的菜单内容
    this.__menuDOM.innerHTML = "";

    // 使用 Vue 的 h 函数创建你的组件的虚拟节点
    const vnode = h(ContextMenu, {
      items,
      lf: this.lf,
      activeData: this._activeData,
      menuDOM: this.__menuDOM,
      addNode: this.addNode,
      hideContextMenu: this.hideContextMenu
    });

    // 将虚拟节点挂载到 DOM 中
    const container = document.createElement("div");
    render(vnode, container);
    this.__menuDOM.appendChild(container);

    this.showMenu();
  }

  //边的删除按钮
  createEdgeContextMenu() {
    const { isSilentMode } = this.lf.options;
    // 静默模式不显示菜单
    if (isSilentMode) {
      return;
    }
    let items = this.menuTypeMap.get(this._activeData.type) || [];
    items = items.concat(this.menuTypeMap.get(COMMON_TYPE_KEY));
    const menus = document.createDocumentFragment();
    items.forEach(item => {
      const menuItem = document.createElement("div");
      menuItem.className = "lf-context-item";
      const img = document.createElement("img");
      img.src = item.icon;
      img.className = "lf-context-img";
      if (item.className) {
        menuItem.className = `${menuItem.className} ${item.className}`;
      }
      img.addEventListener("click", () => {
        this.hideContextMenu();
        if (item.callback) {
          item.callback(this._activeData);
        } else {
          this.addNode({
            sourceId: this._activeData.id,
            x: this._activeData.x,
            y: this._activeData.y,
            properties: item.properties,
            type: item.type
          });
        }
      });
      menuItem.appendChild(img);
      menus.appendChild(menuItem);
    });
    this.__menuDOM.innerHTML = "";
    this.__menuDOM.appendChild(menus);
    this.showMenu();
  }

  addNode(node, y?) {
    const isDeep = y !== undefined;
    if (y === undefined) {
      y = node.y;
    }
    const nodeModel = this.lf.getNodeModelById(node.sourceId);
    const leftTopX = node.x - 100 + NEXT_X_DISTANCE;
    const leftTopY = y - node.y / 2 - 20;
    const rightBottomX = node.x + 100 + NEXT_X_DISTANCE;
    const rightBottomY = y + node.y / 2 + 20;
    const existElements = this.lf.getAreaElement(
      [leftTopX, leftTopY],
      [rightBottomX, rightBottomY]
    );
    console.log(existElements);
    if (existElements.length) {
      y = y + NEXT_Y_DISTANCE;
      this.addNode(node, y);
      return;
    }
    const newNode = this.lf.addNode({
      type: node.type,
      x: node.x + 300,
      y,
      properties: node.properties
    });
    let startPoint;
    let endPoint;
    if (isDeep) {
      startPoint = {
        x: node.x + nodeModel.width / 2,
        y: node.y
      };
      endPoint = {
        x: newNode.x - newNode.width / 2,
        y: newNode.y
      };
    }
    //连接新旧节点
    this.lf.addEdge({
      sourceNodeId: node.sourceId,
      targetNodeId: newNode.id,
      startPoint,
      endPoint
    });
  }

  showMenu() {
    const [x, y] = this.getContextMenuPosition();
    const data = this._activeData;
    const Model = this.lf.graphModel.getElement(data.id);
    //获取边的X和Y
    if (Model.BaseType === "edge") {
      this.__menuDOM.style.display = "flex";
      this.__menuDOM.style.top = `${y - 10}px`;
      this.__menuDOM.style.left = `${x - 10}px`;
    }
    if (Model.BaseType === "node") {
      this.__menuDOM.style.display = "flex";
      this.__menuDOM.style.top = `${y}px`;
      this.__menuDOM.style.left = `${x + 10}px`;
      this.__menuDOM.style.transform = ""; // 清除transform
    }
    this.container.appendChild(this.__menuDOM);
    // 菜单显示的时候，监听删除，同时隐藏
    !this.isShow &&
      this.lf.on(
        "node:delete,edge:delete,node:drag,graph:transform",
        this.listenDelete
      );
    this.isShow = true;
  }

  listenDelete = () => {
    this.hideContextMenu();
  };
}

ContextPad.pluginName = "contextPad";

export { ContextPad };
