<script setup>
import { onMounted, ref } from "vue";
import { Search } from "@element-plus/icons-vue";
import {
  ApiUser,
  fetchApiUserList,
  handleClearSearch,
  handleCurrentChange,
  handleSearch,
  handleSizeChange,
  input1,
  Page,
  size,
  totals
} from "./apiUser";
import apiUserCreate from "./components/apiUser_create.vue";
import { deleteApiUser } from "@/api/userList";
import { ElMessage } from "element-plus";
import { Icon } from "@iconify/vue";

const apiUserCreateRef = ref(null);

// 初始化加载数据
onMounted(() => {
  fetchApiUserList();
});

// 新建用户功能
const handleNewUser = () => {
  apiUserCreateRef.value.open();
};

// 删除用户功能
const handleDelete = async row => {
  const res = await deleteApiUser(row);
  if (res.code === 0) {
    ElMessage.success("删除成功");
    fetchApiUserList();
  } else {
    ElMessage.error("删除失败");
  }
};
</script>

<template>
  <div class="api-user-container">
    <div class="mb-[20px] flex items-center justify-between">
      <div>
        <!-- 搜索区域 -->
        <div class="search-area">
          <el-input
            v-model="input1"
            clearable

            style="width: 340px"
            @clear="handleClearSearch"
          >
            <template #append>
              <el-button @click="handleSearch">
                <el-icon>
                  <Search />
                </el-icon>
              </el-button>
            </template>
          </el-input>
        </div>
      </div>
      <Perms :value="['user:c']">
        <div class="flex items-center gap-4">
          <el-button type="primary" @click="handleNewUser">新建</el-button>
        </div>
      </Perms>
    </div>

    <!-- API账号表格 -->
    <el-table :data="ApiUser" border style="width: 100%">
      <el-table-column label="名称" prop="username" />
      <el-table-column label="角色" prop="roles" />
      <el-table-column label="描述" prop="description" />
      <el-table-column label="创建者" prop="creator" />
      <el-table-column label="创建时间" prop="ctime" sortable />
      <el-table-column label="过期时间" prop="expiration" sortable />
      <Perms :value="['user:d']">
        <el-table-column label="操作" prop="action" width="100">
          <template #default="scope">
            <el-tooltip :hide-after="0" content="删除" placement="top">
              <span>
                <el-popconfirm
                  cancel-button-text="取消"
                  confirm-button-text="确认"
                  title="确认要删除这个账号吗？"
                  @confirm="handleDelete(scope.row)"
                >
                  <template #reference>
                    <el-button link type="danger">
                      <Icon height="18" icon="uiw:delete" width="18" />
                    </el-button>
                  </template>
                </el-popconfirm>
              </span>
            </el-tooltip>
          </template>
        </el-table-column>
      </Perms>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="Page"
        v-model:page-size="size"
        v-model:total="totals"
        :background="true"
        :page-sizes="[15, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>
    <apiUserCreate ref="apiUserCreateRef" @success="fetchApiUserList" />
  </div>
</template>

<style lang="scss" scoped>
.el-input {
  margin-bottom: 0;
}

.pagination-container {
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .pagination-info {
    color: #606266;
  }
}
</style>
