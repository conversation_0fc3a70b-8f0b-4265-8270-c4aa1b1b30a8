<template>
  <el-menu
    active-text-color="#3498db"
    background-color="#2c3e50"
    class="side-menu"
    router
    text-color="#ecf0f1"
  >
    <div class="room-header">
      <div class="title">
        <IconifyIconOffline
          class="icon"
          height="20px"
          icon="icon-park-outline:computer"
          width="20px"
        />
        <el-text class="onlineTag">作战室</el-text>
      </div>
      <div class="room-actions">
        <Perms :value="['warroom:r']">
          <el-tooltip
            :hide-after="0"
            :show-after="50"
            content="搜索作战室"
            effect="light"
            placement="bottom"
          >
            <el-button circle size="small" @click="showSearchDialog">
              <IconifyIconOffline
                class="icon"
                height="15px"
                icon="ri:search-line"
                width="15px"
              />
            </el-button>
          </el-tooltip>
        </Perms>
        <Perms :value="['warroom:c']">
          <el-tooltip
            :hide-after="0"
            :show-after="50"
            content="新建作战室"
            effect="light"
            placement="bottom"
          >
            <el-button circle size="small" @click="showAddRoomDialog">
              <IconifyIconOffline
                class="icon"
                height="15px"
                icon="icon-park-outline:plus"
                width="15px"
              />
            </el-button>
          </el-tooltip>
        </Perms>
      </div>
    </div>
    <el-scrollbar height="calc(100vh - 150px)">
      <div class="room-list-container">
        <el-menu-item
          v-for="room in filteredRooms"
          :key="room.id"
          :class="{
            current: room.id === currentRoomId
          }"
          class="menu-item"
          @click="handleRoomClick(room)"
        >
          <span class="menu-text ellipsis">{{ room.name }}</span>
          <Perms :value="['warroom:u']">
            <el-tooltip
              :hide-after="0"
              content="取消关注"
              effect="dark"
              placement="bottom"
            >
              <el-button
                circle
                class="exit-room-btn"
                link
                size="small"
                @click.stop="handleUnstarRoom(room)"
              >
                <IconifyIconOffline
                  class="icon"
                  color="#fff"
                  height="20px"
                  icon="hugeicons:star-off"
                  width="20px"
                />
              </el-button>
            </el-tooltip>
          </Perms>
        </el-menu-item>
      </div>
    </el-scrollbar>
  </el-menu>
  <el-dialog
    v-model="searchDialogVisible"
    :append-to-body="true"
    :modal="true"
    title="搜索作战室"
    width="30%"
  >
    <el-input
      v-model="searchQuery"
      clearable
      placeholder="输入作战室名称"
      @keyup.enter="handleSearch"
    />
    <div v-if="searchResults.length > 0" class="search-results">
      <div
        v-for="room in searchResults"
        :key="room.id"
        class="search-result-item"
        @click="goToRoom(room.id)"
      >
        {{ room.name }}
        <el-button link type="primary" @click="starRoom(room.id)"
          >关注
        </el-button>
      </div>
    </div>
    <template #footer>
      <el-button @click="searchDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleSearch">搜索</el-button>
    </template>
  </el-dialog>

  <el-dialog
    v-model="addRoomDialogVisible"
    :append-to-body="true"
    :modal="true"
    class="add-room-dialog"
    title="添加新作战室"
    width="40%"
  >
    <el-form :model="newRoomForm" @submit.prevent>
      <el-form-item label="作战室名称">
        <el-input
          v-model="newRoomForm.name"

          @keyup.enter="addRoom"
        />
      </el-form-item>
      <el-form-item label="邀请用户">
        <el-select
          ref="userSelectRef"
          v-model="newRoomForm.invitedUsers"
          :popper-options="{
            modifiers: [
              {
                name: 'offset',
                options: {
                  offset: [0, 8]
                }
              }
            ]
          }"
          filterable
          multiple
          placeholder="选择要邀请的用户，支持模糊查询"
          popper-class="invite-select-dropdown"
          popper-placement="top-start"
          style="width: 100%"
          @visible-change="handleSelectVisibleChange"
        >
          <el-option
            v-for="user in allUsers"
            :key="user.id"
            :label="`${user.display_name} (${user.username})`"
            :value="user.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="addRoomDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="addRoom">确定</el-button>
    </template>
  </el-dialog>
  <el-dialog
    v-model="joinRoomDialogVisible"
    :append-to-body="true"
    :modal="true"
    title="加入作战室"
    width="30%"
  >
    <p>确定要加入作战室- "{{ selectedRoom?.name }}" 吗？</p>
    <template #footer>
      <el-button @click="joinRoomDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="confirmJoinRoom">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import { useUserStore } from "@/store/modules/user";
import { useChatStore } from "@/store/warChat";
import { ElMessage } from "element-plus";
import { type Room } from "@/directives/warroom/warChat";
import { storeToRefs } from "pinia";
import {
  createRoom,
  getRoomsList,
  joinRoom,
  StarRooms,
  unstarRoom
} from "@/api/warroom";
import { apiuserAccountList } from "@/api/userList";

import IconifyIconOffline from "@/components/ReIcon/src/iconifyIconOffline";

interface NewRoomForm {
  name: string;
  invitedUsers: string[];
}

// 添加isFloating属性
const props = defineProps({
  ws: Object,
  rooms: Array,
  userRooms: Array,
  isFloating: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(["roomChanged"]);

const newRoomForm = ref<NewRoomForm>({
  name: "",
  invitedUsers: []
});

const chatStore = useChatStore();
const {
  rooms: storeRooms,
  currentRoomId,
  ws: storeWs
} = storeToRefs(chatStore);

const router = useRouter();
const userStore = useUserStore();
const addRoomDialogVisible = ref(false);
const searchDialogVisible = ref(false);
const searchQuery = ref("");
const searchResults = ref<Room[]>([]);
const joinRoomDialogVisible = ref(false);
const selectedRoom = ref<Room | null>(null);
const allUsers = ref<any[]>([]); // 所有用户列表
const hasLoadedRooms = ref(false);
const isFetchingRooms = ref(false);
// 新建作战室时的用户相关
const userTotalCount = ref(0);
const isLoadingUsers = ref(false);
const userSelectRef = ref<any>(null);
const dropdownVisible = ref(false);

const handleRoomClick = async (room: Room) => {
  if (!storeWs.value || storeWs.value.readyState !== WebSocket.OPEN) {
    chatStore.connectWebSocket();
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  if (chatStore.isUserInRoom(room.id)) {
    if (props.isFloating) {
      // 悬浮窗模式下，使用emit直接通知父组件
      emit("roomChanged", room.id);
    } else {
      // 普通模式下，使用chatStore的方法（内部会使用路由导航）
      await chatStore.handleRoomChanged(room.id);
    }
  } else {
    selectedRoom.value = room;
    joinRoomDialogVisible.value = true;
  }
};

const confirmJoinRoom = async () => {
  if (!selectedRoom.value) return;
  try {
    const response = (await joinRoom({
      id: selectedRoom.value.id
    })) as any;

    if (response.code === 0) {
      await chatStore.fetchRooms();
      joinRoomDialogVisible.value = false;

      ElMessage.success({
        message: "成功加入作战室",
        duration: 1000
      });
      if (props.isFloating) {
        // 悬浮窗模式下使用emit
        emit("roomChanged", selectedRoom.value.id);
      } else {
        // 普通模式下使用路由导航
        await chatStore.handleRoomChanged(selectedRoom.value.id);
      }
    } else {
      ElMessage.error(response.message || "加入作战室失败");
    }
  } catch (error) {
    ElMessage.error("加入作战室失败");
  }
};

const showSearchDialog = () => {
  searchQuery.value = "";
  searchResults.value = [];
  searchDialogVisible.value = true;
};

const handleSearch = async () => {
  if (!searchQuery.value.trim()) {
    searchResults.value = [];
    return;
  }
  const res = (await getRoomsList({ keyword: searchQuery.value })) as any;
  if (res.code === 0) {
    searchResults.value = res.data.rooms;
  }
};

const goToRoom = (roomId: string) => {
  if (props.isFloating) {
    emit("roomChanged", roomId);
  } else {
    if (router.currentRoute.value.params.roomId !== roomId) {
      router.push(`warroom/index/${roomId}`);
    }
  }
  searchDialogVisible.value = false;
};
// 关注作战室
const starRoom = async (room: string) => {
  const StarRes = (await StarRooms({ id: room })) as any;
  if (StarRes.code === 0) {
    await chatStore.fetchRooms();
    ElMessage.success({
      message: "已关注作战室！",
      duration: 800
    });
  }
};

// 退出作战室
const handleUnstarRoom = async room => {
  try {
    const response = (await unstarRoom({ id: room.id })) as any;
    if (response.code === 0) {
      await chatStore.fetchRooms();
      ElMessage.success({
        message: "已取关该作战室！",
        duration: 800
      });
    }
  } catch {
    // 用户点击取消，不做任何事
  }
};
const showAddRoomDialog = async () => {
  newRoomForm.value = {
    name: "",
    invitedUsers: [] as string[]
  };

  allUsers.value = [];
  await loadUserList();
  addRoomDialogVisible.value = true;
};

// 加载用户列表的方法
const loadUserList = async () => {
  isLoadingUsers.value = true;
  try {
    // 创建作战时获取的邀请用户列表调用的是user模块的api,这里写死100条,可做分页处理
    const response = (await apiuserAccountList({ size: 100 })) as any;
    if (response.code === 0 && response.data.users) {
      allUsers.value = response.data.users.filter(
        (user: any) => user.id !== userStore.$state.id
      );
      userTotalCount.value = response.data.total || 0;
    } else {
      ElMessage.error({
        message: "获取邀请用户列表失败",
        duration: 1000
      });
    }
  } catch (error) {
    ElMessage.error({
      message: "获取邀请用户列表失败",
      duration: 1000
    });
  } finally {
    isLoadingUsers.value = false;
  }
};

// 处理下拉框可见性变化
const handleSelectVisibleChange = (visible: boolean) => {
  dropdownVisible.value = visible;
};

const addRoom = async () => {
  if (!newRoomForm.value.name.trim()) {
    return;
  }

  try {
    const response = (await createRoom({
      name: newRoomForm.value.name,
      members: newRoomForm.value.invitedUsers
    })) as any;

    if (response.code === 0 && response.data) {
      // 添加新创建的房间到本地状态
      const newRoom = {
        id: response.data.room_id,
        name: response.data.name,
        creator_id: userStore.$state.id,
        creator_name: userStore.$state.username
      };

      // 重新获取房间列表和用户房间列表
      await chatStore.fetchRooms();

      addRoomDialogVisible.value = false;

      ElMessage.success({
        message: "作战室创建成功",
        duration: 1000
      });

      await chatStore.handleRoomChanged(newRoom.id);
    } else {
      ElMessage.error(response.message || "创建作战室失败");
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      ElMessage.error(`创建作战室失败: ${error.message}`);
    } else {
      ElMessage.error("创建作战室失败");
    }
  }
};
onMounted(() => {
  if (
    !hasLoadedRooms.value &&
    storeRooms.value.length === 0 &&
    !isFetchingRooms.value
  ) {
    isFetchingRooms.value = true;
    chatStore.fetchRooms().finally(() => {
      hasLoadedRooms.value = true;
      isFetchingRooms.value = false;
    });
  }
});
const filteredRooms = computed(() => {
  // 过滤出非私聊房间
  const nonPrivateRooms = storeRooms.value.filter(
    room => !room.name.startsWith("私聊_")
  );
  return nonPrivateRooms;
});
</script>
<style scoped>
.side-menu {
  height: 100%;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
  border: none;
}

.room-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  color: #ecf0f1;
  font-weight: bold;
  margin: 0;
}

.room-list-container {
  overflow-y: auto;
  max-height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
}

.side-menu .el-menu-item {
  background-color: #2c3e50 !important;
  margin: 0px 12px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.side-menu .el-menu-item:hover {
  background-color: rgba(52, 152, 219, 0.1) !important;
  transform: translateX(5px);
}

.side-menu .el-menu-item.is-active {
  background-color: rgba(52, 152, 219, 0.2) !important;
  box-shadow: 0 4px 8px rgba(52, 152, 219, 0.2);
}

.menu-icon {
  font-size: 20px;
  margin-right: 12px;
  color: #ecf0f1;
}

.menu-text {
  font-size: 13px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.menu-item.current {
  background-color: rgba(51, 182, 209, 0.1) !important;
  cursor: default;
}

.menu-text.current {
  font-size: 25px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.menu-item.current:hover {
  transform: none !important;
  background-color: rgba(255, 193, 7, 0.1) !important;
}

.title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: bold;
}

.onlineTag {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.room-actions {
  display: flex;
  margin: 6px;
}

.search-results {
  margin-top: 16px;
  max-height: 300px;
  /* overflow: auto; */
  overflow: hidden;
}

.search-result-item {
  padding: 12px;
  margin: 4px 0;
  border-radius: 4px;
  background-color: #f5f5f5;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-result-item:hover {
  background-color: #e0e0e0;
  transform: translateX(2px);
}

.el-scrollbar {
  height: calc(100vh - 150px);
  margin: 0;
  padding: 0;
}

.el-dialog {
  z-index: 3002;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  margin-bottom: 10px;
}

.invite-select-dropdown {
  margin-bottom: 10px;
}

.menu-text.ellipsis {
  display: inline-block;
  max-width: 220px; /* 你可以根据侧栏实际宽度再调大 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
}

.exit-room-btn {
  margin-left: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 覆盖 el-menu-item 里的 el-icon 右边距 */
:deep(.el-menu-item .el-icon) {
  margin-right: 0 !important;
}
</style>
