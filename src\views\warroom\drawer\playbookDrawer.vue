<template>
  <el-drawer
    v-model="visible"
    :with-header="false"
    direction="ltr"
    size="25%"
    @close="handleClose"
  >
    <div class="drawer-flex">
      <!-- 左侧：剧本列表 -->
      <div class="drawer-left">
        <div class="drawer-left-header">
          <span style="font-size: 16px; font-weight: bold">选择剧本</span>
          <span class="custom-close-btn" @click="handleClose">×</span>
        </div>
        <!-- 搜索框 -->
        <div class="search-area">
          <el-input
            v-model="searchKey"

            style="width: 100%"
            @input="onSearch"
          >
            <template #append>
              <el-button @click="onSearch">
                <el-icon>
                  <Search />
                </el-icon>
              </el-button>
            </template>
          </el-input>
        </div>
        <!-- 剧本列表 -->
        <div v-if="playbookList.length">
          <div
            v-for="item in playbookList"
            :key="item.id"
            :class="{ 'playbook-item-hover': hoverId === item.id }"
            class="playbook-item"
            @mouseenter="hoverId = item.id"
            @mouseleave="hoverId = null"
          >
            <div class="playbook-title">{{ item.name }}</div>
            <div class="playbook-actions">
              <a href="javascript:" @click="openPlaybookVersionsDialog(item)"
                >选择</a
              >
            </div>
          </div>
        </div>
        <div v-else style="text-align: center; color: #aaa; padding: 20px">
          暂无剧本
        </div>
        <div class="pagination-fixed">
          <el-pagination
            v-model:current-page="playbookPage"
            v-model:page-size="playbookSize"
            :page-sizes="[50]"
            :pager-count="5"
            :total="playbookTotal"
            background
            layout="total, prev, pager, next"
            size="small"
            @size-change="onPlaybookSizeChange"
            @current-change="onPlaybookPageChange"
          />
        </div>
      </div>
    </div>
  </el-drawer>

  <el-dialog v-model="isShowPlaybookVersions" width="1300px">
    <template #header>
      <div>剧本历史版本</div>
    </template>
    <div style="display: flex; flex-direction: column; max-height: 70vh">
      <div class="playbook-versions-name">
        <el-tag>{{ selectedPlaybookName }}</el-tag>
      </div>
      <el-scrollbar style="flex: 1; max-height: 60vh">
        <el-table :data="playbookVersionsTableData" border height="55vh">
          <el-table-column
            align="center"
            label="版本"
            prop="version"
            width="auto"
          >
            <template #default="{ row }">
              <el-button
                link
                type="primary"
                @click="
                  toDetail(
                    {
                      flow_id: row.flow_id,
                      version_id: row.version_id,
                      status: row.status
                    },
                    'params'
                  )
                "
                >{{ row.version }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            label="状态"
            prop="status"
            width="100px"
          >
            <template #default="scope">
              <el-tag :type="scope.row.status == '1' ? 'success' : 'primary'">
                {{ scope.row.status == "1" ? "已发布" : "草稿" }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            :formatter="updatorFormatter"
            align="center"
            label="更新人"
            prop="updator"
            width="150px"
          />
          <el-table-column
            align="center"
            label="更新时间"
            prop="utime"
            width="auto"
          />
          <el-table-column
            :formatter="creatorFormatter"
            align="center"
            label="创建人"
            prop="creator"
            width="150px"
          />
          <el-table-column
            align="center"
            label="创建时间"
            prop="ctime"
            width="auto"
          />
          <el-table-column align="center" label="操作" width="auto">
            <template #default="{ row }">
              <div>
                <el-tooltip :hide-after="0" content="预览" placement="top">
                  <el-button
                    link
                    type="primary"
                    @click="viewLogicflowData(row)"
                  >
                    <IconifyIconOffline
                      height="20"
                      icon="icon-park-outline:preview-open"
                      width="20"
                    />
                  </el-button>
                </el-tooltip>
                <el-tooltip :hide-after="0" content="执行" placement="top">
                  <el-button link type="primary" @click="playRun(row)">
                    <IconifyIconOffline
                      height="20"
                      icon="qlementine-icons:run-debug-16"
                      width="20"
                    />
                  </el-button>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-scrollbar>
    </div>
  </el-dialog>
  <!-- 剧本流程图DOM -->
  <el-dialog v-model="isShowViewPlaybook" style="width: 1500px; height: 750px">
    <template #header>
      <div>预览流程图</div>
    </template>
    <div class="view-div">
      <div ref="viewLogicflowRef" class="view-logicflow" />
    </div>
  </el-dialog>
  <!-- 剧本参数 -->
  <el-dialog v-model="isShowPlayParamDialog" title="剧本参数配置" width="500px">
    <el-form
      ref="playParamFormRef"
      :model="playParamForm"
      :rules="playParamRules"
    >
      <template v-for="(item, key) in playInputData" :key="key">
        <el-form-item
          :prop="Object.keys(item)[0]"
          :required="item[Object.keys(item)[0]].input.required"
        >
          <template #label>
            <div style="display: flex; align-items: center; gap: 6px">
              <span
                >{{
                  item[Object.keys(item)[0]].input.description ||
                  Object.keys(item)[0]
                }}：</span
              >
              <el-tooltip
                :content="`键：${Object.keys(item)[0]}`"
                :hide-after="0"
                placement="top"
              >
                <IconifyIconOffline
                  height="20"
                  icon="radix-icons:question-mark-circled"
                  style="color: #333"
                  width="20"
                />
              </el-tooltip>
            </div>
          </template>
          <el-input
            v-if="
              item[Object.keys(item)[0]].input.type === 'text' ||
              item[Object.keys(item)[0]].input.type === 'string'
            "
            v-model="playParamForm[Object.keys(item)[0]]"
            :placeholder="
              '请输入' + item[Object.keys(item)[0]].input.description
            "
          />
          <el-input
            v-else-if="
              item[Object.keys(item)[0]].input.type === 'number' ||
              item[Object.keys(item)[0]].input.type === 'integer'
            "
            v-model.number="playParamForm[Object.keys(item)[0]]"
            :placeholder="
              '请输入' + item[Object.keys(item)[0]].input.description
            "
            type="number"
          />
          <el-switch
            v-else-if="item[Object.keys(item)[0]].input.type === 'boolean'"
            v-model="playParamForm[Object.keys(item)[0]]"
            :active-value="true"
            :inactive-value="false"
            active-text="开启"
            inactive-text="关闭"
            inline-prompt
          />
          <!-- 可扩展更多类型 -->
        </el-form-item>
      </template>
    </el-form>
    <template #footer>
      <el-button @click="isShowPlayParamDialog = false">取消</el-button>
      <el-button type="primary" @click="onPlayParamConfirm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import {
  apiGetPlaybookInput,
  apiGetPlaybookList,
  apiGetPlaybookVersionsList
} from "@/api/playbook";
import { Search } from "@element-plus/icons-vue";
import { useChatStore } from "@/store/warChat";
import LogicFlow from "@logicflow/core";
import "@logicflow/core/lib/style/index.css";
import "@logicflow/extension/lib/style/index.css";
import { registerCustomElement } from "@/views/playbook/playbookEdit/node";
import { useDetail } from "@/views/playbook/hooks";
import { executeDrawerAction } from "./DrawerExecute";
import IconifyIconOffline from "@/components/ReIcon/src/iconifyIconOffline";

const props = defineProps({
  modelValue: Boolean
});
const emit = defineEmits(["update:modelValue", "open", "close"]);
const chatStore = useChatStore();
const visible = ref(props.modelValue);
const playbookList = ref<any[]>([]);
const searchKey = ref(""); // 搜索关键字
const playbookPage = ref(1);
const playbookSize = ref(50);
const playbookTotal = ref(0);
const playbookLoading = ref(false);
const hoverId = ref<number | null>(null);

//剧本预览流程图
//初始数据
let data = {
  nodes: [
    {
      type: "start-node",
      x: 100,
      y: 100
    },
    {
      type: "end-node",
      x: 1000,
      y: 100
    }
  ]
};
const lf = ref<LogicFlow>();
const viewLogicflowRef = ref();
const isShowViewPlaybook = ref(false);
const playbookDetail = ref();
const isShowPlaybookVersions = ref(false);
const playbookVersionsTableData = ref();
const selectedPlaybookName = ref(""); // 当前选中的剧本名称
const { toDetail } = useDetail();
const isShowPlayParamDialog = ref(false);
const playParamForm = ref({});
const playParamFormRef = ref();
const playParamRules = ref({});
const playRunRow = ref(null);
const playInputData = ref([]);
//打开剧本版本列表
const openPlaybookVersionsDialog = async row => {
  // 保存当前选中的剧本名称
  selectedPlaybookName.value = row.name;
  //请求剧本版本列表
  let res: any = await apiGetPlaybookVersionsList({ playbook_id: row.id });
  playbookVersionsTableData.value = res.data.filter(
    (item: any) => item.status !== 0
  );
  console.log(playbookVersionsTableData.value);
  isShowPlaybookVersions.value = true;
};
//预览
const viewLogicflowData = row => {
  isShowViewPlaybook.value = true;
  initLogicFlow(row);
};
//执行
const playRun = async row => {
  playRunRow.value = row;
  const res = (await apiGetPlaybookInput({ flow_id: row.version_id })) as any;
  if (res.code === 0) {
    playInputData.value = res.data;
    // 重置参数表单和校验规则
    const formObj = {};
    const rulesObj = {};
    res.data.forEach(item => {
      const key = Object.keys(item)[0];
      const inputType = item[key].input.type;
      // 根据类型设置默认值
      if (inputType === "boolean") {
        formObj[key] = false; // 布尔类型默认为 false
      } else {
        formObj[key] = ""; // 其他类型默认为空字符串
      }
      rulesObj[key] = [
        {
          required: true,
          message: `请${inputType === "boolean" ? "选择" : "输入"}${
            item[key].input.description || Object.keys(item)[0]
          }`,
          trigger: inputType === "boolean" ? "change" : "blur"
        }
      ];
    });
    playParamForm.value = formObj;
    playParamRules.value = rulesObj;

    // 关键逻辑：如果没有参数，直接执行，不弹窗
    if (res.data.length === 0) {
      await onPlayParamConfirm();
    } else {
      isShowPlayParamDialog.value = true;
    }
  }
};
const onPlayParamConfirm = async () => {
  // 如果有参数需要验证，先进行表单验证
  if (playInputData.value.length > 0) {
    try {
      await playParamFormRef.value.validate();
    } catch (error) {
      // 验证失败，不执行后续操作
      return;
    }
  }

  isShowPlayParamDialog.value = false;
  const flow_input = {};
  Object.keys(playParamForm.value).forEach(key => {
    flow_input[key] = playParamForm.value[key];
  });
  const res = (await executeDrawerAction(
    chatStore.currentRoomId,
    "flow",
    playRunRow.value,
    { flow_input }
  )) as any;
  if (res && res.code === 0) {
    isShowPlaybookVersions.value = false;
  }
};
//渲染流程图数据
const initLogicFlow = async row => {
  //获取剧本的详细信息
  let res: any = await apiGetPlaybookVersionsList({
    playbook_id: row.flow_id
  });
  const result = res.data.find(item => item.version_id === row.version_id);
  playbookDetail.value = result;
  lf.value = new LogicFlow({
    container: viewLogicflowRef.value,
    width: 1450,
    height: 650,
    grid: {
      visible: false,
      size: 15
    },
    isSilentMode: true
  });
  registerCustomElement(lf.value); //注册自定义节点和边
  lf.value.setDefaultEdgeType("vue-edge"); //边的类型
  //如果是已有剧本，则渲染已有剧本的流程图信息，否则默认为初始数据
  if (Object.entries(playbookDetail.value.flow_json).length > 0) {
    playbookDetail.value.flow_json.nodes.forEach(node => {
      // //基于锚点的位置更新边的路径
      // lf.value.getNodeModelById(node.id).updateField();
      //在渲染流程图之前，先把初始化isWebSocket和scale的值
      if (node.properties && node.properties.isWebSocket !== undefined) {
        node.properties.isWebSocket = false;
        node.properties.scale = 1;
      }
    });
    lf.value.render(playbookDetail.value.flow_json);
  } else {
    lf.value.render(data);
  }
  lf.value.translateCenter(); // 将图形移动到画布中央
};
//对表格creator列进行处理
const creatorFormatter = row => {
  return `${row.creator.display_name}(${row.creator.username})`;
};
//对表格数据updator列进行处理
const updatorFormatter = row => {
  return `${row.updator.display_name}(${row.updator.username})`;
};
///////////////////////
watch(
  () => props.modelValue,
  val => (visible.value = val)
);

watch(visible, (val, oldVal) => {
  emit("update:modelValue", val);
  if (val && !oldVal) {
    emit("open");
    getPlaybook();
    // 打印作战室id
    console.log("当前作战室id：", chatStore.currentRoomId);
  } else if (!val && oldVal) {
    emit("close");
  }
});

function handleClose() {
  visible.value = false;
  // 这里可以重置你的内部状态
}

//获取剧本
const getPlaybook = async (
  page = playbookPage.value,
  size = playbookSize.value,
  keyword = searchKey.value
) => {
  const res = (await apiGetPlaybookList({
    page: page,
    size: size,
    keyword: keyword
  })) as any;
  if (res.code === 0) {
    playbookList.value = res.data.data || [];
    playbookPage.value = res.data.page;
    playbookSize.value = res.data.size;
    playbookTotal.value = res.data.total;
  }
};

const onSearch = () => {
  playbookPage.value = 1;
  getPlaybook(1, playbookSize.value, searchKey.value);
};

const onPlaybookPageChange = (page: number) => {
  playbookPage.value = page;
  getPlaybook(page, playbookSize.value, searchKey.value);
};
const onPlaybookSizeChange = (size: number) => {
  playbookSize.value = size;
  playbookPage.value = 1;
  getPlaybook(1, size, searchKey.value);
};
</script>

<style scoped>
.drawer-flex {
  display: flex;
  height: 100%;
}

.drawer-left {
  width: 100%;
  min-width: 220px;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #f0f0f0;
  height: 100%;
}

.drawer-left-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 48px;
  padding: 0 16px;
  border-bottom: 1px solid #f0f0f0;
}

.pagination-fixed {
  margin-top: auto;
  padding: 12px 0 0 0;
  background: #fff;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.drawer-preview {
  flex: 1;
  padding: 0px 0px;
  overflow-y: auto;
}

.drawer-preview-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.playbook-item {
  cursor: pointer;
}

.playbook-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  padding: 0 12px;
  border-bottom: 1px solid #f0f0f0;
  transition: background 0.2s;
}

.playbook-item-hover {
  background: #f5faff;
}

.playbook-title {
  font-size: 14px;
  color: #222;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 8px;
}

.playbook-remark {
  color: #888;
  font-size: 13px;
  margin-top: 4px;
}

.playbook-actions {
  color: #888;
  font-size: 13px;
}

.playbook-actions a {
  color: #1890ff;
  margin: 0 4px;
  cursor: pointer;
  text-decoration: none;
}

.playbook-actions a:hover {
  text-decoration: underline;
}

:deep(.el-drawer__footer),
:deep(.el-drawer-footer) {
  padding: 12px 0 0 0;
  background: #fff;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

:deep(.el-input__wrapper) {
  display: flex;
  justify-content: center;
  align-items: center;
}

.custom-close-btn {
  font-size: 26px;
  color: #888;
  cursor: pointer;
  user-select: none;
  padding: 0 8px;
  transition: color 0.2s;
}

.custom-close-btn:hover {
  color: #f56c6c;
}

.search-area {
  display: flex;
  align-items: center;
  margin: 12px 16px 8px 16px;
}

/* 剧本流程 */
.playbook-versions-name {
  display: flex;
  padding-bottom: 10px;

  .text {
    padding-left: 5px;
  }
}

.view-div {
  width: 100%;
  height: 100%;

  .view-logicflow {
    width: 100%;
    height: 75vh;
  }
}
</style>
