<template>
  <div class="play-config-container">
    <el-drawer
      :before-close="handleClose"
      :destroy-on-close="true"
      :model-value="visible"
      :size="600"
      :title="'剧本配置'"
      @update:model-value="handleVisibleChange"
    >
      <el-form :model="formData" label-width="120px">
        <!-- 事件名称 -->
        <el-form-item label="事件名称：">
          <el-input
            v-model="playData.event_ingestion_name"
            :disabled="true"

          />
        </el-form-item>
        <!-- 关联事件类型 -->
        <el-form-item label="关联事件类型：">
          <el-button
            plain
            style="width: 100%"
            type="primary"
            @click="handleAddEventType"
            >+ 添加关联规则
          </el-button>
        </el-form-item>
      </el-form>
      <div class="section-title">
        <span class="section-text">剧本配置预览</span>
        <span class="section-line" />
      </div>
      <el-scrollbar
        v-if="eventIngestionDetail.length > 0"
        class="preview-list-scrollbar"
      >
        <div class="preview-list">
          <el-row>
            <el-col
              v-for="item in eventIngestionDetail"
              :key="item.id"
              :span="24"
              class="preview-item"
            >
              <el-card shadow="hover">
                <div class="preview-header">
                  <span class="preview-title">{{
                    formatEventName(item.event_name)
                  }}</span>
                  <span class="preview-id">ID: {{ item.id }}</span>
                </div>
                <el-descriptions :column="2" border size="small">
                  <el-descriptions-item label="剧本绑定数量">
                    {{
                      Array.isArray(item.script_id) ? item.script_id.length : 0
                    }}
                  </el-descriptions-item>
                  <el-descriptions-item label="判断条件数量">
                    <el-tooltip
                      :content="
                        Array.isArray(item.condition)
                          ? JSON.stringify(item.condition, null, 2)
                          : ''
                      "
                      :hide-after="0"
                      effect="dark"
                      placement="top"
                    >
                      <span style="cursor: pointer">
                        {{
                          Array.isArray(item.condition)
                            ? item.condition.length
                            : 0
                        }}
                      </span>
                    </el-tooltip>
                  </el-descriptions-item>
                  <el-descriptions-item label="创建时间">
                    {{ item.ctime }}
                  </el-descriptions-item>
                  <el-descriptions-item label="更新时间">
                    {{ item.utime }}
                  </el-descriptions-item>
                </el-descriptions>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-scrollbar>
      <el-empty v-else description="暂无数据" />
    </el-drawer>

    <!-- 绑定事件类型弹窗 -->
    <binding
      v-model:visible="bindingVisible"
      :event-id="playData.id"
      @update:visible="onBindingVisibleChange"
    />
  </div>
</template>

<script lang="ts" setup>
import { defineEmits, defineProps, ref, watch } from "vue";
import Binding from "./binding.vue";
import { eventIngestionEditDetail } from "@/api/event";

// 表单数据
const playData = ref<any>({});
const eventIngestionDetail = ref<any>({});
// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  rowData: {
    type: Object,
    default: () => ({})
  }
});

// 定义组件事件
const emit = defineEmits(["update:visible", "refresh"]);
const formData = ref<any>({});

// 绑定弹窗可见性
const bindingVisible = ref(false);

// 监听visible属性变化
watch(
  () => props.visible,
  async newVal => {
    if (newVal) {
      playData.value = { ...props.rowData };
      await getEventIngestionDetail();
    }
  }
);

//获取事件接入详情
const getEventIngestionDetail = async () => {
  const res = (await eventIngestionEditDetail({
    id: props.rowData.id
  })) as any;
  if (res.code === 0 && Array.isArray(res.data) && res.data.length > 0) {
    eventIngestionDetail.value = res.data;
  } else {
    eventIngestionDetail.value = [];
  }
};

// 关闭抽屉
const handleClose = () => {
  playData.value = {};
  emit("update:visible", false);
};

// 监听抽屉打开状态变化
const handleVisibleChange = (visible: boolean) => {
  emit("update:visible", visible);
};

// 添加关联事件类型
const handleAddEventType = () => {
  bindingVisible.value = true;
};

const onBindingVisibleChange = (val: boolean) => {
  bindingVisible.value = val;
  if (val === false) {
    getEventIngestionDetail();
  }
};

function formatEventName(eventName: string) {
  if (!eventName) return "";
  // 替换 to_string!(.xxx) 为 xxx
  return eventName.replace(/to_string!\(\.(\w+)\)/g, "$1");
}
</script>

<style lang="scss" scoped>
.play-config-container {
  .el-drawer__body {
    padding: 20px;
  }
}

.section-title {
  display: flex;
  align-items: center;
  margin: 25px 0 8px 0;
  font-size: 16px;
  font-weight: 500;
}

.section-icon {
  color: #13c2c2;
  font-size: 16px;
  margin-right: 2px;
}

.section-text {
  color: #13c2c2;
  margin-right: 8px;
  font-size: 15px;
  font-weight: 500;
}

.section-line {
  flex: 1;
  height: 3px;
  background: #13c2c2;
  border-radius: 2px;
  margin-left: 4px;
  margin-top: 2px;
}

.preview-list {
  width: 100%;
  box-sizing: border-box;
  margin-top: 10px;
}

.preview-item {
  margin-bottom: 16px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-title {
  font-weight: 500;
}

.preview-id {
  color: #909399;
  font-size: 12px;
}

.preview-list-scrollbar {
  max-height: 75vh;
  min-height: 200px;
  margin-top: 10px;
  overflow-x: hidden;
}

.preview-list,
.preview-item,
.el-card,
.el-descriptions {
  width: 100% !important;
  box-sizing: border-box;
}

.el-descriptions__body {
  width: 100% !important;
  box-sizing: border-box;
  overflow-x: hidden !important;
}

.el-descriptions__table {
  table-layout: fixed !important;
  width: 100% !important;
}

.el-descriptions__cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
