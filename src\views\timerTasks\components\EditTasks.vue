<template>
  <el-dialog
    v-model="dialogVisible"
    :close-on-click-modal="false"
    :title="isEdit ? '编辑定时任务' : '新建定时任务'"
    destroy-on-close
    width="720px"
    top="5vh"
  >
    <el-scrollbar height="calc(100vh - 25vh)">
      <div class="task-form">
        <!-- 任务名称 -->
        <div class="form-item">
          <div class="form-label">
            <span class="required">*</span>
            任务名称
          </div>
          <el-input
            v-model="formData.name"
            maxlength="60"

            show-word-limit
          />
        </div>
        <!-- 执行操作类型选择 -->
        <div class="form-item">
          <div class="form-label">
            <span class="required">*</span>
            执行操作类型
          </div>
          <el-segmented
            v-model="formData.type"
            :options="[
              {
                label: '应用',
                value: 1
              },
              {
                label: '剧本',
                value: 0
              }
            ]"
            block
          />
        </div>

        <!-- 动作类型 -->
        <div v-if="formData.type === 1" class="form-item">
          <div class="form-label">
            <span class="required">*</span>
            选择应用
          </div>
          <el-select
            v-model="formData.action_input.app_name"
            :loading="isActionLoading"
            :remote-method="handleToolSearch"
            filterable
            placeholder="请选择应用"
            remote
            @change="onToolChange"
          >
            <el-option
              v-for="item in filteredToolList"
              :key="item.id"
              :label="item.description"
              :value="item.name"
            />
            <!-- 加载更多按钮 -->
            <template #footer>
              <div
                v-if="toolCurrentPage < toolTotalPage"
                style="text-align: center; padding: 8px 0"
              >
                <el-button
                  :loading="isActionLoading"
                  size="small"
                  style="width: 90%"
                  @click.stop="loadMoreTools"
                  >点击加载更多
                </el-button>
              </div>
              <div
                v-else
                style="text-align: center; color: #aaa; padding: 8px 0"
              >
                没有更多了
              </div>
            </template>
          </el-select>
          <div class="form-label">
            <span class="required">*</span>
            选择版本
          </div>
          <el-select
            v-model="formData.action_input.app_ver"
            :loading="isVersionLoading"
            filterable
            placeholder="请选择应用版本"
            @change="onVersionChange"
          >
            <el-option
              v-for="version in versionOptions"
              :key="version.id"
              :label="version.version"
              :value="version.version"
            />
          </el-select>
          <div class="form-label">
            <span class="required">*</span>
            选择动作
          </div>
          <el-select
            v-model="formData.action_input.action_name"
            :loading="isActionListLoading"
            filterable
            placeholder="请选择要执行的动作"
            @change="
              action => {
                const selectedAction = actionOptions.find(
                  opt => opt.name === action
                );
                if (selectedAction) {
                  formData.action_id = selectedAction.id;
                  formData.action_input.action = selectedAction.func;
                  // 生成动作参数输入框
                  generateActionParams(formData, selectedAction);
                }
              }
            "
          >
            <el-option
              v-for="action in actionOptions"
              :key="action.id"
              :label="action.name"
              :value="action.name"
            />
          </el-select>
          <!-- 重试次数/超时时间和参数输入 -->
          <div
            v-if="Object.keys(formData.action_input.action_input).length > 0"
          >
            <div class="form-label">
              <span class="required">*</span>
              动作参数
            </div>
            <div class="retry-timeout-wrapper">
              <!-- 工具动作的参数输入部分 -->
              <div
                v-for="(value, key) in formData.action_input.action_input"
                :key="key"
                class="param-row"
              >
                <div class="param-input-wrapper">
                  <span
                    :title="paramDescriptions[key] + ':'"
                    class="param-label"
                  >
                    {{ paramDescriptions[key] || key }}:
                  </span>
                  <el-switch
                    v-if="paramTypes[key] === 'boolean'"
                    v-model="formData.action_input.action_input[key]"
                    :active-value="true"
                    :inactive-value="false"
                    active-text="开启"
                    inactive-text="关闭"
                    inline-prompt
                  />
                  <el-input
                    v-else
                    v-model="formData.action_input.action_input[key]"
                    :placeholder="'请输入' + (paramDescriptions[key] || key)"
                    :rows="2"
                    :show-password="paramTypes[key] === 'password'"
                    type="textarea"
                  />
                  <span v-if="paramRequired[key]" class="required">*</span>
                </div>
              </div>
            </div>
          </div>
          <!-- 资源选择 -->
          <div class="form-label">资源选择</div>
          <el-select
            v-model="formData.action_input.resource"
            :loading="isResourceLoading"
            filterable
            placeholder="请选择资源"
            @change="value => onResourceChange(value, formData)"
          >
            <el-option
              v-for="resource in resourceOptions"
              :key="resource.id"
              :label="resource.name"
              :value="resource.id"
            />
          </el-select>
        </div>

        <!-- 剧本类型 -->
        <div v-if="formData.type === 0" class="form-item">
          <div class="form-label">
            <span class="required">*</span>
            选择剧本
          </div>
          <el-select
            v-model="formData.playbook_id"
            :infinite-scroll-disabled="isLoading || currentPage >= totalPage"
            :loading="isLoading"
            :remote-method="handleSearch"
            filterable
            placeholder="请指定剧本"
            remote
            @change="onPlaybookChange"
          >
            <el-option
              v-for="item in scriptOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <!-- <div class="playbook-option">
                <span class="playbook-name">{{ item.name }}</span>
                <span v-if="item.remark" class="playbook-remark"
                  >({{ item.remark }})</span
                >
              </div> -->
            </el-option>
            <!-- 加载更多按钮 -->
            <template #footer>
              <div
                v-if="currentPage < totalPage"
                style="text-align: center; padding: 8px 0"
              >
                <el-button
                  :loading="isLoading"
                  size="small"
                  style="width: 90%"
                  @click.stop="loadMoreScripts"
                  >点击加载更多
                </el-button>
              </div>
              <div
                v-else
                style="text-align: center; color: #aaa; padding: 8px 0"
              >
                没有更多了
              </div>
            </template>
          </el-select>
          <!-- 剧本版本 -->
          <div class="form-label">
            <span class="required">*</span>
            选择版本
          </div>
          <el-select
            v-model="formData.playbook_input.flow_id"
            @change="
              async versionId => {
                const inputParams = await getScriptInput(versionId);
                if (inputParams && inputParams.data) {
                  generatePlaybookParams(formData, inputParams.data);
                }
              }
            "
          >
            <el-option
              v-for="version in playbookVersionOptions"
              :key="version.id"
              :label="version.version"
              :value="version.id"
            >
              <div class="playbook-option">
                <span class="playbook-name">{{ version.version }}</span>
                <span v-if="version.remark" class="playbook-remark"
                  >({{ version.remark }})</span
                >
              </div>
            </el-option>
          </el-select>
          <!-- 剧本参数的输入部分 -->
          <div
            v-if="
              Object.keys(formData.playbook_input.flow_input).filter(
                key => key !== 'version'
              ).length > 0
            "
            class="form-item"
          >
            <div class="form-label">
              <span class="required">*</span>
              输入参数
            </div>
            <div class="retry-timeout-wrapper">
              <div
                v-for="(value, key) in formData.playbook_input.flow_input"
                :key="key"
                class="param-row"
              >
                <div class="param-input-wrapper">
                  <span
                    :title="paramDescriptions[key] + ':'"
                    class="param-label"
                  >
                    {{ paramDescriptions[key] || key }}:
                  </span>
                  <el-switch
                    v-if="paramTypes[key] === 'boolean'"
                    v-model="formData.playbook_input.flow_input[key]"
                    :active-value="true"
                    :inactive-value="false"
                    active-text="开启"
                    inactive-text="关闭"
                    inline-prompt
                  />
                  <el-input
                    v-else
                    v-model="formData.playbook_input.flow_input[key]"
                    :placeholder="paramDescriptions[key] || '请输入参数值'"
                    :type="
                      paramTypes[key] === 'password' ? 'password' : 'textarea'
                    "
                    :rows="2"
                  />
                  <span v-if="paramRequired[key]" class="required">*</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 重试次数/超时时间和参数输入 -->
        <div v-if="formData.type === 1" class="retry-timeout-wrapper">
          <div class="retry-timeout-row">
            <div class="retry-timeout-item">
              <div class="retry-timeout-label">
                <el-icon>
                  <RefreshRight />
                </el-icon>
                重试次数
              </div>
              <el-input-number
                v-model="formData.action_input.retry_times"
                :max="3"
                :min="0"
                class="custom-input-number"
                controls-position="right"
                size="default"
              />
            </div>
            <div class="retry-timeout-item">
              <div class="retry-timeout-label">
                <el-icon>
                  <Timer />
                </el-icon>
                超时时间(秒)
              </div>
              <el-input-number
                v-model="formData.action_input.timeout"
                :max="3600"
                :min="1"
                class="custom-input-number"
                controls-position="right"
                size="default"
              />
            </div>
          </div>
        </div>
        <!-- 执行策略 -->
        <div class="form-item">
          <div class="form-label">
            <span class="required">*</span>
            执行策略
          </div>
          <el-segmented
            v-model="formData.status"
            :options="[
              {
                label: '单次执行',
                value: 0
              },
              {
                label: '周期执行',
                value: 1
              }
            ]"
            block
          />
          <!-- 单次执行 -->
          <div v-if="formData.status === 0" class="mt-4">
            <div class="form-label">
              <span class="required">*</span>
              执行时间
            </div>
            <el-date-picker
              v-model="formData.tactics"
              :disabled-date="time => time.getTime() < Date.now()"
              format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择执行时间"
              style="width: 100%"
              type="datetime"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </div>
          <!-- 周期执行 -->
          <div v-if="formData.status === 1" class="mt-4">
            <div class="cron-input-container">
              <div class="cron-header">
                <span class="cron-label">秒</span>
                <span class="cron-label">分</span>
                <span class="cron-label">时</span>
                <span class="cron-label">日</span>
                <span class="cron-label">月</span>
                <span class="cron-label">周</span>
              </div>
              <div class="cron-visual-editor">
                <div
                  v-for="(field, index) in cronFields"
                  :key="index"
                  class="cron-field"
                >
                  <input
                    :value="field"
                    class="cron-field-input"
                    @input="e => updateCronField(e.target.value, index)"
                  />
                </div>
              </div>
              <div class="next-execution">
                下次执行时间：{{ nextExecutionTime }}
              </div>
              <div class="next-execution">执行时间：{{ nextCronTime }}</div>
            </div>
            <div class="form-label">
              <span class="required">*</span>
              结束时间
            </div>
            <el-date-picker
              v-model="formData.end_time"
              :disabled-date="time => time.getTime() < Date.now()"
              format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择结束时间"
              style="width: 100%"
              type="datetime"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </div>
        </div>
      </div>
    </el-scrollbar>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="onSubmit">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import {
  computed,
  defineEmits,
  defineProps,
  onMounted,
  reactive,
  ref,
  watch
} from "vue";
import {
  createDefaultFormData,
  useCronExpression,
  useFormSubmit,
  useParams,
  usePlaybookList,
  useToolList
} from "./EditTasks";
import "cronstrue/locales/zh_CN";
import { RefreshRight, Timer } from "@element-plus/icons-vue";
import cronParser from "cron-parser";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  taskData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(["update:visible", "success"]);
const dialogVisible = ref(props.visible);

// 表单数据
const formData = reactive(createDefaultFormData());

// 使用封装的composables
const { cronFields, updateCronField, nextExecutionTime } = useCronExpression();
const {
  paramKeys,
  paramTypes,
  paramRequired,
  paramDescriptions,
  generateActionParams,
  generatePlaybookParams
} = useParams();

// 1. 先解构 useToolList
const {
  isActionLoading,
  filteredToolList,
  versionOptions,
  actionOptions,
  isVersionLoading,
  isActionListLoading,
  handleToolChange,
  handleVersionChange,
  getToolList,
  handleToolSearch,
  loadMoreTools,
  toolCurrentPage,
  toolTotalPage,
  isResourceLoading,
  resourceOptions,
  onResourceChange
} = useToolList();

// 2. 再调用 useFormSubmit
const { handleSubmit } = useFormSubmit(formData, cronFields, filteredToolList);

// 初始化表单数据
const initFormData = () => {
  // 保存当前的配置
  const currentType = formData.type;
  const currentActionInput = { ...formData.action_input };
  const currentPlaybookInput = { ...formData.playbook_input };
  const currentPlaybookId = formData.playbook_id;

  // 重置基本数据
  Object.assign(formData, {
    name: "",
    id: undefined,
    status: 0,
    tactics: "",
    end_time: "",
    type: currentType
  });

  // 根据类型恢复相应的配置
  if (currentType === 1) {
    formData.action_input = currentActionInput;
    formData.playbook_input = { flow_id: "", flow_input: {} };
    formData.playbook_id = "";
  } else {
    formData.playbook_input = currentPlaybookInput;
    formData.playbook_id = currentPlaybookId;
    formData.action_input = {
      app_name: "",
      app_ver: "",
      main: "",
      action_name: "",
      name: "",
      node_name: "",
      action: "",
      retry_times: 0,
      timeout: 300,
      action_input: {}
    };
  }

  // 重置cron和参数键
  cronFields.value = ["*", "*", "*", "*", "*", "*"];
  paramKeys.value =
    currentType === 1
      ? Object.keys(currentActionInput.action_input || {}).reduce(
          (acc, key) => ({ ...acc, [key]: key }),
          {}
        )
      : Object.keys(currentPlaybookInput.flow_input || {}).reduce(
          (acc, key) => ({ ...acc, [key]: key }),
          {}
        );
};

// 剧本相关
const {
  isLoading,
  scriptOptions,
  getScriptList,
  handleSearch,
  loadMoreScripts,
  getScriptInput,
  getPlaybookVersions,
  playbookVersionOptions,
  currentPage,
  totalPage
} = usePlaybookList();

// 监听对话框可见性变化
watch(
  () => props.visible,
  async val => {
    dialogVisible.value = val;
    if (val) {
      // 打开对话框时
      if (props.isEdit && props.taskData) {
        console.log(props.taskData);
        // 编辑模式
        Object.assign(formData, createDefaultFormData()); // 先重置所有数据
        formData.id = props.taskData.id;
        formData.name = props.taskData.name;
        formData.type = props.taskData.type;
        formData.status = props.taskData.status;
        formData.tactics = props.taskData.tactics;
        formData.end_time = props.taskData.end_time;

        if (formData.type === 0) {
          // 1. 先赋值剧本
          let playbookId = props.taskData.playbook;

          // 2. 确保剧本列表有数据
          if (!scriptOptions.value.length) {
            await getScriptList();
          }

          // 3. name→id 映射
          const matched = scriptOptions.value.find(
            item => item.name === playbookId || item.value === playbookId
          );
          if (matched) {
            playbookId = matched.value; // id
            formData.playbook_id = playbookId;
            await getPlaybookVersions(playbookId);
          } else {
            // 没找到，说明剧本被删除
            formData.playbook_id = "";
            // 不请求版本列表
          }

          // 5. 赋值剧本版本
          formData.playbook_input.flow_id =
            props.taskData.playbook_input?.flow_id || "";

          // 6. 回填参数
          if (props.taskData.input_args) {
            // 清空
            paramDescriptions.value = {};
            paramTypes.value = {};
            paramRequired.value = {};
            // 遍历 input_args
            props.taskData.input_args.forEach(item => {
              const key = Object.keys(item)[0];
              const param = item[key].input;
              paramDescriptions.value[key] = param.description || key;
              paramTypes.value[key] = param.type || "text";
              paramRequired.value[key] = param.required || false;
              // 回填参数值
              formData.playbook_input.flow_input[key] =
                props.taskData.playbook_input.flow_input[key] || "";
            });
          }
        } else if (formData.type === 1) {
          // 只做动作相关的回填
          if (props.taskData.action_input) {
            const { action_input } = props.taskData;
            formData.action_input = {
              ...createDefaultFormData().action_input,
              ...action_input,
              action_input: {}
            };

            // 只在编辑模式下从action_input中获取参数信息
            if (props.isEdit && action_input.action_input) {
              formData.action_input.action_input = {
                ...action_input.action_input
              };
              // 先从动作定义中获取参数描述
              const selectedAction = actionOptions.value.find(
                opt => opt.name === action_input.action_name
              );
              if (selectedAction && selectedAction.input) {
                selectedAction.input.forEach(input => {
                  if (input.key) {
                    paramDescriptions[input.key] =
                      input.description || input.key;
                    paramTypes[input.key] = input.type || "text";
                    paramRequired[input.key] = input.required || false;
                  }
                });
              }
              // 设置参数值，并只在paramDescriptions[key]不存在时才赋值
              if (props.taskData.input_args) {
                props.taskData.input_args.forEach(item => {
                  const key = Object.keys(item)[0];
                  const param = item[key];
                  paramKeys.value[key] = key;
                  paramDescriptions.value[key] = param.description || key;
                  paramTypes.value[key] = param.type || "text";
                  paramRequired.value[key] = param.required || false;
                });
              }
            }

            await getToolList();
            if (action_input.app_name) {
              const originalVersion = action_input.app_ver;
              const originalAction = action_input.action_name;
              await handleToolChange(formData, action_input.app_name);
              if (originalVersion) {
                formData.action_input.app_ver = originalVersion;
                await handleVersionChange(formData, originalVersion);
                if (originalAction) {
                  formData.action_input.action_name = originalAction;
                  // 修复：编辑时根据action_name回填action_id
                  const selectedAction = actionOptions.value.find(
                    opt => opt.name === originalAction
                  );
                  if (selectedAction) {
                    formData.action_id = selectedAction.id;
                  }
                }
              }
            }
          }
        }

        if (formData.status === 1 && formData.tactics) {
          const parts = formData.tactics.split(" ");
          if (parts.length === 6) {
            cronFields.value = parts;
          }
        }
      } else {
        // 新建模式
        getToolList();
        // 移除自动获取剧本列表，改为懒加载
        Object.assign(formData, createDefaultFormData());
        cronFields.value = ["*", "*", "*", "*", "*", "*"];
        paramKeys.value = {};
      }
    } else {
      // 关闭对话框时完全清除数据
      Object.assign(formData, createDefaultFormData());
      cronFields.value = ["*", "*", "*", "*", "*", "*"];
      paramKeys.value = {};
      versionOptions.value = [];
      actionOptions.value = [];
      scriptOptions.value = [];
      // 发出事件通知父组件更新
      emit("update:visible", false);
    }
  }
);

// 监听执行类型变化，懒加载剧本列表
watch(
  () => formData.type,
  newType => {
    if (newType === 0 && scriptOptions.value.length === 0) {
      // 当选择剧本类型且还未加载剧本列表时，才获取剧本列表
      getScriptList();
    }
  }
);

// 监听对话框关闭
watch(
  () => dialogVisible.value,
  val => {
    emit("update:visible", val);
    if (!val) {
      // 清除所有配置
      initFormData();
      // 重置cron字段
      cronFields.value = ["*", "*", "*", "*", "*", "*"];
      // 清除参数键
      paramKeys.value = {};
      // 清除工具和剧本相关的选项
      versionOptions.value = [];
      actionOptions.value = [];
      scriptOptions.value = [];
    }
  }
);

// 组件挂载时加载初始数据
onMounted(() => {
  if (!props.isEdit) {
    getToolList();
  }
});

// 处理工具选择
const onToolChange = async toolName => {
  // 1. 先清空所有依赖于应用的字段
  formData.action_input.app_ver = "";
  formData.action_input.action_name = "";
  formData.action_id = "";
  formData.action_input.action = "";
  formData.action_input.action_input = {};
  formData.action_input.resource = "";

  // 2. 清空 options
  versionOptions.value = [];
  actionOptions.value = [];
  resourceOptions.value = [];

  // 3. 再调用原有的逻辑（如加载新应用的版本、资源等）
  await handleToolChange(formData, toolName);
};

// 处理版本选择
const onVersionChange = async version => {
  await handleVersionChange(formData, version);
};

// 提交表单
const onSubmit = async () => {
  const success = await handleSubmit(props.isEdit);
  if (success) {
    emit("success");
    dialogVisible.value = false;
  }
};

const nextCronTime = computed(() => {
  if (formData.status === 1) {
    const cronExpr = cronFields.value.join(" ");
    try {
      const interval = cronParser.parseExpression(cronExpr, { iterator: true });
      const next = interval.next().value.toDate();
      return `${next.getFullYear()}年${String(next.getMonth() + 1).padStart(2, "0")}月${String(next.getDate()).padStart(2, "0")}日 ${String(next.getHours()).padStart(2, "0")}:${String(next.getMinutes()).padStart(2, "0")}:${String(next.getSeconds()).padStart(2, "0")}`;
    } catch (e) {
      return "表达式不支持";
    }
  }
  return "";
});

const onPlaybookChange = async playbookId => {
  // 1. 获取剧本版本
  await getPlaybookVersions(playbookId);
  // 2. 清空已选版本和参数
  formData.playbook_input.flow_id = "";
  formData.playbook_input.flow_input = {};
};
</script>

<style scoped>
@import "../css/EditTasks.css";
</style>
