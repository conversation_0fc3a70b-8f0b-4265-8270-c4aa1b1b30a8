<template>
  <el-dialog
    v-model="dialogVisible"
    :before-close="handleClose"
    :title="title"
    class="approve-dialog"
    destroy-on-close
    top="5vh"
    width="50%"
  >
    <div class="approve-info-content">
      <el-form label-position="top" label-width="120px">
        <el-form-item label="审批详情：">
          <div class="approve-detail">
            <el-descriptions :column="1" border label-width="100px">
              <el-descriptions-item label="名称">
                <div>{{ data.title }}</div>
              </el-descriptions-item>
              <el-descriptions-item label="审批内容">
                <div>{{ data.content }}</div>
              </el-descriptions-item>
              <el-descriptions-item label="审批人">
                <el-tag
                  v-for="item in data.approvers"
                  :key="item"
                  :style="{ color: '--pure-theme-menu-hover', margin: '5px' }"
                  color="#D9E4FF"
                  size="large"
                  >{{ item.display_name }}({{ item.username }})
                </el-tag>
              </el-descriptions-item>
              \
              <el-descriptions-item label="审批类型">
                <div>{{ data.approve_type == 1 ? "或签" : "会签" }}</div>
              </el-descriptions-item>
              <el-descriptions-item label="审批状态">
                <el-tag v-if="data.status === 3" type="info">已过期</el-tag>
                <el-tag v-if="data.status === 2" type="danger">拒绝</el-tag>
                <el-tag v-if="data.status === 1" type="success">通过</el-tag>
                <el-tag v-if="data.status === 0" type="warning">待处理</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="开始时间">
                <span class="time-text">{{ formatTime(data.ctime) }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="过期时间">
                <span class="time-text">{{
                  formatTime(data.expire_time)
                }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-form-item>
        <el-form-item label="自定义参数：">
          <div class="param-list">
            <el-form>
              <el-form-item
                v-for="(item, index) in data.params"
                :key="index"
                :label="item.desc"
                label-position="top"
              >
                <el-input
                  v-model="item.default"
                  :disabled="data.status !== 0"
                />
              </el-form-item>
            </el-form>
          </div>
        </el-form-item>
        <el-form-item v-if="Object.keys(data.results).length > 0">
          <el-collapse style="width: 100%">
            <el-collapse-item>
              <template #title>
                <div class="approve-title-custom">审批情况：(点击查看)</div>
              </template>
              <div
                v-for="(item, index) in data.results"
                :key="index"
                class="approve-detail-item"
              >
                <div>{{ item.display_name }}({{ item.username }})</div>
                <div v-html="stripParagraphTags(item.remark)" />
                <div :style="{ color: item.agree === true ? 'green' : 'red' }">
                  {{ item.agree === true ? "同意" : "拒绝" }}
                </div>
                <div>{{ item.approve_time || "无" }}</div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-form-item>

        <el-form-item v-if="data.status == 0" label="审批意见：" required>
          <div style="border: 1px solid #ccc; width: 100%">
            <Toolbar
              :defaultConfig="toolbarConfig"
              :editor="editorRef"
              mode="default"
              style="border-bottom: 1px solid #ccc"
            />
            <Editor
              v-model="form"
              :defaultConfig="editorConfig"
              mode="default"
              style="height: 180px; overflow-y: hidden"
              @onCreated="handleCreated"
            />
          </div>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span v-if="data.status == 0" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="danger" @click="handleRefuse">拒绝</el-button>
        <el-button type="primary" @click="handleConfirm">同意</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { computed, ref, shallowRef, watch } from "vue";
import { ElLoading, ElMessage } from "element-plus";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import "@wangeditor/editor/dist/css/style.css";
import type { IDomEditor } from "@wangeditor/editor";
import { apiApproveUpdate } from "@/api/approve";

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    default: "审批信息"
  },
  rowData: {
    type: Object,
    default: () => null
  }
});

const emit = defineEmits(["update:modelValue", "refresh"]);

const dialogVisible = computed({
  get: () => props.modelValue,
  set: val => emit("update:modelValue", val)
});

const form = ref<any>();
const approveDetailList = ref();
const imageContent = ref("");
const params = ref({});

const editorRef = shallowRef();
// 工具栏配置(取消全部功能)
const toolbarConfig = {
  toolbarKeys: [],
  hideKeys: [
    "fullScreen",
    "insertTable",
    "codeBlock",
    "insertLink",
    "todo",
    "emotion",
    "fontFamily",
    "fontSize",
    "lineHeight",
    "bulletedList",
    "numberedList",
    "blockquote",
    "code",
    "clearStyle",
    "divider",
    "redo",
    "undo",
    "through",
    "italic",
    "bold",
    "color",
    "backgroundColor",
    "justifyCenter",
    "justifyLeft",
    "justifyRight",
    "indent",
    "delIndent",
    "header1",
    "header2",
    "header3",
    "uploadImage"
  ]
};

const editorConfig = {
  placeholder: "",
  MENU_CONF: {
    uploadImage: {
      // server: "/api/upload/image",
      fieldName: "file",
      maxFileSize: 10 * 1024 * 1024,
      maxNumberOfFiles: 10,
      allowedFileTypes: ["image/*"],
      metaWithUrl: true,
      customUpload: async (file, insertFn) => {
        const loading = ElLoading.service({
          lock: true,
          text: "图片处理中...",
          background: "rgba(0, 0, 0, 0.7)"
        });

        try {
          const compressedFile = await compressImage(file);
          const reader = new FileReader();
          reader.onload = () => {
            const base64 = reader.result as string;
            imageContent.value = base64;
            insertFn(base64, file.name, base64);
            // ElMessage.success({
            //   message: "图片上传成功",
            //   duration: 1000
            // });

            loading.close();
          };
          reader.onerror = () => {
            ElMessage.error("图片读取失败");
            loading.close();
          };
          reader.readAsDataURL(compressedFile);
        } catch (error) {
          ElMessage.error("图片处理失败");
          loading.close();
        }
      }
    }
  }
};

// 图片压缩方法
const compressImage = async (file: File): Promise<File> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = URL.createObjectURL(file);
    img.onload = () => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      const MAX_WIDTH = 1600;
      const MAX_HEIGHT = 1200;
      let { width, height } = img;

      if (width > height) {
        if (width > MAX_WIDTH) {
          height *= MAX_WIDTH / width;
          width = MAX_WIDTH;
        }
      } else {
        if (height > MAX_HEIGHT) {
          width *= MAX_HEIGHT / height;
          height = MAX_HEIGHT;
        }
      }

      canvas.width = width;
      canvas.height = height;
      ctx?.drawImage(img, 0, 0, width, height);

      const fileType = file.type || "image/jpeg";
      const mimeType = fileType === "image/png" ? "image/png" : "image/jpeg";

      canvas.toBlob(
        blob => {
          if (blob) {
            const newFile = new File([blob], file.name, {
              type: mimeType,
              lastModified: Date.now()
            });
            resolve(newFile);
          } else {
            reject(new Error("图片压缩失败"));
          }
        },
        mimeType,
        0.9
      );
    };
    img.onerror = () => reject(new Error("图片加载失败"));
  });
};

const handleCreated = (editor: IDomEditor) => {
  editorRef.value = editor;
};

const handleClose = () => {
  dialogVisible.value = false;
};

// 同意提交
const handleConfirm = async () => {
  const editor = editorRef.value;
  const textContent = editor?.getText() || "";
  if (!textContent.trim()) {
    ElMessage.warning("请输入审批意见");
    return;
  }
  data.value.params.forEach(item => {
    item["value"] = item.default;
  });

  try {
    let res: any = await apiApproveUpdate({
      id: data.value.id,
      agree: true,
      remark: form.value,
      params: data.value.params // 使用转换后的params对象
    });
    if (res.code === 0) {
      ElMessage.success("处理成功");
      handleClose();
      emit("refresh", {
        page: 1,
        size: 15, // 从父组件传递的size参数
        status: [0] // 从父组件传递的status参数
      });
    }
  } catch (error) {
    ElMessage.error("处理失败");
  }
};

// 拒绝提交
const handleRefuse = async () => {
  const editor = editorRef.value;
  const textContent = editor?.getText() || "";
  if (!textContent.trim()) {
    ElMessage.warning("请输入审批意见");
    return;
  }
  try {
    let res: any = await apiApproveUpdate({
      id: data.value.id,
      agree: false,
      remark: form.value,
      params: params.value
    });
    if (res.code === 0) {
      ElMessage.success("处理成功");
      handleClose();
      emit("refresh", {
        page: 1,
        size: 15, // 从父组件传递的size参数
        status: [0] // 从父组件传递的status参数
      });
    }
  } catch (error) {
    ElMessage.error("处理失败");
  }
};

interface ApproveData {
  id: string;
  title: string;
  content: string;
  approvers: any;
  status: number;
  ctime: string | number;
  expire_time: string | number;
  processors: any;
  approve_type: number;

  [key: string]: any;
}

const data = ref<ApproveData>({} as ApproveData);

watch(
  () => props.rowData,
  (newVal: ApproveData) => {
    if (newVal) {
      data.value = newVal;
      form.value = "";
    }
  },
  { immediate: true }
);

const formatTime = (timestamp: string | number) => {
  if (!timestamp) return "";
  const date = new Date(timestamp);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false
  });
};

// 新增方法处理富文本数据
const stripParagraphTags = (html: string): string => {
  return html.replace(/<\/?(p|P)>/g, "").replace(/<[^>]+>/g, "");
};
</script>

<style scoped>
.approve-info-content {
  padding: 20px;
}

:deep(.el-dialog__header) {
  margin-right: 0;
  border-bottom: 1px solid #ebeef5;
  padding: 20px;
}

:deep(.el-dialog__footer) {
  border-top: 1px solid #ebeef5;
  padding: 15px 20px;
}

:deep(.w-e-text-container) {
  background-color: #fff;
  z-index: auto !important;
}

:deep(.w-e-toolbar) {
  z-index: auto !important;
}

:deep(.w-e-text-container [data-w-e-textarea="true"]) {
  height: 180px !important;
  min-height: unset !important;
}

.approve-detail {
  width: 100%;
  margin-top: 10px;
  border-radius: 4px;
  overflow: hidden;
  text-align: center;
}

.time-text {
  color: #606266;
  font-size: 14px;
}

.approve-detail-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 新增审批标题样式 */
.approve-title-custom {
  color: #606266; /* 与自定义参数颜色一致 */
  font-size: 14px; /* 与自定义参数字体大小一致 */
  font-weight: 700; /* 清除加粗效果 */
  line-height: 22px; /* 调整行高 */
}

.param-list {
  width: 100%;
  border: 1px solid #ccc;
  padding: 10px;
}
</style>
