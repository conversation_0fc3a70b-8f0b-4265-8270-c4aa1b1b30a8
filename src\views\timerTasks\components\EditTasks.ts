import { computed, ref, type Ref } from "vue";
import {
  apiGetPlaybookInput,
  apiGetPlaybookList,
  apiGetPlaybookVersionsList
} from "@/api/playbook";
import {
  getToolActionList,
  getToolDetail,
  getToolList,
  getToolVersionList
} from "@/api/toolManagement";
import { createTimed, updateTimed } from "@/api/timed";
import { ElMessage } from "element-plus";
import cronstrue from "cronstrue/i18n";

// 定义表单数据类型
interface FormData {
  id?: string;
  name: string;
  type: number;
  playbook_id: string;
  playbook_input: {
    flow_id: string;
    flow_input: Record<string, any>;
  };
  action_id: string;
  action_input: {
    app_name: string;
    app_ver: string;
    main: string;
    action_name: string;
    name: string;
    node_name: string;
    action: string;
    retry_times: number;
    timeout: number;
    action_input: Record<string, any>;
    resource: string;
  };
  status: number;
  tactics: string;
  end_time: string;
}

// 创建默认表单数据
export const createDefaultFormData = (): FormData => ({
  name: "",
  type: 1,
  playbook_id: "",
  playbook_input: {
    flow_id: "",
    flow_input: {}
  },
  action_id: "",
  action_input: {
    app_name: "",
    app_ver: "",
    main: "",
    action_name: "",
    name: "",
    node_name: "",
    action: "",
    retry_times: 0,
    timeout: 300,
    action_input: {},
    resource: ""
  },
  status: 0,
  tactics: "",
  end_time: ""
});

// 移除对象中的空值
export const removeEmptyValues = (obj: Record<string, any>) => {
  const newObj = {};
  Object.keys(obj).forEach(key => {
    if (obj[key] !== null && obj[key] !== undefined && obj[key] !== "") {
      if (typeof obj[key] === "object" && !Array.isArray(obj[key])) {
        newObj[key] = removeEmptyValues(obj[key]);
      } else {
        newObj[key] = obj[key];
      }
    }
  });
  return newObj;
};

// 应用列表相关
export const useToolList = () => {
  const toolListData = ref([]);
  const isActionLoading = ref(false);
  const actionSearchKeyword = ref("");
  const selectedTool = ref<any>(null);
  const versionOptions = ref([]);
  const actionOptions = ref([]);
  const isVersionLoading = ref(false);
  const isActionListLoading = ref(false);
  const isResourceLoading = ref(false);
  const resourceOptions = ref([]);

  // 分页相关
  const toolCurrentPage = ref(1);
  const toolTotalPage = ref(1);

  // 查询参数
  const queryParams = {
    page: 1,
    size: 20
  };

  // 过滤后的应用列表
  const filteredToolList = computed(() => toolListData.value);

  // 获取应用列表
  const fetchToolList = async () => {
    if (isActionLoading.value) return;
    isActionLoading.value = true;
    try {
      const toolRef = (await getToolList({
        page: toolCurrentPage.value,
        size: queryParams.size,
        key: actionSearchKeyword.value
      })) as any;
      const toolList = toolRef.data.tools || [];
      if (toolCurrentPage.value === 1) {
        toolListData.value = toolList;
      } else {
        toolListData.value = [...toolListData.value, ...toolList];
      }
      toolTotalPage.value = toolRef.data.total_pages;
    } catch (error) {
      console.error("获取应用列表失败:", error);
    } finally {
      isActionLoading.value = false;
    }
  };

  // 获取工具版本
  const toolVersions = async (tool: any, formData: FormData) => {
    selectedTool.value = tool;
    isVersionLoading.value = true;
    try {
      const versionsRef = (await getToolVersionList({
        name: tool.name
      })) as any;
      // 从应用列表返回数据中获取main
      formData.action_input.main = tool.main;
      versionOptions.value = versionsRef.data || [];
      return versionOptions.value;
    } catch (error) {
      console.error("获取版本列表失败:", error);
      return [];
    } finally {
      isVersionLoading.value = false;
    }
  };

  //获取工具资源
  const getToolResource = async (toolName: string) => {
    isActionLoading.value = true;
    try {
      const toolRef = (await getToolDetail({ name: toolName })) as any;
      return toolRef.data || null;
    } catch (error) {
      console.error("获取工具资源失败:", error);
      return null;
    } finally {
      isActionLoading.value = false;
    }
  };

  // 获取动作列表
  const toolActions = async (toolName: string, version: string) => {
    isActionListLoading.value = true;
    try {
      const actionsRef = (await getToolActionList({
        name: toolName,
        version: version
      })) as any;
      actionOptions.value = (actionsRef.data.actions || []).map(action => ({
        ...action,
        id: action.id,
        name: action.name,
        func: action.func
      }));
      return actionOptions.value;
    } catch (error) {
      console.error("获取动作列表失败:", error);
      return [];
    } finally {
      isActionListLoading.value = false;
    }
  };

  // 获取资源列表
  const fetchResourceList = async (toolName: string) => {
    isResourceLoading.value = true;
    try {
      const resourceRef = await getToolResource(toolName);
      if (resourceRef) {
        resourceOptions.value = Array.isArray(resourceRef) ? resourceRef : [];
      }
    } catch (error) {
      console.error("获取资源列表失败:", error);
      ElMessage.error("获取资源列表失败");
    } finally {
      isResourceLoading.value = false;
    }
  };

  // 处理工具搜索
  const handleToolSearch = (query: string) => {
    actionSearchKeyword.value = query;
    toolCurrentPage.value = 1;
    toolListData.value = [];
    fetchToolList();
  };

  // 加载更多工具
  const loadMoreTools = () => {
    if (toolCurrentPage.value >= toolTotalPage.value || isActionLoading.value)
      return;
    toolCurrentPage.value++;
    fetchToolList();
  };

  // 处理资源选择
  const onResourceChange = (resourceName: string, formData: FormData) => {
    if (resourceName && selectedTool.value) {
      const selectedResource = resourceOptions.value.find(
        (resource: any) => resource.name === resourceName
      );
      if (selectedResource) {
        formData.action_input.resource = selectedResource.name;
        console.log("选中的资源:", selectedResource);
      }
    }
  };

  // 处理工具选择
  const handleToolChange = async (formData: FormData, toolName: string) => {
    // 重置版本和动作
    versionOptions.value = [];
    actionOptions.value = [];

    // 保留原有的版本和动作
    const originalVersion = formData.action_input.app_ver;
    const originalAction = formData.action_input.action_name;
    const originalActionId = formData.action_id;
    const originalRetry = formData.action_input.retry_times;
    const originalTimeout = formData.action_input.timeout;
    const originalParams = formData.action_input.action_input;

    const tool = filteredToolList.value.find(item => item.name === toolName);
    if (tool) {
      try {
        await toolVersions(tool, formData);
        // 获取资源列表
        await fetchResourceList(toolName);
        // 恢复原有的配置
        if (originalVersion) {
          formData.action_input.app_ver = originalVersion;
          await handleVersionChange(formData, originalVersion);
          if (originalAction) {
            formData.action_input.action_name = originalAction;
            formData.action_id = originalActionId;
          }
        }
        formData.action_input.retry_times = originalRetry;
        formData.action_input.timeout = originalTimeout;
        formData.action_input.action_input = originalParams;
      } catch (error) {
        console.error("获取版本列表失败:", error);
      }
    }
  };

  // 处理版本选择
  const handleVersionChange = async (formData: FormData, version: string) => {
    // 重置动作
    actionOptions.value = [];

    // 保留原有的动作和配置
    const originalAction = formData.action_input.action_name;
    const originalActionId = formData.action_id;
    const originalRetry = formData.action_input.retry_times;
    const originalTimeout = formData.action_input.timeout;
    const originalParams = formData.action_input.action_input;

    if (version && selectedTool.value) {
      try {
        await toolActions(selectedTool.value.name, version);
        // 恢复原有的配置
        if (originalAction) {
          formData.action_input.action_name = originalAction;
          formData.action_id = originalActionId;
        }
        formData.action_input.retry_times = originalRetry;
        formData.action_input.timeout = originalTimeout;
        formData.action_input.action_input = originalParams;
        console.log(selectedTool.value);
      } catch (error) {
        console.error("获取动作列表失败:", error);
      }
    }
  };

  return {
    toolListData,
    isActionLoading,
    actionSearchKeyword,
    filteredToolList,
    versionOptions,
    actionOptions,
    isVersionLoading,
    isActionListLoading,
    selectedTool,
    isResourceLoading,
    resourceOptions,
    toolVersions,
    toolActions,
    handleToolChange,
    handleVersionChange,
    getToolList: fetchToolList,
    handleToolSearch,
    loadMoreTools,
    toolCurrentPage,
    toolTotalPage,
    onResourceChange
  };
};

// 剧本列表相关
export const usePlaybookList = () => {
  const currentPage = ref(1);
  const totalPage = ref(1);
  const isLoading = ref(false);
  const searchKeyword = ref("");
  const scriptOptions = ref<{ label: string; value: string }[]>([]);
  const playbookVersionOptions = ref<any[]>([]);

  // 查询参数
  const queryParams = {
    page: 1,
    size: 100
  };

  // 获取剧本列表
  const getScriptList = async () => {
    if (isLoading.value) return;
    isLoading.value = true;
    try {
      const res = (await apiGetPlaybookList({
        ...queryParams,
        page: currentPage.value,
        keyword: searchKeyword.value
      })) as any;
      const scriptList = (res.data.data || []).map(item => {
        const name = item.name;
        const remark = item.playbook_remark || item.remark;
        return {
          label: name,
          value: item.id,
          name,
          remark
        };
      });
      if (currentPage.value === 1) {
        scriptOptions.value = scriptList;
      } else {
        scriptOptions.value = [...scriptOptions.value, ...scriptList];
      }
      totalPage.value = res.data.total_pages;
    } catch (error) {
      console.error("获取列表失败:", error);
    } finally {
      isLoading.value = false;
    }
  };
  //获取剧本输入参数（现在用版本id）
  const getScriptInput = async (version_id: string) => {
    try {
      const inputRes = await apiGetPlaybookInput({ flow_id: version_id });
      return inputRes;
    } catch (error) {
      console.error("获取剧本输入参数失败:", error);
      return null;
    }
  };

  // 新增：获取剧本版本
  const getPlaybookVersions = async (playbookId: string) => {
    playbookVersionOptions.value = [];
    try {
      const res = (await apiGetPlaybookVersionsList({
        playbook_id: playbookId
      })) as any;
      if (res && res.data) {
        playbookVersionOptions.value = res.data
          .filter((item: any) => item.status !== 0)
          .map((item: any) => ({
            id: item.version_id,
            version: item.version,
            name: item.name,
            remark: item.remark,
            status: item.status
          }));
      }
    } catch (e) {
      console.error("获取剧本版本失败", e);
    }
    return playbookVersionOptions.value;
  };

  // 处理搜索
  const handleSearch = (query: string) => {
    searchKeyword.value = query;
    currentPage.value = 1;
    scriptOptions.value = [];
    getScriptList();
  };

  // 下拉加载更多
  const loadMoreScripts = () => {
    if (currentPage.value >= totalPage.value || isLoading.value) return;
    currentPage.value++;
    getScriptList();
  };

  return {
    currentPage,
    totalPage,
    isLoading,
    searchKeyword,
    scriptOptions,
    getScriptList,
    handleSearch,
    loadMoreScripts,
    getScriptInput,
    playbookVersionOptions,
    getPlaybookVersions
  };
};

// 定义提交数据类型
interface SubmitData {
  id?: string;
  name: string;
  type: number;
  status: number;
  tactics?: string;
  end_time?: string;
  playbook_id?: string;
  action_id?: string;
  playbook_input?: {
    flow_id: string;
    flow_input: Record<string, any>;
  };
  action_input?: {
    action_input: Record<string, any>;
    action_name: string;
    app_name: string;
    app_ver: string;
    retry_times: number;
    timeout: number;
    action: string;
    node_name: string;
    resource: string;
    main: string;
    name: string;
  };
}

// 处理表单提交
export const useFormSubmit = (
  formData: FormData,
  cronFields: Ref<string[]>,
  filteredToolList: Ref<any[]>
) => {
  const handleSubmit = async (isEdit: boolean) => {
    // 表单验证
    if (!formData.name) {
      ElMessage.error("请输入任务名称");
      return false;
    }

    // 根据类型验证必填项
    if (formData.type === 1) {
      // 工具动作验证
      if (!formData.action_input.app_name) {
        ElMessage.error("请选择工具");
        return false;
      }
      if (!formData.action_input.app_ver) {
        ElMessage.error("请选择工具版本");
        return false;
      }
      if (!formData.action_input.action_name) {
        ElMessage.error("请选择动作");
        return false;
      }

      // 验证动作参数
      const actionInputs = formData.action_input.action_input;
      for (const key in actionInputs) {
        if (actionInputs[key] === "") {
          ElMessage.error(`请输入参数: ${key}`);
          return false;
        }
      }
    } else {
      // 剧本验证
      if (!formData.playbook_id) {
        ElMessage.error("请选择剧本");
        return false;
      }

      // 验证剧本参数
      const flowInputs = formData.playbook_input.flow_input;
      for (const key in flowInputs) {
        if (flowInputs[key] === "") {
          ElMessage.error(`请输入参数: ${key}`);
          return false;
        }
      }
    }

    // 根据执行策略处理数据
    let submitData: SubmitData = {
      name: formData.name,
      type: formData.type,
      status: formData.status
    };

    // 如果是编辑模式，添加id字段
    if (isEdit && formData.id) {
      submitData.id = formData.id;
      // 编辑时不需要带name字段
      delete submitData.name;
    }

    // 新增校验：周期执行时必须填写结束时间
    if (formData.status === 1 && !formData.end_time) {
      ElMessage.error("周期执行时必须填写结束时间");
      return false;
    }

    // 根据执行策略处理数据
    if (formData.status === 0) {
      // 单次执行
      if (!formData.tactics) {
        ElMessage.error("请选择执行时间");
        return false;
      }
      submitData.tactics = formData.tactics;
      submitData.end_time = undefined;
    } else {
      // 周期执行
      submitData.tactics = cronFields.value.join(" ");
      submitData.end_time = formData.end_time;
    }

    // 根据类型添加不同的数据
    if (formData.type === 1) {
      // 通过工具名查找当前工具
      const tool = filteredToolList.value.find(
        item => item.name === formData.action_input.app_name
      );
      submitData.playbook_input = undefined;
      submitData.playbook_id = undefined;
      submitData.action_id = formData.action_id;
      submitData.action_input = {
        action_input: formData.action_input.action_input || {},
        action_name: formData.action_input.action_name,
        app_name: formData.action_input.app_name,
        app_ver: formData.action_input.app_ver,
        retry_times: formData.action_input.retry_times || 1,
        timeout: formData.action_input.timeout || 300,
        action: formData.action_input.action,
        node_name: formData.action_input.action_name,
        main: formData.action_input.main,
        name: tool?.description || "",
        resource: formData.action_input.resource
      };
    } else {
      // 剧本类型，只清除工具动作相关数据，保留基础配置
      const { action_input: _, ...restSubmitData } = submitData;
      submitData = {
        ...restSubmitData,
        playbook_input: {
          flow_id: formData.playbook_input.flow_id,
          flow_input: formData.playbook_input.flow_input || {}
        },
        playbook_id: formData.playbook_id
      };
    }

    try {
      if (isEdit) {
        console.log("submitData", submitData);
        const EditRes = (await updateTimed(submitData)) as any;
        if (EditRes.code === 0) {
          ElMessage.success("编辑成功");
          return true;
        } else {
          ElMessage.error("编辑失败");
          return false;
        }
      } else {
        const CreateRes = (await createTimed(submitData)) as any;
        if (CreateRes.code === 0) {
          ElMessage.success("新建成功");
          return true;
        } else {
          return false;
        }
      }
    } catch (error) {
      console.log("操作失败：", error);
      return false;
    }
  };

  return {
    handleSubmit
  };
};

// Cron表达式处理
export const useCronExpression = () => {
  const cronFields = ref(["*", "*", "*", "*", "*"]);

  const updateCronField = (value: string, index: number) => {
    cronFields.value[index] = value;
  };

  const nextExecutionTime = computed(() => {
    try {
      const cronExpression = cronFields.value.join(" ");
      if (cronExpression) {
        return cronstrue.toString(cronExpression, { locale: "zh_CN" });
      }
    } catch (e) {
      console.log("cron表达式处理失败：", e);
      return "";
    }
    return "";
  });

  return {
    cronFields,
    updateCronField,
    nextExecutionTime
  };
};

// 参数处理相关
export const useParams = () => {
  const paramKeys = ref<Record<string, string>>({});
  const paramTypes = ref<Record<string, string>>({});
  const paramRequired = ref<Record<string, boolean>>({});
  const paramDescriptions = ref<Record<string, string>>({});

  // 根据动作生成输入参数
  const generateActionParams = (formData: FormData, action: any) => {
    if (!action || !action.input || !Array.isArray(action.input)) {
      formData.action_input.action_input = {};
      paramKeys.value = {};
      paramTypes.value = {};
      paramRequired.value = {};
      paramDescriptions.value = {};
      return;
    }

    // 清空原有参数
    formData.action_input.action_input = {};
    paramKeys.value = {};
    paramTypes.value = {};
    paramRequired.value = {};
    paramDescriptions.value = {};

    // 根据动作的input参数生成输入框
    action.input.forEach((param: any) => {
      if (param && param.key) {
        formData.action_input.action_input[param.key] = "";
        paramKeys.value[param.key] = param.key;
        paramTypes.value[param.key] = param.type || "text";
        paramRequired.value[param.key] = param.required || false;
        paramDescriptions.value[param.key] = param.description || "";
      }
    });
  };

  // 根据剧本输入参数生成表单项
  const generatePlaybookParams = (formData, playbookInputData) => {
    formData.playbook_input.flow_input = {};
    paramKeys.value = {};
    paramTypes.value = {};
    paramRequired.value = {};
    paramDescriptions.value = {};

    playbookInputData.forEach(item => {
      const key = Object.keys(item)[0];
      if (key) {
        const param = item[key].input;
        if (param) {
          formData.playbook_input.flow_input[key] = "";
          paramKeys.value[key] = key;
          paramTypes.value[key] = param.type || "text";
          paramRequired.value[key] = param.required || false;
          paramDescriptions.value[key] = param.description || key;
        }
      }
    });
  };

  return {
    paramKeys,
    paramTypes,
    paramRequired,
    paramDescriptions,
    generateActionParams,
    generatePlaybookParams
  };
};
