<script lang="ts" setup>
import { useColumns } from "./columns";
import { message } from "@/utils/message";
import { editUser } from "@/api/userList";
import { handleChangeStatus, handleDelete } from "./User";
import { Search } from "@element-plus/icons-vue";
import { ref } from "vue";
import type { TabsPaneContext } from "element-plus";
import ChangePwdMod from "./components/ChangePwdMod.vue";
import NewUser from "./components/NewUser.vue";
import EditUser from "./components/editUser.vue";
import ApiUserComponent from "./apiUser.vue";
import IconifyIconOffline from "@/components/ReIcon/src/iconifyIconOffline";

const {
  columns,
  dataList,
  exportExcel,
  fetchUserData,
  totals,
  handleSizeChange
} = useColumns();

// 标签页激活名称
const activeName = ref("user");

// 标签页点击处理
const handleTabClick = (tab: TabsPaneContext) => {
  console.log(tab.props.name);
};

// 修改密码
const changePwdRef = ref();
const handleChangePassword = (row: any) => {
  changePwdRef.value?.openDialog(row);
};

// 新建用户
const newUserRef = ref();
const handleNewUser = (row: any) => {
  newUserRef.value?.openDialog(row);
};

const handleMfaEnable = (row: any) => {
  console.log(row.mfa_enable);
  editUser({
    username: row.username,
    mfa_enable: row.mfa_enable
  }).then((res: any) => {
    console.log(res);
    if (res.data.mfa_enable === false) {
      message("多因素认证关闭成功", { type: "success" });
    } else {
      message("多因素认证开启成功", { type: "success" });
    }
  });
};

// 编辑用户
const editUserRef = ref();
const handleEdit = (row: any) => {
  console.log("编辑", row);
  editUserRef.value?.openDialog(row);
};

// 分页
const Page = ref(1);
const size = ref(15);
const total = ref(totals);
const handleCurrentChange = (val: number) => {
  fetchUserData(val, size.value, input1.value);
};

//---------------------------------搜索---------------------------------
// 搜索
const input1 = ref("");
const handleSearch = () => {
  if (input1.value) {
    fetchUserData(1, size.value, input1.value);
  } else {
    fetchUserData(Page.value, size.value, "");
  }
};
// 清空搜索
const handleClearSearch = () => {
  input1.value = "";
  fetchUserData(Page.value, size.value, "");
};
</script>

<template>
  <div class="user-container">
    <el-card>
      <el-tabs
        v-model="activeName"
        class="demo-tabs"
        @tab-click="handleTabClick"
      >
        <el-tab-pane label="用户名" name="user">
          <div class="mb-[20px] flex items-center justify-between">
            <div>
              <!-- 搜索区域 -->
              <div class="search-area">
                <el-input
                  v-model="input1"
                  clearable

                  style="width: 340px"
                  @clear="handleClearSearch"
                >
                  <template #append>
                    <el-button @click="handleSearch">
                      <el-icon>
                        <Search />
                      </el-icon>
                    </el-button>
                  </template>
                </el-input>
              </div>
            </div>
            <div class="flex items-center gap-4">
              <!--              <el-button type="primary" @click="exportExcel">-->
              <!--                导出Excel-->
              <!--              </el-button>-->
              <Perms :value="['user:c']">
                <el-button type="primary" @click="handleNewUser">
                  新建
                </el-button>
              </Perms>
            </div>
          </div>

          <pure-table :columns="columns" :data="dataList" border row-key="id">
            <template #disabled="{ row }">
              <el-switch
                v-model="row.disabled"
                :active-value="false"
                :inactive-value="true"
                active-text="启用"
                inactive-text="禁用"
                inline-prompt
                @change="handleChangeStatus(row)"
              />
            </template>
            <template #mfa_enable="{ row }">
              <el-tooltip
                :hide-after="0"
                content="开启后，用户需要通过多因素认证才能登录"
                placement="top"
              >
                <el-switch
                  v-model="row.mfa_enable"
                  :active-value="true"
                  :inactive-value="false"
                  active-text="开启"
                  inactive-text="关闭"
                  inline-prompt
                  @change="handleMfaEnable(row)"
                />
              </el-tooltip>
            </template>
            <template #operation="{ row }">
              <el-tooltip :hide-after="0" content="编辑" placement="top">
                <el-button link type="primary" @click="handleEdit(row)">
                  <IconifyIconOffline
                    height="18"
                    icon="mingcute:edit-line"
                    width="18"
                  />
                </el-button>
              </el-tooltip>
              <Perms :value="['user:u']">
                <el-tooltip :hide-after="0" content="修改密码" placement="top">
                  <el-button
                    link
                    type="primary"
                    @click="handleChangePassword(row)"
                  >
                    <IconifyIconOffline
                      height="18"
                      icon="teenyicons:password-outline"
                      width="18"
                    />
                  </el-button>
                </el-tooltip>
              </Perms>
              <Perms :value="['user:d']">
                <el-tooltip :hide-after="0" content="删除" placement="top">
                  <span class="ml-3">
                    <el-popconfirm
                      cancel-button-text="取消"
                      confirm-button-text="确认"
                      title="确认要删除该用户吗？"
                      @confirm="
                        () =>
                          handleDelete(row, () =>
                            fetchUserData(Page, size, input1)
                          )
                      "
                    >
                      <template #reference>
                        <el-button link type="danger">
                          <IconifyIconOffline
                            height="18"
                            icon="icon-park-outline:delete"
                            width="18"
                          />
                        </el-button>
                      </template>
                    </el-popconfirm>
                  </span>
                </el-tooltip>
              </Perms>
            </template>
          </pure-table>
          <!-- 修改密码 -->
          <ChangePwdMod ref="changePwdRef" />
          <!-- 新建用户 -->
          <NewUser
            ref="newUserRef"
            @refresh="fetchUserData(Page, size, input1)"
          />
          <!-- 编辑用户 -->
          <EditUser
            ref="editUserRef"
            @refresh="fetchUserData(Page, size, input1)"
          />
          <!-- 分页 -->
          <div class="pagination-container">
            <div class="pagination-info" />
            <el-pagination
              v-model:current-page="Page"
              v-model:page-size="size"
              v-model:total="totals"
              :background="true"
              :page-sizes="[15, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @current-change="handleCurrentChange"
              @size-change="handleSizeChange"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane label="API账号" name="api">
          <ApiUserComponent />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.el-input {
  margin-bottom: 0;
}

.pagination-container {
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .pagination-info {
    color: #606266;
  }
}
</style>
