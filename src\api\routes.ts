import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

type Result = {
  code: number;
  data: Array<any>;
};

export const getAsyncRoutes = () => {
  return http.request<Result>("post", baseUrlApi("license/get"), {
    data: {}
  });
};

// import { http } from "@/utils/http";

// type Result = {
//   success: boolean;
//   data: Array<any>;
// };

// export const getAsyncRoutes = () => {
//   return http.request<Result>("get", "/get-async-routes");
// };
