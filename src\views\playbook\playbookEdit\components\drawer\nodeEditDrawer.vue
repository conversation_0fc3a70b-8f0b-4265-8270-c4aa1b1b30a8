<template>
  <div>
    <!-- 动作节点drawer -->
    <el-drawer
      v-if="nodeData?.type === 'action-node'"
      v-model="isShowDrawer"
      size="50%"
    >
      <template #header>
        <div class="node-edit-drawer-title">节点信息</div>
      </template>
      <template #default>
        <div class="node-edit-drawer-body">
          <!-- 节点通用信息 -->
          <el-form :model="nodeData">
            <el-form-item label="节点ID:">{{ nodeData.id }}</el-form-item>
            <!--            <el-form-item label="节点属性"-->
            <!--              >{{ nodeData.properties }}-->
            <!--            </el-form-item>-->
          </el-form>

          <!-- 动作节点 -->
          <el-form :model="nodeData.properties">
            <el-form-item>
              <!-- 绑定值为对象类型时，value-key必填 -->
              <el-select
                v-model="toolSelectedValue"
                value-key="id"
                @change="openActionDrawer"
              >
                <template #header>
                  <el-input
                    v-model="searchKeyword"
                    :suffix-icon="Search"
                    placeholder="搜索工具"
                    @change="getToolList()"
                  />
                </template>
                <el-option
                  v-for="item in toolList.events"
                  :key="item.id"
                  :label="item.description"
                  :value="item"
                >
                  <span style="float: left">{{ item.description }}</span>
                  <span
                    style="
                      float: right;
                      color: var(--el-text-color-secondary);
                      font-size: 13px;
                    "
                  >
                    {{ item.name }}
                  </span>
                </el-option>
                <template #footer>
                  <el-pagination
                    v-model:current-page="toolCurrentPage"
                    v-model:page-size="toolPageSize"
                    :page-sizes="[15, 50, 100]"
                    :total="toolList.total"
                    layout="total, sizes, prev, pager, next, jumper"
                    size="small"
                    @size-change="getToolList()"
                    @current-change="getToolList()"
                  />
                </template>
              </el-select>
            </el-form-item>
          </el-form>

          <!-- 动作节点嵌套drawer -->
          <el-drawer
            v-model="isShowActionDrawer"
            :append-to-body="true"
            size="40%"
            title="请选择版本以及动作"
          >
            <el-form>
              <el-form-item label="请选择版本:">
                <el-select
                  v-model="versionSelectedValue"
                  value-key="id"
                  @change="getActionList"
                >
                  <el-option
                    v-for="item in versionList"
                    :key="item.id"
                    :label="item.version"
                    :value="item"
                  />
                </el-select>
              </el-form-item>
              <el-form-item v-if="actionList">
                <el-row>
                  <el-col v-for="(item, index) in actionList" :key="index">
                    <el-button
                      link
                      type="primary"
                      @click="openSetActionDrawer(item)"
                      >{{ item.description }}
                    </el-button>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-form>
            <el-drawer
              v-model="isShowSetActionDrawer"
              :append-to-body="true"
              size="30%"
              title="配置动作"
            >
              <template #default>
                <div>
                  <div>
                    <h3>
                      {{ setActionList.action }}
                    </h3>
                    <h3>
                      {{ setActionList.description }}
                    </h3>
                  </div>
                  <el-form>
                    <el-form-item label="节点标题:">
                      <el-input v-model="nodeData.properties.data.nodeTitle" />
                    </el-form-item>
                    <el-form-item>
                      <el-tabs v-model="activeName" class="demo-tabs">
                        <el-tab-pane label="输入参数" name="first">
                          <el-form>
                            <!-- v-for中，先后顺序决定值，和名称无关 -->
                            <el-form-item
                              v-for="(value, key) in setActionList.input"
                              :key="key"
                            >
                              <div>{{ value.description }}</div>
                              <el-input
                                v-model="nodeData.properties.data[key]"
                              />
                            </el-form-item>
                          </el-form>
                        </el-tab-pane>
                        <el-tab-pane label="输出参数" name="second">
                          <el-form>
                            <el-form-item>
                              <el-row
                                v-for="(item, index) in setActionList.output"
                                :key="index"
                              >
                                <el-col>{{ item.description }}</el-col>
                                <el-col>{{ item.data_path }}</el-col>
                              </el-row>
                            </el-form-item>
                          </el-form>
                        </el-tab-pane>
                      </el-tabs>
                    </el-form-item>
                  </el-form>
                </div>
              </template>
              <template #footer>
                <div>
                  <el-button @click="closeNodeEditDrawer()">取消</el-button>
                  <el-button type="primary" @click="nodeEditConfirm()"
                    >保存
                  </el-button>
                </div>
              </template>
            </el-drawer>
          </el-drawer>
        </div>
      </template>
    </el-drawer>

    <!-- 判断节点drawer -->
    <el-drawer
      v-if="nodeData?.type === 'judge-node'"
      v-model="isShowDrawer"
      size="50%"
    >
      <template #header>
        <div>配置规则</div>
      </template>
      <template #default>
        <div class="judge-node-drawer-body">
          <el-card shadow="never">
            <el-form>
              <el-form-item label="节点标题:">
                <el-input v-model="nodeData.properties.data.nodeTitle" />
              </el-form-item>
              <el-form-item>
                <div class="judge-rule">
                  <div class="judge-step">1</div>
                  <div>规则编辑</div>
                </div>
                <div class="judge-line" />
                <div class="judge-rule">
                  <div class="judge-step">2</div>
                  <div>规则入参配置</div>
                </div>
              </el-form-item>
              <el-form-item>
                <el-tabs class="judge-tabs" type="border-card">
                  <el-tab-pane>
                    <template #label>
                      <div class="judge-tabs-label">
                        <div>规则列表</div>
                        <div>
                          <el-icon>
                            <QuestionFilled />
                          </el-icon>
                        </div>
                      </div>
                    </template>
                    新增规则
                  </el-tab-pane>
                </el-tabs>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </template>
      <template #footer>
        <div>
          <el-button>取消</el-button>
          <el-button type="primary">下一步</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
import LogicFlow from "@logicflow/core";
import { onMounted, ref } from "vue";
import {
  apiGetActionList,
  apiGetToolList,
  apiGetToolVersionsList
} from "@/api/playbook";
import { QuestionFilled, Search } from "@element-plus/icons-vue";

const props = defineProps({
  lf: LogicFlow
});
const isShowDrawer = ref(false);
const isShowActionDrawer = ref(false);
const isShowSetActionDrawer = ref(false);
const nodeData = ref<LogicFlow.NodeData>();
const toolList = ref<any>([]);
const versionList = ref();
const versionSelectedValue = ref();
const actionList = ref([]);
const testList = ref([]);
const setActionList = ref<any>([]);
const activeName = ref("first");
const toolSelectedValue = ref();
const toolCurrentPage = ref(1); //工具页码
const toolPageSize = ref(15); //工具一页展示的数量
const searchKeyword = ref(); //搜索关键词

onMounted(() => {
  getToolList();
});

//打开抽屉
const openNodeEditDrawer = async (data: LogicFlow.NodeData) => {
  toolSelectedValue.value = "";
  isShowDrawer.value = !isShowDrawer.value;
  nodeData.value = data;
  //判断该节点是新节点还是已有数据节点
  if (nodeData.value.properties.data) {
    console.log(nodeData.value);
  } else {
    nodeData.value.properties.data = {};
  }
  console.log(nodeData.value);
};

//关闭抽屉
const closeNodeEditDrawer = () => {
  isShowDrawer.value = false;
};

//请求动作节点的所有工具
const getToolList = async (pager = 1) => {
  toolCurrentPage.value = pager;
  let res: any = await apiGetToolList({
    page: toolCurrentPage.value,
    size: toolPageSize.value,
    key: searchKeyword.value
  });
  toolList.value = res.data;
  console.log("请求全部工具");
  console.log(toolList.value);
};

//打开工具版本选择和动作选择drawer并请求数据
const openActionDrawer = async item => {
  console.log(toolSelectedValue.value);
  isShowActionDrawer.value = true;
  //获取具体工具的所有版本列表
  let res: any = await apiGetToolVersionsList({ name: item.name });
  versionList.value = res.data;
  //保存用户选择的工具的main和description到properties
  nodeData.value.properties.main = item.main;
  nodeData.value.properties.name = item.description;
  nodeData.value.properties.app_name = item.name;
  //下拉多选框的默认版本为最新版本
  versionSelectedValue.value = res.data[0].version;
  //默认获取并展示该工具最新版本的动作
  const { name, version } = res.data[0];
  //请求工具对应版本的动作列表数据
  getActionList({ name: name, version: version });
  console.log("versionList");
  console.log(versionList.value);
};

//获取工具当前版本的所有动作列表
const getActionList = async item => {
  let res: any = await apiGetActionList({
    name: item.name,
    version: item.version
  });
  actionList.value = res.data.actions;
  console.log(actionList.value);
  //保存用户选择的工具的版本到properties
  nodeData.value.properties.version = item.version;
};

//打开配置动作drawer
const openSetActionDrawer = item => {
  isShowSetActionDrawer.value = true;
  setActionList.value = item;
  if (Object.keys(nodeData.value.properties.data).length < 1) {
    for (const key in setActionList.value.input) {
      nodeData.value.properties.data[key] = "";
    }
  }

  console.log("setActionList");
  console.log(setActionList.value);
  console.log(Object.keys(setActionList.value.input));
};

//配置动作drawer确认按钮
const nodeEditConfirm = () => {
  //将用户的输入结果保存到节点的properties的data上
  // 遍历setActionList的input对象的键值对
  // for (const key in setActionList.value.input) {
  //   if (setActionList.value.input.hasOwnProperty(key)) {
  //     const defaultValue = setActionList.value.input[key].default_value;
  //     testList.value.push({ key, value: defaultValue }); // 将 default_value 添加到 testList
  //     nodeData.value.properties.data[key] = defaultValue;
  //   }
  // }
  // console.log("testList");
  // console.log(testList.value);
  // //处理数据格式
  // const setActionInput = testList.value.reduce((acc, { key, value }) => {
  //   acc[key] = value;
  //   return acc;
  // }, {});

  //将动作信息保存到节点的properties的data上
  nodeData.value.properties.data.node_name = versionList.value[0].description;
  nodeData.value.properties.data.action = setActionList.value.action;
  nodeData.value.properties.data.action_name = setActionList.value.description;
  //推送数据到节点的propert上并更新
  props.lf.setProperties(nodeData.value.id, nodeData.value.properties!);
  // isShowDrawer.value = false;
  isShowActionDrawer.value = false;
  isShowSetActionDrawer.value = false;
  console.log("submit!");
  console.log(props.lf.getGraphData());
};

defineExpose({
  openNodeEditDrawer,
  closeNodeEditDrawer
});
</script>

<style lang="scss" scoped>
.judge-node-drawer-body {
  .judge-rule {
    display: flex;
    justify-content: center;
    align-items: center;

    .judge-step {
      width: 24px;
      height: 24px;
      border: 1px solid #ccc;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 5px;
      background: #1890ff;
      color: white;
    }
  }

  .judge-line {
    flex-grow: 1; /* 横线自动填充剩余空间 */
    height: 1px; /* 横线的高度 */
    background-color: #ccc; /* 横线的颜色 */
    margin: 0 10px; /* 横线和数字之间的空隙 */
  }

  .judge-tabs {
    width: 100%;

    .judge-tabs-label {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
