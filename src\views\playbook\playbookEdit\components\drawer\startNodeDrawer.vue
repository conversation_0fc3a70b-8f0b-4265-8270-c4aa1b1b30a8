<template>
  <div>
    <el-drawer v-model="isShowDrawer" size="50%">
      <template #header>
        <div>开始节点</div>
      </template>
      <template #default>
        <div>
          <el-form>
            <el-form-item label="节点ID:">
              <el-button
                type="primary"
                link
                @click="$_copyNodeId(nodeData.id)"
                >{{ nodeData.id }}</el-button
              >
            </el-form-item>
          </el-form>
          <div class="start-addparameter">
            <div>剧本输入参数</div>
            <el-button type="primary" @click="addStartParameter()">
              添加参数
              <el-icon class="el-icon--right">
                <Plus />
              </el-icon>
            </el-button>
          </div>
        </div>
        <el-table :data="startTableData">
          <el-table-column align="center" label="参数名">
            <template #default="scope">
              <div>
                <el-input v-model="scope.row.name"  />
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="参数类型">
            <template #default="scope">
              <div>
                <el-select v-model="scope.row.type">
                  <el-option
                    v-for="(item, index) in startParameterType()"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="参数描述">
            <template #default="scope">
              <div>
                <el-input
                  v-model="scope.row.desc"

                />
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作">
            <template #default="scope">
              <div class="operation">
                <el-tooltip :hide-after="0" content="复制参数" placement="top">
                  <div
                    link
                    type="danger"
                    style="margin-right: 10px; cursor: pointer"
                    @click="$_copyStartNodeParameter(scope.$index)"
                  >
                    <svg
                      class="icon"
                      height="20"
                      p-id="2839"
                      t="1748240616953"
                      version="1.1"
                      viewBox="0 0 1024 1024"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M720 192h-544A80.096 80.096 0 0 0 96 272v608C96 924.128 131.904 960 176 960h544c44.128 0 80-35.872 80-80v-608C800 227.904 764.128 192 720 192z m16 688c0 8.8-7.2 16-16 16h-544a16 16 0 0 1-16-16v-608a16 16 0 0 1 16-16h544a16 16 0 0 1 16 16v608z"
                        fill="#2C7FE4"
                        p-id="2840"
                      />
                      <path
                        d="M848 64h-544a32 32 0 0 0 0 64h544a16 16 0 0 1 16 16v608a32 32 0 1 0 64 0v-608C928 99.904 892.128 64 848 64z"
                        fill="#2C7FE4"
                        p-id="2841"
                      />
                      <path
                        d="M608 360H288a32 32 0 0 0 0 64h320a32 32 0 0 0 0-64zM608 520H288a32 32 0 1 0 0 64h320a32 32 0 1 0 0-64zM480 678.656H288a32 32 0 1 0 0 64h192a32 32 0 1 0 0-64z"
                        fill="#2C7FE4"
                        p-id="2842"
                      />
                    </svg>
                  </div>
                </el-tooltip>
                <el-tooltip :hide-after="0" content="删除" placement="top">
                  <el-button
                    link
                    type="danger"
                    @click="deleteStartParams(scope.$index)"
                  >
                    <Icon height="20" icon="uiw:delete" width="20" />
                  </el-button>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #footer>
        <div>
          <el-button @click="closeNodeEditDrawer">取消</el-button>
          <el-button type="primary" @click="nodeEditConfirm()">保存</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
import LogicFlow from "@logicflow/core";
import { ref } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { Icon } from "@iconify/vue";
import usePlaybookStore from "@/store/modules/playbook";
import { ElMessage } from "element-plus";

interface startTableType {
  name: string;
  type: any;
  desc: string;
}

const props = defineProps({
  lf: LogicFlow
});
const isShowDrawer = ref(false);
const nodeData = ref<LogicFlow.NodeData>();
const startTableData = ref<startTableType[]>([]);
const playbookStore = usePlaybookStore();

//打开抽屉
const openNodeEditDrawer = async (data: LogicFlow.NodeData) => {
  isShowDrawer.value = !isShowDrawer.value;
  nodeData.value = data;
  if (nodeData.value.properties.start_params) {
    startTableData.value = nodeData.value.properties.start_params;
  }
  console.log(nodeData.value);
};

//关闭抽屉
const closeNodeEditDrawer = () => {
  isShowDrawer.value = false;
};

//值类型
const startParameterType = () => {
  return [
    { label: "字符串", value: "string" },
    { label: "数字", value: "number" },
    { label: "日期", value: "date" },
    { label: "布尔", value: "boolean" },
    { label: "Json数组", value: "jsonArray" },
    { label: "Json对象", value: "jsonObject" }
  ];
};

//添加自定义聚合参数
const addStartParameter = () => {
  const newRow = {
    name: "",
    type: "string",
    desc: ""
  };
  startTableData.value.push(newRow);
};

//删除自定义参数
const deleteStartParams = index => {
  startTableData.value.splice(index, 1);
};

// 复制开始节点参数
const $_copyStartNodeParameter = async (index: any) => {
  try {
    // 使用现代Clipboard API替代已废弃的execCommand
    await navigator.clipboard.writeText(
      `\$\{${nodeData.value.id}.output.${startTableData.value[index].name}\}`
    );
  } catch (err) {
    console.error("复制失败:", err);
    // 添加失败反馈（可根据项目需求替换为错误提示）
  }
};

//确认并保存数据到nodeData的properties上
const nodeEditConfirm = () => {
  isShowDrawer.value = false;
  nodeData.value.properties.start_params = startTableData.value;
  playbookStore.startNodeParams = startTableData.value;
  console.log("playbookStore.startNodeParams:", playbookStore.startNodeParams);
  //推送数据到节点的properties上并更新(很重要，没送上来你前面怎么改都没用)
  props.lf.setProperties(nodeData.value.id, nodeData.value.properties!);
  console.log(props.lf.getGraphData());
};

// 复制节点ID
const $_copyNodeId = async (id: string) => {
  try {
    // 使用现代Clipboard API替代已废弃的execCommand
    await navigator.clipboard.writeText(`\$\{${id}.output\}`);
    ElMessage.success("已复制该节点ID");
  } catch (err) {
    console.error("复制失败:", err);
    // 添加失败反馈（可根据项目需求替换为错误提示）
  }
};

//对外暴露方法
defineExpose({
  openNodeEditDrawer
});
</script>

<style lang="scss" scoped>
.start-addparameter {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
.operation {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
