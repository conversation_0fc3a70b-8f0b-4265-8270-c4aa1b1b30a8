import { HtmlNode, HtmlNodeModel } from "@logicflow/core";
import { createApp, h } from "vue";
import StartNode from "@/views/playbook/playbookEdit/icon/StartNode.vue";

class StartNodeView extends HtmlNode {
  isMounted: boolean;
  r: any;
  app: any;

  constructor(props) {
    super(props);
    this.isMounted = false;
    this.r = h(StartNode, {
      id: props.model.id,
      properties: props.model.getProperties(),
      text: props.model.inputData,
      model: props.model,
      graphModel: props.graphModel,
      onBtnCopyClick: () => {
        props.graphModel.eventCenter.emit("custom:onBtnCopyClick", {
          props: this.props
        });
      },
      onBtnDelClick: () => {
        props.graphModel.eventCenter.emit("custom:onBtnDelClick", {
          props: this.props
        });
      }
    });
    this.app = createApp({
      render: () => this.r
    });
  }

  shouldUpdate() {
    if (this.preProperties && this.preProperties === this.currentProperties)
      return;
    this.preProperties = this.currentProperties;
    return true;
  }

  setHtml(rootEl) {
    if (!this.isMounted) {
      this.isMounted = true;
      const node = document.createElement("div");
      rootEl.appendChild(node);
      this.app.mount(node);
    } else {
      this.r.component.props.properties = this.props.model.getProperties();
    }
  }
}

class StartNodeModel extends HtmlNodeModel {
  setAttributes() {
    this.width = 100;
    this.height = 100;
    this.text.editable = false;
    this.inputData = this.text.value;
  }

  // 定义开始节点只有右锚点. 锚点位置通过中心点和宽度算出来。
  getDefaultAnchor() {
    const { width, x, y, id } = this;
    return [
      {
        x: x + width / 2,
        y,
        name: "right",
        id: `${id}_0`
      }
    ];
  }

  getConnectedTargetRules() {
    const rules = super.getConnectedTargetRules();
    const notAsTarget = {
      message: "起始节点不能作为边的终点",
      validate: () => false
    };
    rules.push(notAsTarget);
    return rules;
  }

  getOutlineStyle() {
    const style = super.getOutlineStyle();
    style.stroke = "none";
    style.hover.stroke = "red";
    return style;
  }
}

export default {
  type: "start-node",
  view: StartNodeView,
  model: StartNodeModel
};
