// 全局变量接口
import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

// 获取全局变量列表
export const getVarList = (data: any) => {
  return http.post(baseUrlApi("var/list"), { data });
};

// 更新全局变量
export const updateVar = (data: any) => {
  return http.post(baseUrlApi("var/update"), { data });
};

// 删除全局变量
export const deleteVar = (data: any) => {
  return http.post(baseUrlApi("var/del"), { data });
};

//新建全局变量
export const addVar = (data: any) => {
  return http.post(baseUrlApi("var/new"), { data });
};
