<template>
  <div class="warroom-wrapper">
    <div class="chat-container">
      <!-- 左侧房间列表 -->
      <div class="room-list-container">
        <RoomList
          :is-floating="isFloating"
          :rooms="rooms"
          :user-rooms="userRooms"
          :ws="ws"
          @room-changed="handleRoomChanged"
        />
      </div>

      <template v-if="currentRoomId && chatType">
        <!-- 中间聊天区域 -->
        <div
          :style="{ width: isSidebarCollapsed ? '85%' : '70%' }"
          class="chat-center-container"
        >
          <!-- 消息列表 -->
          <div class="message-list-container">
            <MessageList
              ref="messageList"
              :chat-type="chatType"
              :is-floating="isFloating"
              :is-sidebar-collapsed="isSidebarCollapsed"
              :messages="filteredMessages"
              :partner-id="selectedUser?.id"
              :partner-name="selectedUser?.username"
              :room-id="currentRoomId"
              @recall="chatStore.handleRecall"
              @toggle-sidebar="toggleSidebar"
              @back-to-welcome="handleBackToWelcomePage"
            />
          </div>
          <!-- 消息输入框 -->
          <div class="message-input-container">
            <MessageInput
              :chat-type="chatType"
              :partner-id="selectedUser?.id"
              @send="chatStore.handleSendMessage"
            />
          </div>
        </div>
        <!-- 右侧用户列表 -->
        <div
          v-show="!isSidebarCollapsed"
          :style="{ width: isSidebarCollapsed ? '0' : '15%' }"
          class="user-list-container"
        >
          <UserList
            :chat-type="chatType"
            :room-id="currentRoomId"
            :selected-id="selectedUser?.id"
            :users="users"
            @select-user="handleSelectUser"
          />
        </div>
      </template>

      <template v-else>
        <!-- 默认进入欢迎页面 -->
        <div class="welcome-container">
          <WelcomePage />
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
import MessageList from "@/views/warroom/warChat/MessageList.vue";
import MessageInput from "@/views/warroom/warChat/MessageInput.vue";
import UserList from "@/views/warroom/warChat/UserList.vue";
import RoomList from "@/views/warroom/warChat/RoomList.vue";
import WelcomePage from "@/views/warroom/warChat/WelcomePage.vue";
import { nextTick, onMounted, onUnmounted, ref, watch } from "vue";
import { useChatStore } from "@/store/warChat";
import { useUserStore } from "@/store/modules/user";
import { storeToRefs } from "pinia";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { getPrivateRoom } from "@/api/warroom";

defineOptions({
  name: "warroom"
});

// 新增isFloating属性
const props = defineProps({
  isFloating: {
    type: Boolean,
    default: false
  }
});

const userStore = useUserStore();
const route = useRoute();
const chatStore = useChatStore();
const {
  ws,
  rooms,
  userRooms,
  currentRoomId,
  chatType,
  users,
  selectedUser,
  filteredMessages,
  isLoadingMessages
} = storeToRefs(chatStore);

const isSidebarCollapsed = ref(false);
const hasInitialized = ref(false);
const isHandlingRouteChange = ref(false);
const onlineStatusInterval = ref<number | null>(null);
const initialRoomLoaded = ref(false); // 标记初始房间是否已加载
const messageList = ref(null); // 添加消息列表引用

const toggleSidebar = () => {
  isSidebarCollapsed.value = !isSidebarCollapsed.value;
};

// 新增处理房间切换的方法
const handleRoomChanged = (roomId: string) => {
  if (props.isFloating) {
    // 在悬浮窗模式下直接处理房间切换逻辑
    processRoomChange(roomId);
  } else {
    // 在普通模式下使用路由导航
    chatStore.handleRoomChanged(roomId);
  }
};

// 处理房间切换的逻辑
const processRoomChange = async (newRoomId: string) => {
  if (!newRoomId) {
    chatType.value = undefined;
    selectedUser.value = null;
    currentRoomId.value = "";
    return;
  }

  isLoadingMessages.value = true;
  try {
    currentRoomId.value = newRoomId;
    const room = rooms.value.find(r => r.id === currentRoomId.value);
    chatStore.clearRoomMessages();
    // 根据房间名字判断私聊
    if (room?.name?.includes("私聊")) {
      chatType.value = "private";
      selectedUser.value = null;
      await chatStore.fetchRoomUsers(currentRoomId.value);
      // 等待 nextTick，确保 users.value 已更新
      await nextTick();
      const roomUsers = users.value.filter(u => u.id !== userStore.$state.id);
      if (roomUsers.length > 0) {
        selectedUser.value = roomUsers[0];
        chatStore.requestPrivateHistory(
          undefined,
          undefined,
          currentRoomId.value
        );
      }
    } else {
      chatType.value = "public";
      selectedUser.value = null;
      await chatStore.fetchRoomUsers(currentRoomId.value);
      // 只查公聊历史
      chatStore.requestPublicHistory(undefined, undefined, currentRoomId.value);
    }

    if (ws.value?.readyState === WebSocket.OPEN) {
      chatStore.requestOnlineUsers(currentRoomId.value);
    }
  } finally {
    isLoadingMessages.value = false;
  }
};

// 定期请求在线状态更新
const startOnlineStatusPolling = () => {
  if (onlineStatusInterval.value) {
    clearInterval(onlineStatusInterval.value);
  }
  onlineStatusInterval.value = window.setInterval(() => {
    if (ws.value?.readyState === WebSocket.OPEN && currentRoomId.value) {
      chatStore.requestOnlineUsers(currentRoomId.value);
    }
  }, 30000);
};

const initializeWarRoom = async () => {
  if (hasInitialized.value) {
    return;
  }
  hasInitialized.value = true;
  // 只在首次初始化时连接WebSocket
  if (!ws.value || ws.value.readyState !== WebSocket.OPEN) {
    chatStore.connectWebSocket();
  }
  startOnlineStatusPolling();
};

onMounted(async () => {
  await initializeWarRoom();

  // 在悬浮窗模式下不通过路由初始化
  if (props.isFloating && !initialRoomLoaded.value) {
    initialRoomLoaded.value = true;
  }
});

onUnmounted(() => {
  // 不要在卸载时关闭WebSocket，可能会导致重新连接问题
  if (onlineStatusInterval.value) {
    clearInterval(onlineStatusInterval.value);
    onlineStatusInterval.value = null;
  }
});

watch(
  () => ws.value?.readyState,
  newState => {
    if (newState === WebSocket.OPEN) {
      startOnlineStatusPolling();
    }
  }
);

// 仅在非悬浮窗模式下监听路由变化
watch(
  () => route.params.roomId,
  async (newRoomId, oldRoomId) => {
    if (props.isFloating) return;
    if (
      isHandlingRouteChange.value ||
      (newRoomId === oldRoomId && initialRoomLoaded.value) ||
      newRoomId === currentRoomId.value
    ) {
      return;
    }

    if (!newRoomId) {
      chatType.value = undefined;
      selectedUser.value = null;
      currentRoomId.value = "";
      initialRoomLoaded.value = true;
      return;
    }

    isHandlingRouteChange.value = true;
    isLoadingMessages.value = true;
    try {
      // 使用processRoomChange处理，保持逻辑一致
      await processRoomChange(newRoomId as string);
      initialRoomLoaded.value = true;
    } finally {
      isHandlingRouteChange.value = false;
      isLoadingMessages.value = false;
    }
  },
  { immediate: true }
);

// 处理选择用户
const handleSelectUser = user => {
  if (props.isFloating) {
    // 悬浮窗模式下直接处理用户选择逻辑
    processSelectUser(user);
  } else {
    // 普通模式下也使用相同的处理逻辑，确保消息类型一致
    processSelectUser(user);
  }
};

// 处理选择用户的逻辑
const processSelectUser = async user => {
  if (chatType.value === "private" && selectedUser.value?.id === user.id) {
    return;
  }

  const savedRoomId = currentRoomId.value;
  chatType.value = "private";
  selectedUser.value = user;

  try {
    const response = (await getPrivateRoom({
      partner_id: user.id
    })) as any;

    if (response.code === 0 && response.data) {
      const privateRoomId = response.data.room_id;
      const privateName = response.data.name;

      if (privateRoomId) {
        if (privateRoomId === currentRoomId.value) {
          return;
        }

        // 更新rooms集合，添加或更新私聊房间
        const roomIndex = rooms.value.findIndex(r => r.id === privateRoomId);
        if (roomIndex >= 0) {
          rooms.value[roomIndex].name = privateName;
        } else {
          rooms.value.push({
            id: privateRoomId,
            name: privateName,
            creator_id: userStore.$state.id
          });
        }

        currentRoomId.value = privateRoomId;
        chatStore.requestPrivateHistory(undefined, undefined, privateRoomId);

        // 确保获取到正确的房间用户列表
        await chatStore.fetchRoomUsers(privateRoomId);
      } else {
        currentRoomId.value = savedRoomId;
        ElMessage.error("无法创建私聊房间，请稍后再试");
      }
    } else {
      currentRoomId.value = savedRoomId;
      ElMessage.error(response.message || "获取私聊房间失败");
    }
  } catch {
    currentRoomId.value = savedRoomId;
    chatType.value = "public";
    selectedUser.value = null;
    ElMessage.error("获取私聊房间失败，请稍后再试");
    return;
  }
  if (!chatStore.privateMessages[user.id]) {
    chatStore.privateMessages[user.id] = [];
  }

  await nextTick();
  scrollToBottom();
};

// 处理返回到欢迎页面
const handleBackToWelcomePage = () => {
  if (props.isFloating) {
    // 悬浮窗模式下直接重置状态
    chatType.value = undefined;
    selectedUser.value = null;
    currentRoomId.value = "";
    chatStore.clearRoomMessages();
  } else {
    // 普通模式下使用chatStore的方法（内部会使用路由导航）
    chatStore.backToWelcomePage();
  }
};

// 滚动到底部
const scrollToBottom = () => {
  setTimeout(() => {
    messageList.value?.scrollToBottom?.();
  }, 0);
};
</script>

<style scoped>
.warroom-wrapper {
  height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
}

.chat-container {
  display: flex;
  flex: 1;
  width: 100%;
  min-height: 0;
  border-radius: 8px;
  max-height: calc(100vh - 100px);
  overflow: hidden;
}

.room-list-container {
  width: 15%;
  height: 100%;
  background-color: #fff;
  border-right: 1px solid #ebeef5;
}

.chat-center-container {
  width: 70%;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
}

.user-list-container {
  width: 15%;
  height: 100%;
  border-left: 1px solid #ebeef5;
  transition:
    width 0.3s ease,
    opacity 0.3s ease;
  overflow: hidden;
}

.message-list-container {
  height: 78%;
  min-height: 0;
  overflow: hidden;
}

.message-input-container {
  height: 22%;
  border-top: 1px solid #ebeef5;
}

.welcome-container {
  width: 85%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.announcement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.header-right {
  display: flex;
  align-items: center;
}

.delete-button {
  font-size: 18px;
  color: #999;
  padding: 0;
  width: 20px;
  height: 20px;
  line-height: 20px;
  border: none;
  background: none;
}

.delete-button:hover {
  color: #ff4d4f;
}

.announcement-item {
  margin: 15px 0;
  padding: 20px;
  border-radius: 8px;
  background-color: #f8f9fa;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.announcement-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.announcement-content {
  font-size: 1.1em;
  line-height: 1.6;
  padding: 10px 0;
  color: #333;
}

:deep(.el-overlay) {
  position: fixed;
  inset: 0;
  z-index: 2000;
}

:deep(.el-overlay-dialog) {
  overflow: hidden;
}

:deep(.el-dialog) {
  margin: 100px auto !important;
}
</style>
