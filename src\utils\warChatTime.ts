export const formatTime = (timestamp: string) => {
  const now = new Date();
  const msgDate = new Date(timestamp);
  const todayStart = new Date(
    now.getFullYear(),
    now.getMonth(),
    now.getDate()
  ).getTime();
  const msgDayStart = new Date(
    msgDate.getFullYear(),
    msgDate.getMonth(),
    msgDate.getDate()
  ).getTime();
  const diffDays = Math.floor(
    (todayStart - msgDayStart) / (1000 * 60 * 60 * 24)
  );
  const timeStr = msgDate.toLocaleTimeString([], {
    hour: "2-digit",
    minute: "2-digit",
    hour12: false
  });
  if (diffDays === 0) {
    return timeStr;
  } else if (diffDays === 1) {
    return `昨天 ${timeStr}`;
  } else if (diffDays < 7) {
    const weekdays = [
      "星期日",
      "星期一",
      "星期二",
      "星期三",
      "星期四",
      "星期五",
      "星期六"
    ];
    return `${weekdays[msgDate.getDay()]} ${timeStr}`;
  } else {
    return msgDate
      .toLocaleString([], {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        hour12: false
      })
      .replace(/\//g, "-");
  }
};
