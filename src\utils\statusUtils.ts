// 执行状态常量
export const EXECUTION_STATUS = {
  SUCCESS: 0,
  RUNNING: 1,
  WAITING: 2,
  TIMEOUT: 3,
  FAIL: -1
} as const;

export type ExecutionStatus =
  (typeof EXECUTION_STATUS)[keyof typeof EXECUTION_STATUS];

// 状态样式类映射
export const getStatusClass = (
  status: ExecutionStatus | string | number
): string => {
  const numStatus = Number(status);

  switch (numStatus) {
    case EXECUTION_STATUS.SUCCESS:
      return "success";
    case EXECUTION_STATUS.RUNNING:
    case EXECUTION_STATUS.WAITING:
      return "running";
    case EXECUTION_STATUS.TIMEOUT:
    case EXECUTION_STATUS.FAIL:
      return "fail";
    default:
      return "fail";
  }
};

// 状态文本映射
export const getStatusText = (
  status: ExecutionStatus | string | number
): string => {
  const numStatus = Number(status);

  switch (numStatus) {
    case EXECUTION_STATUS.SUCCESS:
      return "成功";
    case EXECUTION_STATUS.RUNNING:
      return "执行中";
    case EXECUTION_STATUS.WAITING:
      return "等待中";
    case EXECUTION_STATUS.TIMEOUT:
      return "超时";
    case EXECUTION_STATUS.FAIL:
      return "失败";
    default:
      return "失败";
  }
};

// 检查是否为成功状态
export const isSuccessStatus = (
  status: ExecutionStatus | string | number
): boolean => {
  return Number(status) === EXECUTION_STATUS.SUCCESS;
};

// 检查是否为失败状态
export const isFailStatus = (
  status: ExecutionStatus | string | number
): boolean => {
  const numStatus = Number(status);
  return (
    numStatus === EXECUTION_STATUS.FAIL ||
    numStatus === EXECUTION_STATUS.TIMEOUT
  );
};

// 检查是否为运行中状态
export const isRunningStatus = (
  status: ExecutionStatus | string | number
): boolean => {
  const numStatus = Number(status);
  return (
    numStatus === EXECUTION_STATUS.RUNNING ||
    numStatus === EXECUTION_STATUS.WAITING
  );
};
