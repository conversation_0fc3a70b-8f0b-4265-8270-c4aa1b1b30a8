<template>
  <div class="loading-container">
    <div class="loading-content">
      <div class="avatar-box">
        <avatar class="avatar-img" />
      </div>
      <h2>正在登录中...</h2>
      <p>{{ statusMessage }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { setToken } from "@/utils/auth";
import { useUserStoreHook } from "@/store/modules/user";
import { useRefreshTokenStore } from "@/store/modules/RefreshToken";
import { avatar } from "../login/utils/static";
const router = useRouter();
const route = useRoute();
const statusMessage = ref("正在处理登录信息...");

// 解析URL参数
const getQueryParams = () => {
  // 获取完整的URL
  const url = window.location.href;
  const hashIndex = url.indexOf("#");
  const queryIndex = url.indexOf("?", hashIndex);

  if (queryIndex === -1) return null;

  // 获取?后面的所有内容
  const queryString = url.substring(queryIndex + 1);
  return queryString;
};

// Base64解码
const decodeBase64 = (str: string) => {
  try {
    // 标准Base64解码
    return decodeURIComponent(escape(atob(str)));
  } catch (e) {
    // 如果失败，尝试URL安全的Base64解码
    try {
      // 将URL安全的Base64转换为标准Base64
      const base64 = str.replace(/-/g, "+").replace(/_/g, "/");
      // 补充padding
      const padding = 4 - (base64.length % 4);
      const paddedBase64 = padding < 4 ? base64 + "=".repeat(padding) : base64;
      return decodeURIComponent(escape(atob(paddedBase64)));
    } catch (error) {
      console.error("Base64解码失败:", error);
      throw error;
    }
  }
};

onMounted(async () => {
  // 详细打印location信息，用于调试

  try {
    // 获取URL参数
    const queryParams = getQueryParams();
    console.log("解析到的查询参数:", queryParams);

    if (!queryParams) {
      statusMessage.value = "未检测到登录信息，正在返回登录页...";
      throw new Error("未找到登录参数");
    }

    statusMessage.value = "正在解析登录信息...";
    // Base64解码
    const decodedData = decodeBase64(queryParams);
    console.log("解码后的数据:", decodedData);

    // 解析JSON
    const authData = JSON.parse(decodedData);
    console.log("解析后的认证数据:", authData);

    // 特别打印redirect字段
    console.log("原始重定向目标:", authData.redirect);

    // 检查 accessToken 或 aioe-auth
    const token = authData["aioe-auth"];
    if (!token) {
      throw new Error("登录信息不完整");
    }

    statusMessage.value = "正在保存登录信息...";
    // 构建符合系统格式的token数据
    const tokenData = {
      accessToken: token,
      refreshToken: token,
      expires: authData.expires || new Date(Date.now() + 24 * 60 * 60 * 1000), // 默认24小时
      username: authData.username || "",
      display_name: authData.display_name || authData.username || "",
      avatar: authData.avatar || "",
      roles: authData.roles || [],
      permissions: authData.permissions || [],
      id: authData.id || "",
      email: authData.email || "",
      phone: authData.phone || "",
      permit_id: authData.permit_id || []
    };
    console.log("即将保存的 tokenData：", tokenData);
    // 保存token信息
    setToken(tokenData);

    // 启动token刷新定时器（立即请求一次token，确保token有效开启定时器）
    useRefreshTokenStore().startRefreshTimer();

    // 更新用户store
    if (authData.username) {
      useUserStoreHook().SET_USERNAME(authData.username);
      useUserStoreHook().SET_NICKNAME(
        authData.display_name || authData.username
      );
      useUserStoreHook().SET_ROLES(authData.roles || []);
      useUserStoreHook().SET_PERMS(authData.permissions || []);
    }
    statusMessage.value = "登录成功，正在跳转...";
    // 获取重定向路径
    let redirectPath = authData.redirect || "/welcome";
    // 确保路径格式正确
    if (!redirectPath.startsWith("/")) {
      redirectPath = "/" + redirectPath;
    }
    // 如果路径包含#，需要特殊处理
    if (redirectPath.includes("#")) {
      redirectPath = redirectPath.replace("#", "");
    }
    console.log("准备跳转到:", redirectPath);
    // 延迟一下让用户看到成功消息
    setTimeout(() => {
      router.replace(redirectPath).catch(err => {
        console.error("路由跳转失败:", err);
        router.replace("/welcome");
      });
    }, 500);
  } catch (error) {
    console.error("OAuth回调处理失败:", error);
    statusMessage.value = "登录失败，正在返回登录页...";
    ElMessage.error(error.message || "登录失败，请重试");
    // 延迟跳转到登录页
    setTimeout(() => {
      router.replace("/login");
    }, 1000);
  }
});
</script>

<style scoped>
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-content {
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  padding: 60px 80px;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

h2 {
  color: #333;
  margin: 0 0 16px 0;
  font-size: 24px;
  font-weight: 500;
}

p {
  color: #666;
  margin: 0;
  font-size: 16px;
}

.avatar-box {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 18px;
}

.avatar-img {
  width: 128px;
  height: 128px;
  object-fit: cover;
}
</style>
