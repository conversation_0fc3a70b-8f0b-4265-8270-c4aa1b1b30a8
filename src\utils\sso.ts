import { getOauth2Status } from "@/api/system";

// OAuth2配置接口定义
interface OauthConfig {
  use_oauth2: boolean;
}

/**
 * 检查OAuth2是否启用并获取配置
 * @returns Promise<OauthConfig | null> OAuth配置或null
 */
export async function checkOAuth2Config(): Promise<OauthConfig | null> {
  try {
    const response = (await getOauth2Status({})) as any;
    if (response.code === 0 && response.data) {
      const config = response.data as OauthConfig;
      // 检查是否启用OAuth2
      if (config.use_oauth2 === true) {
        return config;
      }
    }
    return null;
  } catch (error) {
    console.error("获取OAuth配置失败:", error);
    return null;
  }
}

/**
 * 跳转到OAuth2授权页面（后端主导）
 * @param targetPath 目标路径，用于登录成功后跳转
 */
export async function redirectToOAuth2Login(targetPath?: string) {
  try {
    console.log("开始检查OAuth2配置...");
    const config = await checkOAuth2Config();

    if (config) {
      console.log("OAuth2已启用，准备跳转到后端OAuth接口...");

      // 获取用户当前要访问的路径
      const currentPath =
        targetPath || window.location.hash || window.location.pathname;

      // 确保路径格式正确（以#开头的hash路由）
      let finalTargetPath = currentPath;
      if (
        finalTargetPath.startsWith("/") &&
        !finalTargetPath.startsWith("#/")
      ) {
        finalTargetPath = `#${finalTargetPath}`;
      } else if (
        !finalTargetPath.startsWith("#") &&
        !finalTargetPath.startsWith("/")
      ) {
        finalTargetPath = `#/${finalTargetPath}`;
      }

      // 如果路径无效或为none，跳转到欢迎页
      if (
        !finalTargetPath ||
        finalTargetPath === "#/" ||
        finalTargetPath === "/" ||
        finalTargetPath === "#/none" ||
        finalTargetPath === "/none" ||
        finalTargetPath === "none"
      ) {
        finalTargetPath = "#/welcome";
      }

      // 跳转到后端OAuth接口，后端会处理整个OAuth2流程
      // 构建回调URL，确保正确编码
      const callbackUrl =
        "https://aioe-test.hongcloud.net/api/user/oauth/callback";
      const encodedCallback = `${callbackUrl}?redirect=${finalTargetPath}`;
      const oauthUrl = `/api/user/oauth/login?redirect=${encodeURIComponent(encodedCallback)}`;
      console.log("跳转到后端OAuth接口:", oauthUrl);
      console.log("登录成功后将回到:", finalTargetPath);

      setTimeout(() => {
        window.location.replace(oauthUrl);
      }, 1000);
      return true;
    } else {
      console.log("OAuth2未启用或配置无效，跳转到本地登录页");
      return false;
    }
  } catch (error) {
    console.error("OAuth2登录跳转失败:", error);
    return false;
  }
}

/**
 * 简单检查OAuth2是否启用
 * @returns Promise<boolean> 是否启用OAuth2
 */
export async function isOAuth2Enabled(): Promise<boolean> {
  const config = await checkOAuth2Config();
  return config !== null;
}

console.log("SSO模块已加载");
