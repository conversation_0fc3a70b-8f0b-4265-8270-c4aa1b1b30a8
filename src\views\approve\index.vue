<template>
  <div class="event-module-container bg-white dark:bg-[#141414]">
    <el-card>
      <div class="top-actions">
        <!-- 搜索区域 -->
        <div class="search-area">
          <Perms :value="['approve:r']">
            <el-input
              v-model="keyword"
              clearable

              style="width: 300px"
              @change="search"
              @clear="clear"
            >
              <template #append>
                <el-button @click="search">
                  <el-icon>
                    <Search />
                  </el-icon>
                </el-button>
              </template>
            </el-input>
          </Perms>
          <el-select
            v-model="approveSelectedStatusvalue"
            clearable
            multiple
            placeholder="请选择任务状态搜索"
            style="margin-left: 10px; width: 330px"
            @change="getdataList({ status: approveSelectedStatusvalue })"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>
      <el-table :data="approveData" style="width: 100%">
        <el-table-column label="名称" prop="title" />
        <el-table-column label="审批内容" prop="content" />
        <el-table-column label="状态" prop="status">
          <template #default="{ row }">
            <el-tag v-if="row.status === 3" type="info">已过期</el-tag>
            <el-tag v-if="row.status === 2" type="danger">拒绝</el-tag>
            <el-tag v-if="row.status === 1" type="success">通过</el-tag>
            <el-tag v-if="row.status === 0" type="warning">待处理</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="ctime">
          <template #default="{ row }">
            {{ formatTime(row.ctime) }}
          </template>
        </el-table-column>
        <el-table-column label="更新时间" prop="utime">
          <template #default="{ row }">
            {{ formatTime(row.utime) }}
          </template>
        </el-table-column>
        <el-table-column label="过期时间" prop="expire_time">
          <template #default="{ row }">
            {{ formatTime(row.expire_time) }}
          </template>
        </el-table-column>
        <Perms :value="['approve:u']">
          <el-table-column label="操作" prop="description">
            <template #default="{ row }">
              <el-tooltip :hide-after="0" content="处理" placement="top">
                <el-button link type="primary" @click="handleProcess(row)">
                  <IconifyIconOffline
                    height="18"
                    icon="ep:finished"
                    width="18"
                  />
                </el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </Perms>
      </el-table>
      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[15, 50, 100]"
          :total="total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      <!-- 添加弹窗组件 -->
      <approve-info
        v-model="dialogVisible"
        :row-data="currentRow"
        @refresh="getdataList"
      />
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { Search } from "@element-plus/icons-vue";
import { approveList } from "@/api/approve";
import ApproveInfo from "./components/approveInfo.vue";
import { Icon } from "@iconify/vue";
import { now } from "@vueuse/core";
import IconifyIconOffline from "@/components/ReIcon/src/iconifyIconOffline";
const approveData = ref([]);
const currentPage = ref(1);
const pageSize = ref(15);
const total = ref(0);
const keyword = ref("");

// 添加弹窗控制变量
const dialogVisible = ref(false);
const currentRow = ref<any>(null);

// 添加一个原始数据的引用
const originalData = ref([]);
const approveSelectedStatusvalue = ref<number[]>([0]);

const options = [
  {
    value: 0,
    label: "待处理"
  },
  {
    value: 1,
    label: "通过"
  },
  {
    value: 2,
    label: "拒绝"
  },
  {
    value: 3,
    label: "已过期"
  }
];

//搜索
const search = () => {
  if (!keyword.value) {
    approveData.value = originalData.value;
    return;
  }
  approveData.value = originalData.value.filter((item: any) => {
    return item.title.toLowerCase().includes(keyword.value.toLowerCase());
  });
};

//清除
const clear = () => {
  keyword.value = "";
  getdataList({});
};

//首次加载拉取数据
onMounted(() => {
  getdataList({});
});

//切换当前页数
const handleCurrentChange = (current: number) => {
  getdataList({ page: current, status: approveSelectedStatusvalue.value });
};

//切换每页展示数量
const handleSizeChange = (size: number) => {
  getdataList({ size: size, status: approveSelectedStatusvalue.value });
};

//拉取数据
const getdataList = async ({ page = 1, size = 15, status = [0] }) => {
  currentPage.value = page;
  pageSize.value = size;
  approveSelectedStatusvalue.value = status;
  let res: any = await approveList({
    page: currentPage.value,
    size: pageSize.value,
    status: approveSelectedStatusvalue.value
  });
  console.log(res);
  originalData.value = res.data.data; // 保存原始数据
  approveData.value = res.data.data; // 用于展示的数据
  total.value = res.data.total;
};

// 添加时间格式化函数
const formatTime = (timestamp: string | number) => {
  if (!timestamp) return "";
  const date = new Date(timestamp);
  return date
    .toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false
    })
    .replace(/\//g, "-");
};

// 修改处理函数
const handleProcess = (row: any) => {
  currentRow.value = row;
  console.log(row);
  dialogVisible.value = true;
};
</script>

<style lang="scss" scoped>
.event-module-container {
  .top-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
  }

  .pagination-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 16px;

    .pagination-info {
      color: #606266;
    }
  }

  .search-area {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }
}
</style>
