<script lang="ts" setup>
import { getConfig } from "@/config";

const TITLE = getConfig("Title");
const COPYRIGHT = getConfig("Content");
</script>

<!--<template>-->
<!--  <footer-->
<!--    class="layout-footer text-[rgba(0,0,0,0.6)] dark:text-[rgba(220,220,242,0.8)]"-->
<!--  >-->
<!--    {{ TITLE }}&nbsp;&nbsp;{{ COPYRIGHT }}-->
<!--  </footer>-->
<!--</template>-->

<!--<style lang="scss" scoped>-->
<!--.layout-footer {-->
<!--  display: flex;-->
<!--  align-items: center;-->
<!--  justify-content: center;-->
<!--  width: 100%;-->
<!--  padding: 0 0 8px;-->
<!--  font-size: 13px;-->
<!--}-->
<!--</style>-->
