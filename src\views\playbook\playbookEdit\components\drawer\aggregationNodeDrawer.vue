<template>
  <div>
    <el-drawer v-model="isShowDrawer" size="50%">
      <template #header>
        <div class="action-drawer-title">节点信息</div>
      </template>
      <template #default>
        <el-card shadow="never">
          <el-form
            :model="nodeData.properties"
            label-width="100px"
            :rules="rules"
          >
            <el-form-item label="节点ID:">
              <el-button
                type="primary"
                link
                @click="$_copyNodeId(nodeData.id)"
                >{{ nodeData.id }}</el-button
              >
            </el-form-item>
            <el-form-item label="节点标题:" prop="node_name">
              <el-input v-model="nodeData.properties.node_name" />
            </el-form-item>
          </el-form>
          <el-divider />
          <div>
            <div class="aggregation-addparameter">
              <div>自定义聚合参数</div>
              <el-button type="primary" @click="addAggregationParameter()">
                添加参数
                <el-icon class="el-icon--right">
                  <Plus />
                </el-icon>
              </el-button>
            </div>
          </div>
          <el-table :data="aggregationTableData">
            <el-table-column align="center" label="参数名">
              <template #default="scope">
                <div>
                  <el-input v-model="scope.row.name" />
                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" label="参数描述">
              <template #default="scope">
                <div>
                  <el-input v-model="scope.row.desc" />
                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" label="值类型">
              <template #default="scope">
                <div>
                  <el-select v-model="scope.row.type">
                    <el-option
                      v-for="(item, index) in aggregationType()"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" label="值来源">
              <template #default="scope">
                <div>
                  <el-input v-model="scope.row.from" />
                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" label="拼接符">
              <template #default="scope">
                <div>
                  <el-input v-model="scope.row.symbol" />
                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作">
              <template #default="scope">
                <div>
                  <el-tooltip :hide-after="0" content="删除" placement="top">
                    <el-button
                      link
                      type="danger"
                      @click="deleteAggregationParams(scope.$index)"
                    >
                      <Icon height="20" icon="uiw:delete" width="20" />
                    </el-button>
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </template>
      <template #footer>
        <div>
          <el-button @click="closeNodeEditDrawer">取消</el-button>
          <el-button type="primary" @click="nodeEditConfirm()">保存</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
import LogicFlow from "@logicflow/core";
import { reactive, ref } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { Icon } from "@iconify/vue";
import { ElMessage, FormRules } from "element-plus";
import { validateUniqueParameterNames } from "@/utils/playbook";

interface aggregationTableType {
  name: string;
  desc: string;
  type: any;
  from: any;
  symbol: string;
}

interface RuleForm {
  node_name: string;
}

const props = defineProps({
  lf: LogicFlow
});
const isShowDrawer = ref(false);
const nodeData = ref<LogicFlow.NodeData>();
const aggregationTableData = ref<aggregationTableType[]>([]);
//值类型
const aggregationType = () => {
  return [
    { label: "字符串", value: "string" },
    { label: "数字", value: "number" },
    { label: "日期", value: "date" },
    { label: "布尔", value: "boolean" },
    { label: "Json数组", value: "jsonArray" },
    { label: "Json对象", value: "jsonObject" }
  ];
};
//打开抽屉
const openNodeEditDrawer = async (data: LogicFlow.NodeData) => {
  isShowDrawer.value = !isShowDrawer.value;
  nodeData.value = data;
  //判断该节点是新节点还是已有数据节点
  if (nodeData.value.properties.isOld == true) {
    //旧节点赋值aggregationTableData
    aggregationTableData.value = nodeData.value.properties.aggregation;
  } else {
    //新节点初始化并清空aggregationTableData
    nodeData.value.properties.aggregation = {};
    aggregationTableData.value = [];
  }
  console.log(nodeData.value);
};

//关闭抽屉
const closeNodeEditDrawer = () => {
  isShowDrawer.value = false;
};

//添加自定义聚合参数
const addAggregationParameter = () => {
  const newRow = {
    name: "",
    desc: "",
    type: "string",
    from: "",
    symbol: ""
  };
  aggregationTableData.value.push(newRow);
};

//删除自定义聚合参数
const deleteAggregationParams = index => {
  aggregationTableData.value.splice(index, 1);
};

//确认并保存数据到nodeData的properties上
const nodeEditConfirm = () => {
  //参数名校验
  if (!validateUniqueParameterNames(aggregationTableData.value)) {
    return;
  }
  isShowDrawer.value = false;
  nodeData.value.properties.isOld = true;
  nodeData.value.properties.aggregation = aggregationTableData.value;
  //推送数据到节点的properties上并更新(很重要，没送上来你前面怎么改都没用)
  props.lf.setProperties(nodeData.value.id, nodeData.value.properties!);
  console.log(props.lf.getGraphData());
};

const rules = reactive<FormRules<RuleForm>>({
  node_name: [{ required: true, message: "请输入节点标题", trigger: "blur" }]
});

// 复制节点ID
const $_copyNodeId = async (id: string) => {
  try {
    // 使用现代Clipboard API替代已废弃的execCommand
    await navigator.clipboard.writeText(`\$\{${id}\}`);
    ElMessage.success("已复制该节点ID");
  } catch (err) {
    console.error("复制失败:", err);
    // 添加失败反馈（可根据项目需求替换为错误提示）
  }
};

//对外暴露方法
defineExpose({
  openNodeEditDrawer
});
</script>

<style lang="scss" scoped>
.aggregation-addparameter {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
</style>
