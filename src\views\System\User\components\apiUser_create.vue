<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      title="新建账号"
      width="40%"
      @close="handleClose"
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="账户名称：" prop="name">
          <el-input v-model="form.name" placeholder="只允许英文字母+数字" />
        </el-form-item>
        <el-form-item prop="expiration_type">
          <template #label>
            <span class="flex items-center">
              有效期：
              <el-tooltip
                :hide-after="0"
                content="请选择过期时间，个人访问临牌生成后不支持修改"
                placement="top"
              >
                <el-icon class="ml-2 cursor-help"><QuestionFilled /></el-icon>
              </el-tooltip>
            </span>
          </template>
          <div class="flex items-center gap-2">
            <el-select v-model="form.expiration_type" style="width: 180px">
              <el-option :label="`1天（${oneDayStr}）`" value="1" />
              <el-option :label="`7天（${sevenDaysStr}）`" value="7" />
              <el-option :label="`30天（${thirtyDayStr}）`" value="30" />
              <el-option label="自定义" value="custom" />
            </el-select>
            <el-date-picker
              v-if="form.expiration_type === 'custom'"
              v-model="form.expiration_date"
              :disabled-date="disabledDate"
              placeholder="请选择日期"
              style="margin-left: 12px; width: 180px"
              type="date"
            />
          </div>
        </el-form-item>
        <el-form-item label="角色：" prop="role_names">
          <el-select
            v-model="form.role_names"
            :loading="roleStore.loading"
            filterable
            multiple
            placeholder="请选择角色"
            style="width: 100%"
          >
            <el-option
              v-for="role in roleOptions"
              :key="role.value"
              :label="role.label"
              :value="role.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="描述：">
          <el-input
            v-model="form.description"

            type="textarea"
          />
        </el-form-item>
        <el-form-item>
          <template #label>
            <span class="flex items-center">
              IP白名单:
              <el-tooltip
                :hide-after="0"
                content="只有白名单内的IP地址能够登录该账号"
                placement="top"
              >
                <el-icon class="ml-2 cursor-help"><QuestionFilled /></el-icon>
              </el-tooltip>
            </span>
          </template>
          <el-input
            v-model="form.permit_ip"
            autosize

            type="textarea"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      v-model="tokenDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      title="API Token"
      width="50%"
      @close="handleTokenDialogClose"
    >
      <div class="token-content">
        <p class="token-label">您的API Token（请妥善保管）：</p>
        <el-input
          v-model="currentToken"
          :autosize="{ minRows: 3, maxRows: 10 }"
          readonly
          type="textarea"
          @click="selectAllToken"
        />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleTokenDialogClose">关闭</el-button>
          <el-button type="primary" @click="copyToken">复制Token</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { computed, defineExpose, onMounted, ref } from "vue";
import { useRoleableStore } from "@/store/modules/roletable";
import { createApiUser } from "@/api/userList";
import { ElMessage } from "element-plus";
import { QuestionFilled } from "@element-plus/icons-vue";
import dayjs from "dayjs";

const emit = defineEmits(["success"]);

interface FormData {
  name: string;
  expiration_type: string;
  expiration_date: string;
  role_names: string[];
  description: string;
  permit_ip: string;
}

const dialogVisible = ref(false);
const formRef = ref();

// 添加token对话框相关变量
const tokenDialogVisible = ref(false);
const currentToken = ref("");

// 引入角色store
const roleStore = useRoleableStore();

// 获取角色列表
const fetchRoles = async () => {
  if (!roleStore.roleList.length) {
    await roleStore.fetchRoleList();
  }
};

// 组件挂载时获取角色列表
onMounted(() => {
  fetchRoles();
});

// 计算角色选项
const roleOptions = computed(() => {
  const uniqueRoles = new Map();
  roleStore.roleList.forEach(role => {
    if (!uniqueRoles.has(role.label)) {
      uniqueRoles.set(role.label, role);
    }
  });
  return Array.from(uniqueRoles.values()).map(role => ({
    label: role.label,
    value: role.label
  }));
});

// 处理数组格式化
const formatArrayField = (value: string | string[] | undefined) => {
  if (!value) return "";
  if (Array.isArray(value)) {
    return value.filter(Boolean).join(",");
  }
  return value
    .replace(/，/g, ",")
    .split(",")
    .filter(item => item.trim())
    .join(",");
};

// 将字符串转换为数组
const stringToArray = (value: string) => {
  return value ? value.split(",").filter(Boolean) : [];
};

// 表单数据
const form = ref<FormData>({
  name: "",
  expiration_type: "1",
  expiration_date: "",
  role_names: [],
  description: "",
  permit_ip: ""
});

// 表单验证规则
const rules = {
  name: [{ required: true, message: "请输入API账号", trigger: "blur" }],
  expiration_type: [
    { required: true, message: "请选择有效期", trigger: "change" }
  ],
  role_names: [{ required: true, message: "请选择角色", trigger: "change" }]
};

// 重置表单数据
const resetForm = () => {
  form.value = {
    name: "",
    expiration_type: "1",
    expiration_date: "",
    role_names: [],
    description: "",
    permit_ip: ""
  };
  formRef.value?.resetFields();
};

// 关闭创建表单对话框
const handleClose = () => {
  dialogVisible.value = false;
  resetForm();
};

// 关闭token对话框
const handleTokenDialogClose = () => {
  tokenDialogVisible.value = false;
};

// 选择全部token文本
const selectAllToken = (event: Event) => {
  const target = event.target as HTMLTextAreaElement;
  if (target) {
    target.select();
  }
};

// 复制token功能 - 兼容多种环境
const copyToken = async () => {
  try {
    // 优先使用现代 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(currentToken.value);
      ElMessage.success("复制成功");
      return;
    }

    // 降级方案：使用传统方法
    const textArea = document.createElement("textarea");
    textArea.value = currentToken.value;
    textArea.style.position = "fixed";
    textArea.style.left = "-999999px";
    textArea.style.top = "-999999px";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      const success = document.execCommand("copy");
      if (success) {
        ElMessage.success("复制成功");
      } else {
        throw new Error("execCommand failed");
      }
    } catch (execError) {
      console.error("execCommand 复制失败:", execError);
      ElMessage.error("复制失败，请手动复制");
    } finally {
      document.body.removeChild(textArea);
    }
  } catch (err) {
    console.error("复制失败:", err);
    ElMessage.error("复制失败，请手动复制");
  }
};

// 计算日期字符串
const today = dayjs();
const oneDayStr = today.add(1, "day").format("YYYY-MM-DD");
const sevenDaysStr = today.add(7, "day").format("YYYY-MM-DD");
const thirtyDayStr = today.add(30, "day").format("YYYY-MM-DD");

const disabledDate = date =>
  date.getTime() < today.startOf("day").toDate().getTime();

// 打开弹窗
const open = async () => {
  await fetchRoles();
  dialogVisible.value = true;
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      const { expiration_type, expiration_date, ...formData } = form.value;
      let expiration_time = 0;
      if (expiration_type === "1") {
        expiration_time = 24 * 3600;
      } else if (expiration_type === "7") {
        expiration_time = 7 * 24 * 3600;
      } else if (expiration_type === "30") {
        expiration_time = 30 * 24 * 3600;
      } else if (expiration_type === "custom") {
        const date = new Date(form.value.expiration_date);
        expiration_time = Math.floor(
          (date.getTime() - today.startOf("day").toDate().getTime()) / 1000
        );
      }
      const submitData = {
        ...formData,
        role_names: Array.isArray(form.value.role_names)
          ? form.value.role_names
          : stringToArray(formatArrayField(form.value.role_names)),
        permit_ip: formatArrayField(form.value.permit_ip),
        expiration_time: expiration_time
      };
      const res = (await createApiUser(submitData)) as any;
      if (res.code === 0) {
        ElMessage.success("创建成功");
        dialogVisible.value = false;
        emit("success");
        // 显示token对话框
        if (res.data.token) {
          currentToken.value = res.data.token;
          tokenDialogVisible.value = true;
        }
      } else {
        ElMessage.error("创建失败");
      }
    }
  });
};

// 暴露方法供父组件调用
defineExpose({
  open
});
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.gap-2 {
  gap: 0.5rem;
}

.token-content {
  padding: 0 20px;

  .token-label {
    margin-bottom: 10px;
    color: #606266;
    font-size: 14px;
  }

  .token-tip {
    margin-top: 8px;
    margin-bottom: 0;
    color: #909399;
    font-size: 12px;
    font-style: italic;
  }
}
</style>
