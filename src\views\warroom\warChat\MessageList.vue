<template>
  <div class="message-list">
    <div class="header-controls">
      <div class="room-name">
        <IconifyIconOffline
          height="18px"
          icon="icon-park-outline:message-security"
          width="18px"
        />
        {{ roomName }}
        <Perms :value="['warroom:u']">
          <el-tooltip
            v-if="!roomName.includes('私聊')"
            :hide-after="0"
            content="退出作战室"
            effect="dark"
            placement="top"
          >
            <span
              style="
                margin-left: 25px;
                cursor: pointer;
                display: flex;
                align-items: center;
              "
              @click="handleExitRoom"
            >
              <IconifyIconOffline
                class="icon"
                height="25px"
                icon="ion:exit-outline"
                width="25px"
              />
            </span>
          </el-tooltip>
        </Perms>
      </div>
      <div class="action-buttons">
        <!-- 返回按钮 - 仅在私聊页面显示 -->
        <el-tooltip
          v-if="isPrivateChat"
          :hide-after="0"
          :show-after="50"
          content="返回到作战室首页"
          effect="light"
          placement="bottom"
        >
          <el-button
            plain
            size="small"
            type="primary"
            @click="backToWelcomePage"
          >
            <IconifyIconOffline
              class="icon"
              height="18px"
              icon="icon-park-outline:back"
              width="18px"
            />&nbsp;
            <span>返回</span>
          </el-button>
        </el-tooltip>
        <!-- 邀请用户 -->
        <template v-if="!roomName.includes('私聊')">
          <el-tooltip
            :hide-after="0"
            :show-after="50"
            content="邀请用户加入作战室"
            effect="light"
          >
            <el-button
              color="#2c3e50"
              plain
              size="small"
              @click="showInviteUserDialog"
            >
              <IconifyIconOffline
                class="icon"
                height="18px"
                icon="icon-park-outline:invite"
                width="18px"
              />&nbsp;
              <span>邀请用户</span>
            </el-button>
          </el-tooltip>
        </template>
        <!-- 群公告按钮 - 仅在公共聊天显示 -->
        <Perms :value="['warroom:r']">
          <el-tooltip
            v-if="!isPrivateChat"
            :hide-after="0"
            :show-after="50"
            content="群公告"
            effect="light"
            placement="bottom"
          >
            <el-button
              color="#2c3e50"
              plain
              size="small"
              @click="showAnnouncementDialog"
            >
              <IconifyIconOffline
                class="icon"
                height="18px"
                icon="icon-park-outline:notes"
                width="18px"
              />&nbsp;
              <span>公告</span>
            </el-button>
          </el-tooltip>
        </Perms>
        <!-- 搜索功能 -->
        <Perms :value="['warroom:r']">
          <el-tooltip
            :hide-after="0"
            :show-after="50"
            content="搜索沟通记录"
            effect="light"
            placement="bottom"
          >
            <span>
              <el-popover
                ref="searchPopoverRef"
                :width="300"
                placement="bottom"
                trigger="click"
              >
                <template #reference>
                  <el-button color="#2c3e50" plain size="small">
                    <IconifyIconOffline
                      class="icon"
                      height="15px"
                      icon="ri:search-line"
                      width="15px"
                    />&nbsp;
                    <span>搜索</span>
                  </el-button>
                </template>
                <el-input
                  v-model="searchKeyword"
                  clearable
                  placeholder="搜索历史消息"
                  @clear="handleSearchClear"
                  @keyup.enter="handleSearch"
                >
                  <template #append>
                    <el-button @click="handleSearch">
                      <IconifyIconOffline
                        class="icon"
                        height="18px"
                        icon="icon-park-outline:find"
                        width="18px"
                      />
                    </el-button>
                  </template>
                </el-input>
              </el-popover>
            </span>
          </el-tooltip>
        </Perms>
        <!-- 显示条数 -->
        <el-tooltip
          :hide-after="0"
          :show-after="50"
          content="实时消息最多显示条数"
          effect="light"
          placement="bottom"
        >
          <el-dropdown trigger="click" @command="handleLimitChange">
            <el-button color="#2c3e50" plain size="small">
              <IconifyIconOffline
                class="icon"
                height="15px"
                icon="mdi:history"
                width="15px"
              />&nbsp;
              <span>最近{{ historyLimit }}条</span>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="10">10</el-dropdown-item>
                <el-dropdown-item command="20">20</el-dropdown-item>
                <el-dropdown-item command="50">50</el-dropdown-item>
                <el-dropdown-item command="100">100</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-tooltip>

        <!-- 折叠用户列表按钮 -->
        <el-tooltip
          :content="isSidebarCollapsed ? '展开用户列表' : '折叠用户列表'"
          :hide-after="0"
          :show-after="50"
          effect="light"
          placement="bottom"
        >
          <el-button
            color="#2c3e50"
            plain
            size="small"
            @click="$emit('toggle-sidebar')"
          >
            <IconifyIconOffline
              :icon="
                isSidebarCollapsed
                  ? 'icon-park-outline:user-business'
                  : 'icon-park-outline:preview-close-one'
              "
              class="icon"
              height="18px"
              width="18px"
            />
          </el-button>
        </el-tooltip>
      </div>
    </div>
    <el-scrollbar ref="scrollbar" class="scroll-area">
      <!-- 加载中提示 -->
      <div v-if="isLoadingMessages" class="loading-container">
        <div class="loading-spinner">
          <div class="spinner-circle" />
        </div>
        <div class="loading-text">消息载入中...</div>
      </div>
      <div
        v-for="msg in groupedMessages"
        :key="msg.message_id || msg.run_id"
        :class="{
          self: isSelfMessage(msg),
          recalled: msg.is_recalled,
          'announcement-message': msg.type === 'announcement'
        }"
        :data-message-id="msg.message_id"
        class="message-item"
      >
        <!-- 系统消息 -->
        <div v-if="msg.type === 'system'" class="system-message">
          {{ msg.content }}
        </div>
        <!-- 分组执行记录 -->
        <Runlog
          v-else-if="msg.type === 'runlog-group'"
          :title="`执行记录 - ${msg.playbook_name}`"
          :timestamp="msg.timestamp"
          :items="msg.items"
          :showSummary="true"
          :collapseKey="msg.run_id"
          :defaultOpen="false"
        />
        <!-- 单个执行记录 -->
        <Runlog
          v-else-if="msg.type === 'runlog'"
          :title="'执行记录'"
          :timestamp="msg.timestamp"
          :singleData="msg.content"
          :showSummary="false"
        />
        <!-- AI消息 -->
        <div v-else-if="(msg.type as any) === 'ai'" class="ai-message">
          <div class="message-header">
            <span class="sender">AI</span>
            <span class="timestamp">{{ formatTime(msg.timestamp) }}</span>
          </div>
          <div class="message-content ai-message-content">
            <div v-if="msg.content" class="text-content">
              {{ msg.content }}
            </div>
            <!-- <div v-if="msg.loading" class="ai-loading">
              <div class="loading-dots">
                <span />
                <span />
                <span />
              </div>
              <span>AI正在思考中...</span>
            </div> -->
          </div>
        </div>
        <!-- 普通消息 -->
        <template v-else-if="chatType === 'public' || chatType === 'private'">
          <div v-if="!isSelfMessage(msg)" class="message-header">
            <span class="sender"
              >{{ msg.sender_display_name }} ({{ msg.sender_name }})</span
            >
            <span class="timestamp">{{ formatTime(msg.timestamp) }}</span>
          </div>
          <div v-else class="message-header">
            <span class="timestamp">{{ formatTime(msg.timestamp) }}</span>
          </div>
          <div
            class="message-content"
            @contextmenu.prevent="handleContextMenu($event, msg)"
          >
            <template v-if="msg.is_recalled">
              <span class="recalled-text">[已撤回]</span>
            </template>
            <template v-else>
              <div v-if="msg.content" class="text-content">
                {{ msg.content }}
              </div>
              <img
                v-if="msg.image_content"
                :src="msg.image_content"
                class="message-image"
                @click="previewImage(msg.image_content)"
                @load="scrollToBottom"
              />
            </template>
            <div
              v-if="msg.image_content"
              style="display: flex; justify-content: flex-end"
            >
              <el-button
                color="#626aef"
                plain
                size="small"
                type="primary"
                @click="handleImageTest2(msg.image_content)"
              >
                <span>识别图片文字</span>
              </el-button>
            </div>
          </div>
        </template>
      </div>
    </el-scrollbar>
    <el-dialog
      v-model="searchResultsVisible"
      :style="{ top: '5vh' }"
      custom-class="search-results-dialog"
      title="搜索结果"
      width="60%"
      @close="handleSearchClose"
    >
      <div v-if="chatStore.messageSearchLoading" class="loading">加载中...</div>
      <div v-else>
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :total="chatStore.messageSearchTotal"
          @current-change="handlePageChange"
        />
        <el-scrollbar class="search-results-scrollbar" height="60vh">
          <div
            v-for="msg in chatStore.messageSearchResults"
            :key="msg.id"
            class="search-result-item"
          >
            <div class="message-header">
              <span class="sender">{{ msg.sender_name }}</span>
              <span class="time">{{ formatTime(msg.timestamp) }}</span>
            </div>
            <div class="message-content">
              <div v-if="msg.content" class="text-content">
                {{ msg.content }}
              </div>
              <img
                v-if="msg.image_content"
                :src="msg.image_content"
                class="message-image"
                @click="previewImage(msg.image_content)"
              />
            </div>
          </div>
        </el-scrollbar>
      </div>
    </el-dialog>
    <!-- 群公告弹窗 -->
    <el-dialog
      v-model="announcementDialogVisible"
      :destroy-on-close="true"
      :style="{ top: '5vh' }"
      custom-class="announcement-dialog"
      title="群公告"
      width="60%"
    >
      <div class="announcement-header">
        <el-button
          plain
          size="small"
          type="primary"
          @click="showNewAnnouncementDialog"
        >
          <IconifyIconOffline
            class="icon"
            height="18px"
            icon="fe:notice-on"
            width="18px"
          />&nbsp;
          <span>发布新公告</span>
        </el-button>
      </div>

      <div v-if="chatStore.isLoadingAnnouncements" class="loading-container">
        <div class="loading-spinner">
          <div class="spinner-circle" />
        </div>
        <div class="loading-text">公告加载中...</div>
      </div>

      <div
        v-else-if="chatStore.announcements.length === 0"
        class="empty-data"
        height="20vh"
      >
        <IconifyIconOffline
          class="icon"
          height="48px"
          icon="mingcute:announcement-fill"
          style="opacity: 0.5"
          width="48px"
        />
        <p>暂无公告</p>
      </div>

      <el-scrollbar v-else height="60vh">
        <div
          v-for="announcement in chatStore.announcements"
          :key="announcement.id"
          class="announcement-item"
        >
          <div class="announcement-header">
            <div class="announcement-info">
              <span class="sender">{{ announcement.sender_name }}</span>
              <span class="time">{{ formatTime(announcement.timestamp) }}</span>
            </div>
            <el-button
              v-if="isSelfAnnouncement(announcement)"
              circle
              size="small"
              @click="handleDeleteAnnouncement(announcement.id)"
            >
              <IconifyIconOffline
                class="icon"
                height="16px"
                icon="icon-park-outline:delete"
                width="16px"
              />
            </el-button>
          </div>
          <div class="announcement-content" v-html="announcement.content" />
          <img
            v-if="announcement.image_content"
            :src="announcement.image_content"
            class="announcement-image"
            @click="previewImage(announcement.image_content)"
          />
        </div>
      </el-scrollbar>

      <el-pagination
        v-if="chatStore.announcements.length > 0"
        :page-size="announcementPageSize"
        :total="chatStore.announcementsTotal"
        class="announcement-pagination"
        layout="prev, pager, next"
        @current-change="handleAnnouncementPageChange"
      />
    </el-dialog>
    <!-- 发布新公告弹窗 -->
    <el-dialog
      v-model="newAnnouncementDialogVisible"
      :destroy-on-close="true"
      :style="{ top: '5vh' }"
      title="发布新公告"
      width="60%"
    >
      <div class="editor-container">
        <el-form ref="announcementFormRef" :model="newAnnouncementForm">
          <el-form-item
            :rules="[
              { required: true, message: '公告内容不能为空', trigger: 'blur' }
            ]"
            prop="content"
          >
            <div class="editor-wrapper">
              <Toolbar
                :defaultConfig="toolbarConfig"
                :editor="editorRef"
                :mode="mode"
                class="editor-toolbar-inner"
              />
              <div class="editor-content-wrapper">
                <Editor
                  v-model="announcementValueHtml"
                  :defaultConfig="editorConfig"
                  :mode="mode"
                  @onChange="handleChange"
                  @onCreated="handleCreated"
                />
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button
          plainF
          style="margin-right: 10px"
          @click="newAnnouncementDialogVisible = false"
        >
          <IconifyIconOffline
            class="icon"
            height="18px"
            icon="icon-park-outline:close"
            width="18px"
          />&nbsp;
          <span>取消</span>
        </el-button>
        <el-button
          :loading="publishingAnnouncement"
          plain
          type="primary"
          @click="handlePublishAnnouncement"
        >
          <IconifyIconOnline
            height="18px"
            icon="line-md:confirm-circle"
            width="18px"
          />&nbsp;
          <span>发布公告</span>
        </el-button>
      </template>
    </el-dialog>
    <!-- 右键菜单 -->
    <div
      v-if="contextMenu.visible"
      :style="{
        left: `${contextMenu.x}px`,
        top: `${contextMenu.y}px`
      }"
      class="context-menu"
      @click.stop
    >
      <template v-if="contextMenu.message?.content">
        <div class="menu-item" @click="copyMessage">复制文本</div>
      </template>
      <div
        v-if="canRecall(contextMenu.message)"
        class="menu-item danger-item"
        @click.stop="handleRecall(contextMenu.message)"
      >
        撤回
      </div>
    </div>
    <!-- 图片查看组件 -->
    <el-image-viewer
      v-if="imageViewerVisible"
      :url-list="[currentImageUrl]"
      @close="handleImageViewerClose"
    />
    <!-- 图片预览右键菜单 -->
    <div
      v-if="imageContextMenu.visible"
      :style="{
        left: `${imageContextMenu.x}px`,
        top: `${imageContextMenu.y}px`
      }"
      class="image-context-menu"
      @click.stop
    >
      <div class="menu-item" @click="handleImageTest">识别图片文字</div>
    </div>

    <!-- OCR识别结果对话框 -->
    <el-dialog
      v-model="ocrDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      title="图片文字识别结果"
      width="50%"
      @close="handleOcrDialogClose"
    >
      <div v-if="ocrLoading" class="ocr-loading">
        <el-icon class="is-loading">
          <Loading />
        </el-icon>
        <span>正在识别图片中的文字，请稍候...</span>
      </div>
      <div v-else class="ocr-content">
        <p class="ocr-label">识别结果：</p>
        <el-input
          v-model="ocrResult"
          :autosize="{ minRows: 3, maxRows: 10 }"
          placeholder="识别结果将显示在这里..."
          readonly
          type="textarea"
          @click="selectAllOcrText"
        />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleOcrDialogClose">关闭</el-button>
          <el-button @click="copyOcrResult">复制内容</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 邀请用户对话框 -->
    <el-dialog
      v-model="inviteUserDialogVisible"
      :append-to-body="true"
      :modal="true"
      class="invite-user-dialog"
      title="邀请用户加入作战室"
      width="40%"
    >
      <el-form :model="inviteUserForm" @submit.prevent>
        <el-form-item label="邀请用户">
          <el-select
            ref="inviteUserSelectRef"
            v-model="inviteUserForm.invitedUsers"
            :popper-options="{
              modifiers: [
                {
                  name: 'offset',
                  options: {
                    offset: [0, 8]
                  }
                }
              ]
            }"
            filterable
            multiple
            placeholder="选择要邀请的用户，支持模糊查询"
            popper-class="invite-select-dropdown"
            popper-placement="top-start"
            style="width: 100%"
            @visible-change="handleInviteSelectVisibleChange"
          >
            <el-option
              v-for="user in allUsers"
              :key="user.id"
              :label="`${user.display_name} (${user.username})`"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="inviteUserDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="inviteUsers">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  nextTick,
  onBeforeUnmount,
  onMounted,
  onUnmounted,
  type PropType,
  reactive,
  ref,
  shallowRef,
  watch
} from "vue";
import { useUserStore } from "@/store/modules/user";
import type { FormInstance } from "element-plus";
import {
  ElImageViewer,
  ElLoading,
  ElMessage,
  ElMessageBox
} from "element-plus";
import { Loading } from "@element-plus/icons-vue";
import Tesseract from "tesseract.js";
import { type Message } from "@/directives/warroom/warChat";
import { formatTime } from "@/utils/warChatTime";
import { useRoute } from "vue-router";
import { useChatStore } from "@/store/warChat";
import { storeToRefs } from "pinia";
import "@wangeditor/editor/dist/css/style.css";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import { quitRoom, inviteUser } from "@/api/warroom";
import { apiuserAccountList } from "@/api/userList";
import IconifyIconOffline from "@/components/ReIcon/src/iconifyIconOffline";
import Runlog from "@/components/Rerunlog/runlog.vue";
import {
  getStatusClass,
  getStatusText,
  isSuccessStatus,
  isFailStatus,
  isRunningStatus
} from "@/utils/statusUtils";

const chatStore = useChatStore();
const { historyLimit, isLoadingMessages } = storeToRefs(chatStore);
const handleLimitChange = (limit: string) => {
  chatStore.setHistoryLimit(Number(limit));
};

const userStore = useUserStore();
const route = useRoute();
const currentUserId = ref(userStore.$state.id);
const scrollbar = ref();
const imageViewerVisible = ref(false);
const currentImageUrl = ref(""); // 当前图片url
const contextMenu = ref({
  // 右键菜单
  visible: false,
  x: 0,
  y: 0,
  message: null as unknown as Message
});
const imageContextMenu = ref({
  // 图片预览右键菜单
  visible: false,
  x: 0,
  y: 0
});

// OCR识别相关状态
const ocrDialogVisible = ref(false); // 图片OCR识别对话框
const ocrResult = ref(""); // 图片OCR识别结果
const ocrLoading = ref(false); // 图片OCR识别加载状态
const searchKeyword = ref(""); // 图片OCR识别搜索关键词
const searchResultsVisible = ref(false); // 图片OCR识别搜索结果
const currentPage = ref(1);
const pageSize = ref(20);

// runlog 折叠状态管理
const runlogCollapseState = ref<Record<string, string[]>>({});

// 分组处理 runlog 消息
const groupedMessages = computed(() => {
  const grouped: any[] = [];
  const runlogGroups = new Map();
  const processedRunIds = new Set();

  props.messages.forEach((msg, index) => {
    if (msg.type === "runlog") {
      const content = msg.content as any;
      if (content && content.run_id) {
        const runId = content.run_id;

        // 如果这个run_id还没有被处理过
        if (!processedRunIds.has(runId)) {
          // 收集所有相同run_id的消息
          const sameRunMessages = props.messages.filter(m => {
            const c = m.content as any;
            return m.type === "runlog" && c && c.run_id === runId;
          });

          // 创建分组
          const group = {
            type: "runlog-group",
            run_id: runId,
            message_id: msg.message_id,
            sender_id: msg.sender_id,
            sender_name: msg.sender_name,
            timestamp: msg.timestamp,
            room_id: msg.room_id,
            playbook_name: content.playbook_name,
            items: sameRunMessages.map(m => m.content as any)
          };

          grouped.push(group);
          processedRunIds.add(runId);
        }
        // 如果已经处理过这个run_id，跳过
      } else {
        // 没有 run_id 的单独显示
        grouped.push(msg);
      }
    } else {
      // 非 runlog 消息直接添加
      grouped.push(msg);
    }
  });

  return grouped;
});

// 群公告相关
const announcementDialogVisible = ref(false);
const newAnnouncementDialogVisible = ref(false);
const announcementPageSize = ref(15);
const announcementCurrentPage = ref(1);
const publishingAnnouncement = ref(false);
const announcementFormRef = ref<FormInstance>();
const newAnnouncementForm = reactive({
  content: ""
});

// 邀请用户相关
const inviteUserDialogVisible = ref(false);
const inviteUserForm = reactive({
  invitedUsers: [] as string[]
});
const allUsers = ref<any[]>([]); // 所有用户列表
const isLoadingInviteUsers = ref(false);
const inviteUserSelectRef = ref<any>(null);
const inviteDropdownVisible = ref(false);

// 富文本编辑器相关配置
const mode = "default";
const editorRef = shallowRef();
const announcementValueHtml = ref("<p></p>");
const announcementImageContent = ref("");
const imgRegEx = /<img.*?>/gi;

// 工具栏配置，只保留图片上传功能
const toolbarConfig = {
  toolbarKeys: ["uploadImage"],
  hideKeys: [
    "fullScreen",
    "insertTable",
    "codeBlock",
    "insertLink",
    "todo",
    "emotion",
    "fontFamily",
    "fontSize",
    "lineHeight",
    "bulletedList",
    "numberedList",
    "blockquote",
    "code",
    "clearStyle",
    "divider",
    "redo",
    "undo",
    "through",
    "italic",
    "bold",
    "color",
    "backgroundColor",
    "justifyCenter",
    "justifyLeft",
    "justifyRight",
    "indent",
    "delIndent",
    "header1",
    "header2",
    "header3"
  ]
};

// 图片压缩方法
const compressImage = async (file: File): Promise<File> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = URL.createObjectURL(file);
    img.onload = () => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      const MAX_WIDTH = 1600;
      const MAX_HEIGHT = 1200;
      let { width, height } = img;

      if (width > height) {
        if (width > MAX_WIDTH) {
          height *= MAX_WIDTH / width;
          width = MAX_WIDTH;
        }
      } else {
        if (height > MAX_HEIGHT) {
          width *= MAX_HEIGHT / height;
          height = MAX_HEIGHT;
        }
      }

      canvas.width = width;
      canvas.height = height;
      ctx?.drawImage(img, 0, 0, width, height);

      const fileType = file.type || "image/jpeg";
      const mimeType = fileType === "image/png" ? "image/png" : "image/jpeg";

      canvas.toBlob(
        blob => {
          if (blob) {
            const newFile = new File([blob], file.name, {
              type: mimeType,
              lastModified: Date.now()
            });
            resolve(newFile);
          } else {
            reject(new Error("图片压缩失败"));
          }
        },
        mimeType,
        0.9
      );
    };
    img.onerror = () => reject(new Error("图片加载失败"));
  });
};

// 富文本编辑器配置
const editorConfig = {
  placeholder: "请输入公告内容...",
  defaultContent: [
    {
      type: "paragraph",
      children: [{ text: "", fontFamily: "微软雅黑", fontSize: "20px" }],
      lineHeight: 1.5
    }
  ],
  hoverbarKeys: {
    text: []
  },
  MENU_CONF: {
    uploadImage: {
      server: "/api/upload/image",
      fieldName: "file",
      maxFileSize: 10 * 1024 * 1024,
      maxNumberOfFiles: 10,
      allowedFileTypes: ["image/*"],
      metaWithUrl: true,
      customUpload: async (file, insertFn) => {
        const loading = ElLoading.service({
          lock: true,
          text: "图片处理中...",
          background: "rgba(0, 0, 0, 0.7)"
        });

        try {
          const compressedFile = await compressImage(file);
          const reader = new FileReader();
          reader.onload = () => {
            const base64 = reader.result as string;
            announcementImageContent.value = base64;
            insertFn(base64, file.name, base64);
            ElMessage.success({
              message: "图片上传成功",
              duration: 1000
            });

            loading.close();
          };
          reader.onerror = () => {
            ElMessage.error("图片读取失败");
            loading.close();
          };
          reader.readAsDataURL(compressedFile);
        } catch (error) {
          ElMessage.error("图片处理失败");
          loading.close();
        }
      }
    }
  }
};

// 编辑器创建完成处理
const handleCreated = editor => {
  editorRef.value = editor;
  editor.getConfig().MENU_CONF = {
    ...(editor.getConfig().MENU_CONF || {}),
    insertText: {
      wrap: true
    }
  };

  // 禁用选中文本时弹出的浮动工具栏
  editor.getConfig().hoverbarKeys = {
    text: []
  };

  editor.on("selectionChange", () => {
    const selection = editor.getSelectionText();
    if (selection) {
      const elem = editor.getSelectionContainerElem()?.elems[0];
      if (elem) {
        elem.scrollIntoView({ behavior: "smooth", block: "center" });
      }
    }
  });
};

// 编辑器内容变化处理
const handleChange = editor => {
  newAnnouncementForm.content = editor.getText();
  const richText = announcementValueHtml.value;
  announcementValueHtml.value = richText.replace(imgRegEx, match => {
    return match.replace(/<img/, '<img style="width:30%;"');
  });
};

// 组件销毁时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});

// 公告键盘事件处理器
let announcementKeydownHandler = null;
const setupAnnouncementKeyHandler = () => {
  if (announcementKeydownHandler) {
    document.removeEventListener("keydown", announcementKeydownHandler);
  }

  announcementKeydownHandler = event => {
    if (
      newAnnouncementDialogVisible.value &&
      event.key === "Enter" &&
      !event.shiftKey
    ) {
      event.preventDefault();
      handlePublishAnnouncement();
    }
  };

  document.addEventListener("keydown", announcementKeydownHandler);
};

// 移除公告键盘处理器
const removeAnnouncementKeyHandler = () => {
  if (announcementKeydownHandler) {
    document.removeEventListener("keydown", announcementKeydownHandler);
    announcementKeydownHandler = null;
  }
};

// 显示新公告对话框
const showNewAnnouncementDialog = () => {
  newAnnouncementDialogVisible.value = true;
  announcementDialogVisible.value = false;
  announcementValueHtml.value = "<p></p>";
  announcementImageContent.value = "";
  newAnnouncementForm.content = "";
  nextTick(() => {
    setupAnnouncementKeyHandler();
  });
};

watch(newAnnouncementDialogVisible, newVal => {
  if (!newVal) {
    removeAnnouncementKeyHandler();
  }
});

const partner = ref();

const props = defineProps({
  messages: { type: Array as PropType<Message[]>, required: true },
  chatType: {
    type: String as PropType<"public" | "private" | "ai">,
    required: true
  },
  partnerId: { type: String, required: false },
  partnerName: { type: String, required: false },
  roomId: { type: String, required: true },
  isSidebarCollapsed: {
    type: Boolean,
    default: false
  },
  isFloating: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits<{
  (e: "recall", messageId: string): void;
  (e: "show-announcements"): void;
  (e: "toggle-sidebar"): void;
  (e: "back-to-welcome"): void;
}>();

const backToWelcomePage = () => {
  if (props.isFloating) {
    // 悬浮窗模式下触发事件通知父组件
    emit("back-to-welcome");
  } else {
    // 普通模式下直接使用chatStore的方法
    chatStore.backToWelcomePage();
  }
};

const roomName = computed(() => {
  const room = chatStore.rooms.find(r => r.id === props.roomId);
  if (room) {
    // 如果是私聊房间，显示"与xxx的私聊"
    if (room.name?.startsWith("私聊_") && props.partnerName) {
      return `与 ${props.partnerName} 的私聊`;
    }
    return room.name;
  }
  return "";
});

// 退出作战室
const handleExitRoom = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要退出当前 ${roomName.value} 作战室吗？`,
      "退出确认",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );
    // 用户点击确定后才执行退出
    const response = (await quitRoom({ id: props.roomId })) as any;
    if (response.code === 0) {
      await chatStore.fetchRooms();
      ElMessage.success({
        message: response.data,
        duration: 800
      });
      backToWelcomePage();
    }
  } catch {
    // 用户点击取消，不做任何事
  }
};

const isPrivateChat = computed(() => {
  const room = chatStore.rooms.find(r => r.id === props.roomId);
  return room?.name?.startsWith("私聊_") || false;
});

// 监听私聊对象名称, 用于私聊页面标题
watch(
  () => props.partnerName,
  newName => {
    if (newName) {
      partner.value = newName;
    } else if (props.partnerId && props.messages.length > 0) {
      const partnerMessage = props.messages.find(
        msg => msg.sender_id === props.partnerId && msg.sender_name
      );
      if (partnerMessage?.sender_name) {
        partner.value = partnerMessage.sender_name;
      }
    }
  },
  { immediate: true }
);

// 监听消息列表事件, 消息列表变化时回滚到底部
onMounted(() => {
  document.addEventListener("click", handleClickOutside);
  // 初始化时立即执行一次滚动
  nextTick(() => {
    scrollToBottom();
  });

  // 监听消息变化
  watch(
    () => props.messages,
    () => {
      nextTick(() => {
        scrollToBottom();
      });
    },
    { deep: true }
  );

  // 监听房间ID变化
  watch(
    () => route.params.roomId,
    () => {
      nextTick(() => {
        scrollToBottom();
      });
    }
  );
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
});

const isSelfMessage = (msg: Message) => {
  return msg.sender_id === currentUserId.value;
};

// 判断是否可撤回
const canRecall = (msg: Message) => {
  if (!msg || !currentUserId.value) return false;
  const messageTime = new Date(msg.timestamp).getTime();
  const currentTime = new Date().getTime();
  const timeValid = currentTime - messageTime <= 120 * 1000;
  return (
    isSelfMessage(msg) && !msg.is_recalled && msg.type !== "system" && timeValid
  );
};

// 撤回处理
const handleRecall = (msg: Message) => {
  const messageTime = new Date(msg.timestamp).getTime();
  if (Date.now() - messageTime > 120 * 1000) {
    ElMessage.warning({
      message: "消息已超过2分钟, 无法撤回",
      duration: 1000
    });
    contextMenu.value.visible = false;
    return;
  }
  try {
    emit("recall", msg.message_id);
    contextMenu.value.visible = false;
  } catch {
    ElMessage.error("撤回操作失败");
  }
};

// 图片预览
const previewImage = (url: string) => {
  currentImageUrl.value = url;
  imageViewerVisible.value = true;
  // 添加图片预览右键菜单监听
  nextTick(() => {
    addImageContextMenuListener();
  });
};
//////////////////////////////////////////////////////
// 添加图片预览右键菜单监听器
const addImageContextMenuListener = () => {
  // 尝试多个可能的选择器
  const selectors = [
    ".el-image-viewer__wrapper",
    ".el-image-viewer__img",
    ".el-image-viewer"
  ];
  // 添加右键监听器到图片预览器
  for (const selector of selectors) {
    const element = document.querySelector(selector);
    if (element) {
      element.addEventListener("contextmenu", handleImageContextMenu);
      break;
    }
  }
};
// 移除图片预览右键菜单监听器
const removeImageContextMenuListener = () => {
  const selectors = [
    ".el-image-viewer__wrapper",
    ".el-image-viewer__img",
    ".el-image-viewer"
  ];
  for (const selector of selectors) {
    const element = document.querySelector(selector);
    if (element) {
      element.removeEventListener("contextmenu", handleImageContextMenu);
    }
  }
};
//////////////////////////////////////////////////////

// 图片预览右键菜单处理
const handleImageContextMenu = (event: MouseEvent) => {
  event.preventDefault();
  imageContextMenu.value = {
    visible: true,
    x: event.clientX,
    y: event.clientY
  };
};

//测试
const handleImageTest2 = (imageContent: string) => {
  currentImageUrl.value = imageContent;
  imageViewerVisible.value = false;
  handleImageTest();
};

// 图片OCR识别调用
const handleImageTest = async () => {
  imageContextMenu.value.visible = false;

  if (!currentImageUrl.value) {
    ElMessage.error("未找到图片");
    return;
  }

  ocrDialogVisible.value = true;
  ocrLoading.value = true;
  ocrResult.value = "";

  try {
    // 使用Tesseract.js进行OCR识别
    const {
      data: { text }
    } = await Tesseract.recognize(
      currentImageUrl.value,
      "chi_sim+eng" // 支持中文简体和英文
    );

    ocrResult.value = text.trim() || "未识别到文字内容";
  } catch (error) {
    ElMessage.error("图片文字识别失败，请重试");
    ocrResult.value = "识别失败，请重试";
  } finally {
    ocrLoading.value = false;
  }
};

// 图片查看器关闭
const handleImageViewerClose = () => {
  removeImageContextMenuListener();
  imageViewerVisible.value = false;
  imageContextMenu.value.visible = false;
};

// 关闭OCR对话框
const handleOcrDialogClose = () => {
  ocrDialogVisible.value = false;
  ocrResult.value = "";
  ocrLoading.value = false;
};

// 选择全部OCR文本
const selectAllOcrText = (event: Event) => {
  const target = event.target as HTMLTextAreaElement;
  if (target) {
    target.select();
  }
};

// 复制OCR识别结果
const copyOcrResult = async () => {
  if (!ocrResult.value) {
    ElMessage.warning("没有可复制的内容");
    return;
  }

  try {
    // 优先使用现代 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(ocrResult.value);
      ElMessage.success("复制成功");
      return;
    }

    // 降级方案：使用传统方法
    const textArea = document.createElement("textarea");
    textArea.value = ocrResult.value;
    textArea.style.position = "fixed";
    textArea.style.left = "-999999px";
    textArea.style.top = "-999999px";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      const success = document.execCommand("copy");
      if (success) {
        ElMessage.success("复制成功");
      } else {
        throw new Error("execCommand failed");
      }
    } catch (execError) {
      console.error("execCommand 复制失败:", execError);
      ElMessage.error("复制失败，请手动复制");
    } finally {
      document.body.removeChild(textArea);
    }
  } catch (err) {
    console.error("复制失败:", err);
    ElMessage.error("复制失败，请手动复制");
  }
};

// 右键菜单处理
const handleContextMenu = (event: MouseEvent, msg: Message) => {
  contextMenu.value = {
    visible: true,
    x: event.clientX,
    y: event.clientY,
    message: { ...msg }
  };
};

const copyMessage = () => {
  if (contextMenu.value.message) {
    const contentToCopy = contextMenu.value.message.content || "";
    navigator.clipboard
      .writeText(contentToCopy)
      .then(() =>
        ElMessage.success({
          message: "已复制文本到剪贴板",
          duration: 1000
        })
      )
      .catch(() => ElMessage.error("复制失败"));
  }
  contextMenu.value.visible = false;
};

// 点击空白处退出右键菜单
const handleClickOutside = () => {
  contextMenu.value.visible = false;
  imageContextMenu.value.visible = false;
};

// 滚动条滚动处理
const scrollToBottom = () => {
  const tryScroll = (retries = 3) => {
    if (!scrollbar.value?.wrapRef?.scrollHeight) {
      if (retries > 0) {
        // 如果还没准备好，等待100ms后重试
        setTimeout(() => tryScroll(retries - 1), 100);
      }
      return;
    }
    scrollbar.value.setScrollTop(scrollbar.value.wrapRef.scrollHeight);
  };
  tryScroll();
};

// 群公告相关
const showAnnouncementDialog = async () => {
  announcementDialogVisible.value = true;
  await chatStore.fetchAnnouncements(
    props.roomId,
    announcementCurrentPage.value,
    announcementPageSize.value
  );
};

const handleAnnouncementPageChange = (page: number) => {
  announcementCurrentPage.value = page;
  chatStore.fetchAnnouncements(props.roomId, page, announcementPageSize.value);
};

const isSelfAnnouncement = (announcement: any) => {
  return (
    announcement.sender_id === currentUserId.value ||
    announcement.sender_id === String(currentUserId.value)
  );
};

const handleDeleteAnnouncement = (announcementId: string) => {
  ElMessageBox.confirm("确定要删除这条公告吗？", "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      chatStore.deleteAnnouncement(announcementId);
      // 删除后手动刷新公告列表
      setTimeout(() => {
        chatStore.fetchAnnouncements(
          props.roomId,
          announcementCurrentPage.value,
          announcementPageSize.value
        );
      }, 500);
    })
    .catch(() => {});
};

const handlePublishAnnouncement = async () => {
  if (!announcementFormRef.value) return;

  await announcementFormRef.value.validate(async valid => {
    if (valid) {
      publishingAnnouncement.value = true;
      try {
        const editorContent = editorRef.value?.getText() || "";

        if (!editorContent.trim() && !announcementImageContent.value) {
          ElMessage.warning("公告内容不能为空");
          publishingAnnouncement.value = false;
          return;
        }

        await chatStore.publishAnnouncement(
          editorContent,
          props.roomId,
          announcementImageContent.value
        );

        newAnnouncementDialogVisible.value = false;
        announcementValueHtml.value = "<p></p>";
        newAnnouncementForm.content = "";
        announcementImageContent.value = "";

        announcementDialogVisible.value = true;
        await chatStore.fetchAnnouncements(
          props.roomId,
          1,
          announcementPageSize.value
        );
        announcementCurrentPage.value = 1;

        ElMessage.success({
          message: "发布成功",
          duration: 1000
        });
      } finally {
        publishingAnnouncement.value = false;
      }
    }
  });
};

// 邀请用户相关方法
const showInviteUserDialog = async () => {
  inviteUserForm.invitedUsers = [];

  allUsers.value = [];
  await loadInviteUserList();
  inviteUserDialogVisible.value = true;
};

// 加载邀请用户列表的方法
const loadInviteUserList = async () => {
  isLoadingInviteUsers.value = true;
  try {
    // 获取邀请用户列表调用的是user模块的api,这里写死100条,可做分页处理
    const response = (await apiuserAccountList({ size: 100 })) as any;
    if (response.code === 0 && response.data.users) {
      allUsers.value = response.data.users.filter(
        (user: any) => user.id !== userStore.$state.id
      );
    } else {
      ElMessage.error({
        message: "获取邀请用户列表失败",
        duration: 1000
      });
    }
  } catch (error) {
    ElMessage.error({
      message: "获取邀请用户列表失败",
      duration: 1000
    });
  } finally {
    isLoadingInviteUsers.value = false;
  }
};

// 处理邀请用户下拉框可见性变化
const handleInviteSelectVisibleChange = (visible: boolean) => {
  inviteDropdownVisible.value = visible;
};

// 邀请用户到作战室
const inviteUsers = async () => {
  if (!inviteUserForm.invitedUsers.length) {
    ElMessage.warning("请选择要邀请的用户");
    return;
  }

  try {
    // 打印所选用户的ID
    console.log("所选用户ID列表:", inviteUserForm.invitedUsers);
    console.log("当前作战室ID:", props.roomId);
    // 调用邀请用户API
    const response = (await inviteUser({
      id: props.roomId,
      members: inviteUserForm.invitedUsers
    })) as any;
    if (response.code === 0) {
      ElMessage.success({
        message: response.data,
        duration: 1000
      });
      inviteUserDialogVisible.value = false;
      // 刷新当前房间成员列表
      await chatStore.fetchRoomUsers(props.roomId);
    } else {
      ElMessage.error("邀请用户失败，请稍后重试");
      inviteUserDialogVisible.value = false;
    }
  } catch (error) {
    ElMessage.error("邀请用户失败，请稍后重试");
  }
};

const searchPopoverRef = ref();

const handleSearch = () => {
  if (!searchKeyword.value.trim()) return;

  chatStore.searchMessages({
    keyword: searchKeyword.value,
    page: currentPage.value,
    pageSize: pageSize.value,
    roomId: props.roomId
  });
  searchResultsVisible.value = true;

  // 关闭搜索弹出框
  searchPopoverRef.value?.hide?.();
};

const handleSearchClear = () => {
  searchKeyword.value = "";
};

const handleSearchClose = () => {
  chatStore.messageSearchResults = [];
  chatStore.messageSearchTotal = 0;
  searchKeyword.value = "";
  currentPage.value = 1;
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  handleSearch();
};

defineExpose({
  scrollToBottom
});

// Runlog相关工具函数
// 获取整体状态（用于结果展示）
const getOverallRunlogStatusClass = (items: any[]) => {
  // 如果是单个记录，根据其status正常显示
  if (!items || items.length <= 1) {
    const item = items?.[0];
    return getStatusClass(item?.status);
  }

  // 多组记录：有一个失败就整体失败
  const hasFailedItem = items.some(item => isFailStatus(item.status));
  if (hasFailedItem) {
    return "fail";
  }

  // 检查是否有end-node
  const hasEndNode = items.some(item => item.node_type === "end-node");
  if (hasEndNode) {
    // 有end-node，根据end-node的status判断
    const endNode = items.find(item => item.node_type === "end-node");
    return getStatusClass(endNode?.status);
  }

  // 没有end-node且其它节点status为成功就是执行中
  return "running";
};

const getOverallRunlogStatusText = (items: any[]) => {
  // 如果是单个记录，根据其status正常显示
  if (!items || items.length <= 1) {
    const item = items?.[0];
    return getStatusText(item?.status);
  }

  // 多组记录：有一个失败就整体失败
  const hasFailedItem = items.some(item => isFailStatus(item.status));
  if (hasFailedItem) {
    return "失败";
  }

  // 检查是否有end-node
  const hasEndNode = items.some(item => item.node_type === "end-node");
  if (hasEndNode) {
    // 有end-node，根据end-node的status判断
    const endNode = items.find(item => item.node_type === "end-node");
    return getStatusText(endNode?.status);
  }

  // 没有end-node且其它节点status为成功就是执行中
  return "执行中";
};

const getRunlogStatusClass = (
  status: any,
  nodeType?: string,
  items?: any[]
) => {
  // 只有在多组合并的情况下（items存在且长度大于1）才应用特殊逻辑
  if (items && items.length > 1) {
    // 检查是否有任何一个节点失败
    const hasFailedItem = items.some(item => isFailStatus(item.status));

    // 如果有失败的节点，整个流程显示为失败
    if (hasFailedItem) {
      return "fail";
    }

    const hasEndNode = items.some(item => item.node_type === "end-node");

    // 如果有end-node，说明流程已完成，所有非失败节点显示为成功
    if (hasEndNode) {
      return "success";
    }

    // 如果没有end-node，且当前不是start-node，显示执行中状态
    if (nodeType !== "start-node") {
      return "running";
    }
  }

  // 单个记录或多组已完成的情况，直接按status判断
  return getStatusClass(status);
};

const getRunlogStatusText = (status: any, nodeType?: string, items?: any[]) => {
  // 只有在多组合并的情况下（items存在且长度大于1）才应用特殊逻辑
  if (items && items.length > 1) {
    // 检查是否有任何一个节点失败
    const hasFailedItem = items.some(item => isFailStatus(item.status));

    // 如果有失败的节点，整个流程显示为失败
    if (hasFailedItem) {
      return "失败";
    }

    const hasEndNode = items.some(item => item.node_type === "end-node");

    // 如果有end-node，说明流程已完成，所有非失败节点显示为成功
    if (hasEndNode) {
      return "成功";
    }

    // 如果没有end-node，且当前不是start-node，显示执行中状态
    if (nodeType !== "start-node") {
      return "执行中";
    }
  }

  // 单个记录或多组已完成的情况，直接按status判断
  return getStatusText(status);
};

const getRunlogDuration = (start: any, end: any) => {
  if (start && end) {
    const startTime = new Date(start).getTime();
    const endTime = new Date(end).getTime();
    const durationMs = endTime - startTime;
    const durationSeconds = Math.round((durationMs / 1000) * 100) / 100;
    return `${durationSeconds} 秒`;
  }
  return "-";
};

const getRunlogResultOutput = (result: any) => {
  try {
    if (!result) return "暂无结果";

    // 如果是字符串，尝试解析为JSON
    if (typeof result === "string") {
      const parsedResult = JSON.parse(result);
      return parsedResult.output || parsedResult.message || result;
    }

    // 如果是对象，直接获取output或message字段
    if (typeof result === "object") {
      return result.output || result.message || JSON.stringify(result);
    }

    return String(result);
  } catch (error) {
    // 解析失败时返回原始内容或截断后的内容
    const resultStr = String(result);
    return resultStr.length > 200
      ? resultStr.substring(0, 200) + "..."
      : resultStr;
  }
};

// 解析参数函数，用于vue-json-viewer
const parseArgs = (args: any) => {
  try {
    if (!args) return {};
    // 如果是字符串，尝试解析为JSON
    if (typeof args === "string") {
      // 先尝试解析为JSON
      try {
        return JSON.parse(args);
      } catch (jsonError) {
        // 如果JSON解析失败，说明是普通字符串，直接返回字符串
        return args;
      }
    }
    // 如果已经是对象，直接返回
    if (typeof args === "object") {
      return args;
    }
    // 其他情况返回原始内容
    return args;
  } catch (error) {
    console.error("解析参数失败:", error);
    return {};
  }
};
</script>

<style scoped>
.header-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: white;
  position: sticky;
  top: 0;
  z-index: 100;
  border-bottom: 1px solid #ebeef5;
}

.room-name {
  font-weight: bold;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.el-button {
  margin: 0;
}

.scroll-area {
  height: calc(100% - 62px);
}

.search-navigation {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-left: 10px;
}

.message-list {
  height: 100%;
  padding: 12px;
  box-sizing: border-box;
  background-color: #f5f7fa;
}

.scroll-area {
  height: 100%;
}

.message-item {
  margin-bottom: 16px;
  display: flex;
}

.message-item.self {
  justify-content: flex-end;
}

.message-item:not(.self) {
  justify-content: flex-start;
}

.system-message {
  width: 80%;
  text-align: center;
  color: #909399;
  font-size: 12px;
}

.message-header {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
}

.sender {
  font-weight: 600;
  color: #303133;
  margin-right: 8px;
  font-size: 14px;
}

.timestamp {
  font-size: 12px;
  color: #909399;
  opacity: 0.8;
}

.message-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  gap: 8px;
}

.message-content {
  max-width: 70%;
  padding: 12px;
  border-radius: 8px;
  position: relative;
  word-break: break-word;
}

.message-image {
  max-width: 300px;
  max-height: 300px;
  border-radius: 4px;
  cursor: pointer;
  object-fit: contain;
  margin: 5px 0;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.message-image:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
}

.recalled-text {
  color: #909399;
  font-style: italic;
}

.message-item {
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.message-item.self {
  align-items: flex-end;
  margin-left: -10px;
}

.message-item:not(.self) {
  align-items: flex-start;
}

.message-header {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
}

.message-item.self .message-header {
  flex-direction: row-reverse;
}

.message-content {
  max-width: 70%;
  padding: 12px;
  border-radius: 8px;
  position: relative;
  word-break: break-word;
  display: inline-block;
  max-width: 50%;
  min-width: 40px;
}

.message-item.self .message-content {
  background-color: #2c7fe4;
  color: white;
  border-top-right-radius: 0;
}

.message-item:not(.self) .message-content {
  background-color: white;
  border: 1px solid #ebeef5;
  border-top-left-radius: 0;
}

.context-menu {
  position: fixed;
  background: white;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  z-index: 999;
}

.menu-item {
  padding: 8px 16px;
  cursor: pointer;
}

.menu-item:hover {
  background-color: #f5f7fa;
}

.danger-item {
  color: #f56c6c;
}

.disabled-item {
  color: #c0c4cc;
  cursor: not-allowed;
  padding: 8px 16px;
}

.disabled-item:hover {
  background-color: transparent;
}

.message-item.self .message-content {
  background-color: #2c7fe4;
  color: white;
  border-top-right-radius: 0;
}

.message-item.self.recalled .message-content {
  background-color: white;
  color: #909399;
  border: 1px solid #ebeef5;
}

.message-item:not(.self) .message-content {
  background-color: white;
  border: 1px solid #ebeef5;
  border-top-left-radius: 0;
}

.announcement-entry {
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
  text-align: center;
  position: sticky;
  top: 0;
  background: white;
  z-index: 100;
}

.announcement-button {
  width: 90%;
  margin: 5px auto;
  transition: all 0.3s;
}

.announcement-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.announcement-button i {
  margin-right: 8px;
}

.scroll-area {
  height: calc(100% - 62px);
}

.private-chat-header {
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
  text-align: center;
  position: sticky;
  top: 0;
  background: white;
  z-index: 100;
}

.private-chat-button {
  width: 90%;
  margin: 5px auto;
  transition: all 0.3s;
  background-color: #ecf5ff;
  border-color: #b3d8ff;
  color: #2c7fe4;
}

.private-chat-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.2);
}

.private-chat-button i {
  margin-right: 8px;
}

.search-navigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
}

.search-result-item {
  padding: 10px;
  margin: 5px 0;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.search-result-item:hover {
  background-color: #e4e7ed;
}

.search-results-dialog {
  position: fixed !important;
  top: -80px !important;
  margin: 0 auto !important;
}

.search-results-dialog :deep(.el-dialog) {
  margin: 0 auto !important;
  transform: none !important;
}

.search-results-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.search-results-scrollbar {
  margin-top: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.search-results-scrollbar :deep(.el-scrollbar__wrap) {
  padding: 10px;
}

.search-results-scrollbar :deep(.el-scrollbar__bar) {
  opacity: 0.6;
}

.search-results-scrollbar :deep(.el-scrollbar__bar:hover) {
  opacity: 1;
}

.search-result-item {
  padding: 12px;
  margin: 8px 0;
  border-radius: 4px;
  background-color: #f5f7fa;
  transition: all 0.3s;
}

.search-result-item:hover {
  background-color: #e4e7ed;
  transform: translateX(2px);
}

.search-result-item .message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.search-result-item .sender {
  font-weight: 600;
  color: #303133;
}

.search-result-item .time {
  color: #909399;
  font-size: 12px;
}

.search-result-item .message-content {
  color: #606266;
}

.search-result-item .message-image {
  max-width: 100%;
  max-height: 200px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 8px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  width: 100%;
}

.loading-spinner {
  position: relative;
  width: 60px;
  height: 60px;
  margin-bottom: 20px;
}

.spinner-circle {
  width: 100%;
  height: 100%;
  border: 4px solid rgba(64, 158, 255, 0.2);
  border-radius: 50%;
  border-top-color: #2c7fe4;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: #606266;
  font-size: 16px;
  letter-spacing: 1px;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.announcement-dialog {
  max-width: 800px;
}

.announcement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.announcement-pagination {
  margin-top: 20px;
  text-align: center;
}

.announcement-item {
  margin-bottom: 20px;
  padding: 16px;
  border-radius: 8px;
  background-color: #f9f9f9;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.announcement-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.announcement-info {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.announcement-info .sender {
  font-weight: bold;
  margin-right: 10px;
}

.announcement-info .time {
  color: #909399;
  font-size: 12px;
}

.announcement-content {
  padding: 8px 0;
  line-height: 1.6;
  font-size: 14px;
  white-space: pre-wrap;
  word-break: break-word;
}

.editor-container {
  width: 100%;
  height: 100%;
}

.editor-wrapper {
  width: 100%;
  height: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}

.editor-toolbar-inner {
  border-bottom: 1px solid #eee;
  background-color: #f5f7fa;
  padding: 0;
  margin: 0;
}

.editor-content-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  margin: 0;
  border: none;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  min-height: 300px;
}

.announcement-image {
  max-width: 300px;
  max-height: 300px;
  border-radius: 4px;
  cursor: pointer;
  object-fit: contain;
  margin: 8px 0;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.announcement-image:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
}

/* 隐藏滚动条但保留滚动功能 */
:deep(.w-e-scroll::-webkit-scrollbar) {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

:deep(.w-e-scroll) {
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* IE and Edge */
  overflow: auto !important;
}

:deep(.w-e-text-container) {
  font-family: "微软雅黑" !important;
  font-size: 18px !important;
  height: calc(100% - 35px) !important;
  border: none !important;
  margin: 0 !important;
  overflow: auto !important;
  box-sizing: border-box !important;
}

:deep(.w-e-text) {
  word-break: break-word !important;
  word-wrap: break-word !important;
  white-space: pre-wrap !important;
  overflow-wrap: break-word !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
  overflow-x: hidden !important;
}

:deep(.w-e-scroll) {
  max-width: 100% !important;
  width: 100% !important;
  overflow-x: hidden !important;
}

.empty-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  color: #909399;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.loading-spinner {
  display: inline-block;
  position: relative;
  width: 40px;
  height: 40px;
}

.spinner-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-top-color: #2c7fe4;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 10px;
  color: #909399;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.announcement-info .sender {
  font-weight: bold;
}

.el-image-viewer__wrapper {
  z-index: 3000 !important;
}

.el-image-viewer__img {
  max-width: 90vw !important;
  max-height: 90vh !important;
  object-fit: contain !important;
}

/* 图片预览右键菜单样式 */
.image-context-menu {
  position: fixed;
  background: white;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  z-index: 4000; /* 确保在图片预览器之上 */
  padding: 4px 0;
  min-width: 80px;
}

.image-context-menu .menu-item {
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #303133;
  transition: background-color 0.3s;
}

.image-context-menu .menu-item:hover {
  background-color: #f5f7fa;
}

/* OCR对话框样式 */
.ocr-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #606266;
}

.ocr-loading .el-icon {
  font-size: 32px;
  margin-bottom: 16px;
  color: #409eff;
}

.ocr-loading span {
  font-size: 16px;
}

.ocr-content {
  padding: 0 20px;
}

.ocr-content .ocr-label {
  margin-bottom: 10px;
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 添加公告图片样式 */
.announcement-image {
  max-width: 300px;
  max-height: 300px;
  border-radius: 4px;
  cursor: pointer;
  object-fit: contain;
  margin: 8px 0;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.announcement-image:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
}

.image-upload-area {
  margin-top: 10px;
}

.image-preview-container {
  position: relative;
  display: inline-block;
  margin: 10px 0;
}

.announcement-preview-image {
  max-width: 300px;
  max-height: 200px;
  border-radius: 4px;
  object-fit: contain;
  border: 1px solid #ebeef5;
}

.remove-image-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  padding: 5px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.announcement-upload {
  display: block;
  margin-top: 10px;
}

.el-upload__tip {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
}

:deep(.el-button) {
  color: #503f2c !important; /* 默认字体色 */
}

:deep(.iconify) {
  color: #2c3e50 !important; /* 默认图标色 */
}

:deep(.el-button:hover),
:deep(.el-button:focus),
:deep(.el-button.active) {
  background: #2c3e50 !important;
  color: #fff !important;
  border-color: #2c3e50 !important;
}

:deep(.el-button:hover) .iconify,
:deep(.el-button:focus) .iconify,
:deep(.el-button.active) .iconify {
  color: #fff !important;
}

.runlog-card {
  background: #f8fafc;
  border: 1px solid #e0e7ef;
  border-radius: 8px;
  padding: 16px;
  margin: 10px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
  width: 60%;
}

.runlog-card :deep(.el-collapse-item__header) {
  padding: 12px 16px;
  border-radius: 6px;
}

.runlog-card :deep(.el-collapse-item__content) {
  padding: 0;
}

.runlog-item {
  margin-bottom: 16px;
}

.runlog-item:last-child {
  margin-bottom: 0;
}

.runlog-item-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
  padding: 8px 12px;
  background-color: #f1f3f4;
  border-radius: 4px;
}

.node-type {
  background-color: #2c7fe4;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.node-name {
  font-weight: 600;
  color: #303133;
}

.custom-timeline {
  padding: 0px 0;
}

.timeline-node {
  display: flex;
  align-items: stretch;
  margin-bottom: 24px;
  position: relative;
}

.timeline-node:last-child .timeline-line {
  bottom: 0;
}

.timeline-line-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 16px;
  margin-left: 5px;
  margin-top: 5px;
  position: relative;
}

.timeline-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #2c7fe4;
  border: 2px solid #fff;
  box-shadow: 0 0 0 2px #2c7fe4;
  position: relative;
  z-index: 2;
  flex-shrink: 0;
}

.timeline-line {
  width: 2px;
  background-color: #2c7fe4;
  position: absolute;
  top: 16px;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
}

.timeline-node-content {
  flex: 1;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.timeline-node-content .runlog-item-header {
  background-color: transparent;
  padding: 0;
  margin-bottom: 12px;
  border-radius: 0;
}

.timeline-node-content .runlog-item-details {
  margin-top: 8px;
}

.runlog-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.runlog-icon {
  width: 32px;
  height: 32px;
  margin-right: 12px;
}

.runlog-title {
  display: flex;
  flex-direction: column;
}

.runlog-title span:first-child {
  font-weight: bold;
  color: #2c7fe4;
}

.runlog-time {
  font-size: 12px;
  color: #888;
}

.runlog-content {
  background: #fff;
  border-radius: 4px;
  font-family: "Fira Mono", "Consolas", monospace;
  font-size: 13px;
  color: #333;
  white-space: pre-wrap;
  word-break: break-all;
}

.log-json-label {
  font-size: 13px;
  color: #888;
  margin-bottom: 4px;
}

.log-json-content {
  background: transparent;
  color: #333;
  font-size: 13px;
  white-space: pre-wrap;
  word-break: break-all;
  margin: 0;
  padding: 0;
}

.ai-message {
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  width: 100%;
  align-items: flex-start;
}

.ai-message .message-content {
  min-width: 30px;
  min-height: 80px;
  max-width: 60%;
  padding: 16px;
  border-radius: 8px;
  background-color: #f0f9ff;
  border: 1px solid #e0f2fe;
  border-top-left-radius: 0;
  word-break: break-word;
  position: relative;
  box-sizing: border-box;
}

.ai-message-content {
  position: relative;
  padding-bottom: 32px; /* 给图标留空间 */
}

.ai-message .text-content {
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
  margin: 0;
}

.ai-loading {
  display: flex;
  align-items: center;
  color: #3ba5e7;
  font-size: 14px;
  margin-top: 8px;
  gap: 6px;
}

.loading-dots {
  display: flex;
  gap: 4px;
}

.loading-dots span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #3ba5e7;
  animation: loading-dot 1.4s infinite both;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

.loading-dots span:nth-child(3) {
  animation-delay: 0s;
}

@keyframes loading-dot {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.icon {
  color: #2c3e50 !important; /* 始终深灰色 */
}

/* 按钮悬浮时图标变白色 */
:deep(.el-button:hover) .icon,
:deep(.el-button:focus) .icon {
  color: #fff !important;
}

/* TasksDetails样式 */
.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #fafafa;
  border-radius: 6px;
}

.info-block {
  text-align: left;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  white-space: nowrap;
}

.info-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 0;
  white-space: nowrap;
}

.info-value {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
}

.status-tag {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  color: #fff;
}

.status-tag.success {
  background: #52c41a;
}

.status-tag.fail {
  background: #f5222d;
}

.status-tag.running {
  background: #f3c42b;
}

/* runlog描述列表样式 */
.runlog-item-details {
  margin-top: 8px;
}

.runlog-item-details :deep(.el-descriptions) {
  margin-top: 8px;
}

.runlog-item-details :deep(.el-descriptions__label) {
  font-weight: 500;
  color: #666;
  width: 120px;
  min-width: 120px;
  max-width: 140px;
  white-space: nowrap;
}

.runlog-item-details :deep(.el-descriptions__content) {
  color: #333;
  word-break: break-all;
  white-space: pre-wrap;
  min-width: 140px;
  max-width: 400px;
}

.result-output {
  max-height: 500px;
  overflow-y: auto;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
  font-family: "Fira Mono", "Consolas", monospace;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 邀请用户对话框样式 */
.invite-user-dialog {
  z-index: 3002;
}

.invite-select-dropdown {
  margin-bottom: 10px;
}
</style>
