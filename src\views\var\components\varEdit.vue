<template>
  <el-drawer v-model="visible" :size="500" title="编辑变量">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <div class="form-content">
        <el-form-item label="变量名" prop="name" required>
          <el-input v-model="form.name" disabled  />
        </el-form-item>
        <!-- 变量值输入区域 -->
        <el-form-item
          v-for="(item, index) in form.value"
          :key="index"
          :label="index === 0 ? '变量值' : ''"
          :prop="'value.' + index"
          :rules="{
            required: true,
            message: '变量值不能为空',
            trigger: 'blur'
          }"
        >
          <div class="value-input-group">
            <el-input v-model="form.value[index]"  />
            <el-button
              :disabled="form.value.length === 1"
              class="delete-btn"
              type="danger"
              @click="removeValue(index)"
            >
              删除
            </el-button>
          </div>
        </el-form-item>

        <!-- 添加按钮 -->
        <el-form-item :label="form.value.length > 1 ? '' : ''">
          <el-button class="add-btn" type="primary" @click="addValue">
            添加变量值
          </el-button>
        </el-form-item>
      </div>
    </el-form>
    <!-- 操作按钮 -->
    <div class="drawer-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submit">确定</el-button>
    </div>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import type { FormInstance } from "element-plus";
import { ElMessage } from "element-plus";
import { updateVar } from "@/api/var";

const visible = ref(false);
const formRef = ref<FormInstance>();

const form = ref({
  name: "",
  value: [""] // 初始化一个空的输入框
});

const rules = {
  name: [{ required: true, message: "请输入变量名", trigger: "blur" }]
};

const emit = defineEmits(["refresh"]);

/**
 * 添加新的变量值输入框
 */
const addValue = () => {
  form.value.value.push("");
};

/**
 * 移除指定索引的变量值输入框
 */
const removeValue = (index: number) => {
  form.value.value.splice(index, 1);
};

/**
 * 提交表单
 */
const submit = async () => {
  if (!formRef.value) return;
  await formRef.value.validate(valid => {
    if (valid) {
      // 过滤掉空值
      const values = form.value.value.filter(v => v.trim() !== "");
      if (values.length === 0) {
        ElMessage.warning("请至少输入一个变量值");
        return;
      }
      updateVar({
        name: form.value.name,
        value: values
      }).then((res: any) => {
        if (res.code === 0) {
          ElMessage.success("提交成功");
          visible.value = false;
          emit("refresh"); // 触发刷新事件
        } else {
          ElMessage.error("提交失败");
        }
      });
    }
  });
};

/**
 * 显示抽屉并设置初始数据
 */
const showDrawer = (data: any) => {
  form.value = {
    name: data.name,
    value: Array.isArray(data.value) ? [...data.value] : [data.value]
  };
  visible.value = true;
};

defineExpose({ showDrawer });
</script>

<style lang="scss" scoped>
.form-content {
  padding-bottom: 80px; // 为固定定位的按钮留出空间
  :deep(.el-form-item) {
    margin-bottom: 22px;

    &:last-child {
      margin-bottom: 0;
    }

    .el-form-item__label {
      font-weight: 500;
    }
  }
}

.value-input-group {
  display: flex;
  gap: 12px;
  align-items: center;
  width: 100%;

  .el-input {
    flex: 1;
  }

  .delete-btn {
    width: 80px;
  }
}

.add-btn {
  width: 100%;
  margin-bottom: 10px;
}

.drawer-footer {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 20px;
  background-color: var(--el-bg-color);
  border-top: 1px solid var(--el-border-color-light);
  text-align: right;
  z-index: 1;

  .el-button {
    margin-left: 12px;
  }
}
</style>
