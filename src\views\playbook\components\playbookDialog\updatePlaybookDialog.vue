<template>
  <div>
    <el-dialog v-model="dialogVisible" title="基本信息" top="5vh" width="700px">
      <template #default>
        <div>
          <playbookDialogForm v-model:formData="formData" />
        </div>
      </template>
      <template #footer>
        <div>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updatePlaybook()"> 确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { apiPlaybookEdit } from "@/api/playbook";
import { ElMessage } from "element-plus";
import { ref } from "vue";
import playbookDialogForm from "@/views/playbook/components/playbookDialog/playbookDialogForm.vue";
import usePlaybookStore from "@/store/modules/playbook";

const dialogVisible = ref(false);
const playbookStore = usePlaybookStore();
//表单数据
const formData = ref({
  id: "",
  name: "",
  remark: "",
  tags: [],
  scenes: []
});

//打开dialog并获取父组件传递的数据(并在defineExpose中对外暴露该方法)
const openDialog = data => {
  dialogVisible.value = true;
  formData.value = data; // 将父组件传递的数据赋值给表单
  console.log(formData.value);
};

//确认按钮-更新剧本
const updatePlaybook = async () => {
  let res: any = await apiPlaybookEdit({
    playbook_id: formData.value.id,
    name: formData.value.name,
    remark: formData.value.remark,
    tags: formData.value.tags,
    scenes: formData.value.scenes
  });
  console.log(res);
  if (res.code == 0) {
    ElMessage.success("修改成功");
  } else {
    ElMessage.error("修改失败");
  }
  //用于更新流程图左上角展示的实时数据
  playbookStore.playbookData = formData.value;
  playbookStore.playbookName = formData.value.name;
  console.log(formData.value);
  console.log(playbookStore.playbookData);
  dialogVisible.value = false;
};

//对外暴露方法
defineExpose({
  openDialog
});
</script>

<style lang="scss" scoped></style>
