<template>
  <el-drawer
    v-model="visible"
    :size="drawerSize"
    direction="ltr"
    title="选择动作"
    @close="handleClose"
  >
    <div class="drawer-flex">
      <!-- 左侧：应用列表 -->
      <div :class="{ 'full-width': !selectedTool }" class="drawer-left">
        <div class="header-actions">
          <div class="search-area">
            <el-input
              v-model="searchKey"
              clearable
              placeholder="搜索任务名称, 任务名称, 更新人..."
              style="width: 100%"
              @change="onSearch"
              @clear="handleClear"
            >
              <template #append>
                <el-button @click="onSearch">
                  <el-icon>
                    <Search />
                  </el-icon>
                </el-button>
              </template>
            </el-input>
          </div>
        </div>
        <el-scrollbar style="flex: 1 1 0%; min-height: 0">
          <el-table
            :data="toolList"
            highlight-current-row
            style="width: 100%"
            @row-click="handleToolClick"
          >
            <el-table-column label="应用">
              <template #default="{ row }">
                <div class="tool">
                  <img
                    v-if="row.logo"
                    :src="`data:image/png;base64,${row.logo}`"
                    alt="logo"
                    class="tool_logo"
                  />
                  <span>{{ row.description }}</span>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-scrollbar>
        <div class="pagination-fixed">
          <el-pagination
            v-model:current-page="toolPage"
            v-model:page-size="toolSize"
            :page-sizes="[15, 50, 100]"
            :pager-count="5"
            :total="toolTotal"
            background
            layout="total, sizes, prev, pager, next"
            size="small"
            @size-change="onSizeChange"
            @current-change="onPageChange"
          />
        </div>
      </div>
      <!-- 右侧：版本详情 -->
      <div
        v-if="actionInputList.length || versionLoading"
        v-loading="versionLoading"
        class="drawer-right version-detail"
      >
        <el-scrollbar style="flex: 1 1 0; max-height: 100%">
          <el-form-item label="版本选择:" required>
            <el-select
              v-model="selectedVersionId"
              placeholder="请选择版本"
              style="width: 100%"
              @change="handleVersionChange"
            >
              <el-option
                v-for="item in toolVersionList"
                :key="item.version"
                :label="item.version"
                :value="item.version"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="动作名称:" required>
            <el-select
              v-model="selectedActionFunc"
              placeholder="请选择动作"
              style="width: 100%"
              @change="onActionSelect"
            >
              <el-option
                v-for="item in actionInputList"
                :key="item.func"
                :label="item.name"
                :value="item.func"
              />
            </el-select>
          </el-form-item>
          <!-- 资源选择 -->
          <el-form-item
            label="资源选择:"
            :required="selectedTool && selectedTool.required === 1"
          >
            <el-select
              v-model="selectedResourceKey"
              placeholder="请选择资源"
              style="width: 100%"
              @change="onResourceSelect"
            >
              <el-option
                v-for="item in resourceKeys"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <!-- 参数输入区（有参数才显示） -->
          <div
            v-if="
              currentAction && currentAction.input && currentAction.input.length
            "
            class="param-list"
          >
            <span style="font-size: 16px">动作参数：</span>
            <div>
              <div
                v-for="param in currentAction.input || []"
                :key="param.key"
                class="param-item-vertical"
              >
                <div class="param-label-vertical">
                  {{ param.description }}
                  <span v-if="param.required" style="color: red">*</span>
                </div>
                <div class="param-input-vertical">
                  <el-switch
                    v-if="param.type === 'boolean'"
                    v-model="actionForm[currentAction.func][param.key]"
                    active-text="开启"
                    inactive-text="关闭"
                    :active-value="true"
                    :inactive-value="false"
                    inline-prompt
                  />
                  <!-- 如果出现其他类型，统一当做多行输入框 -->
                  <el-input
                    v-else
                    v-model="actionForm[currentAction.func][param.key]"
                    :placeholder="'请输入' + param.description"
                    type="textarea"
                    :rows="1"
                    :show-password="param.type === 'password'"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 重试次数、超时时间始终显示 -->
          <div style="margin-bottom: 0; margin-top: 16px">
            <div
              style="margin-bottom: 10px; display: flex; align-items: center"
            >
              <label
                style="
                  width: 60px;
                  text-align: right;
                  margin-right: 6px;
                  font-size: 14px;
                  font-weight: normal;
                "
              >
                重试次数:
              </label>
              <el-input
                v-model="actionData.retry_times"
                max="3"
                min="0"

                style="width: 160px; margin-right: 6px"
                type="number"
              >
                <template #append>次</template>
              </el-input>
            </div>
            <div style="display: flex; align-items: center">
              <label
                style="
                  width: 60px;
                  text-align: right;
                  margin-right: 6px;
                  font-size: 14px;
                  font-weight: normal;
                "
              >
                超时时间:
              </label>
              <el-input
                v-model="actionData.timeout"
                min="300"

                style="width: 160px; margin-right: 6px"
                type="number"
              >
                <template #append>秒</template>
              </el-input>
            </div>
          </div>
        </el-scrollbar>
        <div class="drawer-footer-fixed">
          <el-button type="primary" @click="onActionConfirm">确定</el-button>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script lang="ts" setup>
import { computed, defineEmits, defineProps, ref, watch } from "vue";
import {
  getToolActionList,
  getToolDetail,
  getToolList,
  getToolVersionList
} from "@/api/toolManagement";
import { Search } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { useChatStore } from "@/store/warChat";
import { executeDrawerAction } from "./DrawerExecute";

const chatStore = useChatStore();
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
});

// 构建动作对象
const actionData = ref({
  resource: "",
  node_name: "",
  action: "",
  action_name: "",
  retry_times: 0,
  timeout: 300,
  app_name: "",
  name: "",
  app_ver: "",
  main: "",
  action_input: {}
});

const searchKey = ref(""); // 搜索关键字
const toolList = ref([]);
const toolVersionList = ref([]);
const toolPage = ref(1);
const toolSize = ref(15);
const toolTotal = ref(0);
const total_pages = ref(0);
const versionLoading = ref(false); // 版本列表加载
const versionDetailLoading = ref(false); // 版本详情加载

const emit = defineEmits(["update:modelValue"]);

const visible = ref(props.modelValue);

watch(
  () => props.modelValue,
  val => (visible.value = val)
);

watch(visible, async val => {
  emit("update:modelValue", val);
  if (val) {
    ToolList();
  }
});

// 获取应用列表
const ToolList = async (
  page = toolPage.value,
  size = toolSize.value,
  key = searchKey.value
) => {
  const res = (await getToolList({
    page,
    size,
    key
  })) as any;
  if (res.code === 0) {
    toolList.value = res.data.tools || [];
    toolPage.value = res.data.page;
    toolSize.value = res.data.size;
    toolTotal.value = res.data.total;
    total_pages.value = res.data.total_pages;
  }
};

// 关闭抽屉
const handleClose = () => {
  visible.value = false;
  selectedTool.value = null;
  selectedVersionId.value = "";
  selectedVersion.value = null;
  showDetail.value = false;
  // 右侧详情相关
  currentAction.value = null;
  selectedActionFunc.value = "";
  actionForm.value = {};
  showActionDrawer.value = false;
  actionInputList.value = []; // 关键：清空右侧详情
};

//搜索部分
const onSearch = () => {
  toolPage.value = 1; // 搜索时重置到第一页
  ToolList(1, toolSize.value, searchKey.value);
};
const onPageChange = (page: number) => {
  toolPage.value = page;
  ToolList(page, toolSize.value, searchKey.value);
};
const onSizeChange = (size: number) => {
  toolSize.value = size;
  toolPage.value = 1;
  ToolList(1, size, searchKey.value);
};
//清除搜索
const handleClear = () => {
  searchKey.value = "";
  toolPage.value = 1; // 重置到第一页
  ToolList(toolPage.value, toolSize.value, "");
};
///////////////////////
// 选中工具
const selectedTool = ref(null);
const handleToolClick = async row => {
  // 切换应用时清空右侧动作参数部分
  currentAction.value = null;
  selectedActionFunc.value = "";
  actionForm.value = {};
  // 清空资源数据
  resourceKeys.value = [];
  selectedResourceKey.value = "";
  selectedTool.value = row;
  versionLoading.value = true;

  // 配置动作参数
  console.log(row);
  actionData.value.name = row.description;
  actionData.value.app_name = row.name;
  actionData.value.main = row.main;
  actionData.value.node_name = row.description;

  // 获取工具资源详情
  await getToolResources(row.name);
  const res = (await getToolVersionList({ name: row.name })) as any;
  if (res.code === 0) {
    toolVersionList.value = res.data || [];
    if (toolVersionList.value.length > 0) {
      selectedVersionId.value = toolVersionList.value[0].version;
      selectedVersion.value = toolVersionList.value[0];
      actionData.value.app_ver = toolVersionList.value[0].version; // 这里加上
      handleVersionClick(toolVersionList.value[0]);
    }
  }
  versionLoading.value = false;
};
const drawerSize = computed(() => {
  if (showDetail.value && selectedVersion.value) {
    // 两栏：左320 + 右500
    return "850px";
  } else {
    // 只显示左侧
    return "360px";
  }
});
const showDetail = ref(false);
watch(selectedTool, val => {
  if (val) {
    // 先不显示详情，等动画结束后再显示
    showDetail.value = false;
    setTimeout(() => {
      showDetail.value = true;
    }, 0); // 200ms和CSS动画时长一致
  } else {
    showDetail.value = false;
  }
});
//////////////////////////////////////////////////////
// 动作
const selectedVersionId = ref(""); // 用于select的v-model
const selectedVersion = ref(null); // 存对象
const actionInputList = ref<any[]>([]); // 存储actions
const selectedActionFunc = ref(""); // 当前选中的动作 func

const handleVersionClick = (row: any) => {
  versionDetailLoading.value = false; // 不需要 loading
  selectedVersion.value = row;
  ToolActionList(row);
};
// 获取工具资源详情
const getToolResources = async (toolName: string) => {
  try {
    const modelRes = (await getToolDetail({
      name: toolName
    })) as any;
    if (modelRes.code === 0) {
      console.log("modelRes", modelRes);
      // 根据返回的数据结构，name作为label，id作为value
      if (Array.isArray(modelRes.data) && modelRes.data.length > 0) {
        resourceKeys.value = modelRes.data.map(item => ({
          id: item.id,
          name: item.name
        }));
        // 不自动选择第一个，手动选择
        selectedResourceKey.value = "";
        actionData.value.resource = "";
      } else {
        // 没有资源数据时清空
        resourceKeys.value = [];
        selectedResourceKey.value = "";
        actionData.value.resource = "";
      }
    } else {
      // API返回错误时清空
      resourceKeys.value = [];
      selectedResourceKey.value = "";
      actionData.value.resource = "";
    }
  } catch (error) {
    // 请求失败时清空
    console.error("获取工具资源失败:", error);
    resourceKeys.value = [];
    selectedResourceKey.value = "";
    actionData.value.resource = "";
  }
};

// 获取动作输入参数列表
const ToolActionList = async (row: any) => {
  const res = (await getToolActionList({
    name: row.name,
    version: row.version
  })) as any;
  if (res.code === 0) {
    actionInputList.value = res.data.actions || [];
  }
};

//选中动作
// const handleActionClick = (row: any) => {
//   selectedActionFunc.value = row.func;
//   currentAction.value = row;
//   // 每次都只保留当前动作的表单
//   const formObj: any = {};
//   (row.input || []).forEach((param: any) => {
//     if (param.type === "boolean") {
//       formObj[param.key] = param.default !== undefined ? param.default : false;
//     } else {
//       formObj[param.key] = param.default ?? "";
//     }
//   });
//   actionForm.value = { [row.func]: formObj };
//   actionData.value.action_name = row.name;
//   actionData.value.action = row.func;
//   showActionDrawer.value = true;
// };

const showActionDrawer = ref(false); // 控制新抽屉显示
const currentAction = ref<any>(null); // 当前选中的动作对象

const actionForm = ref<{ [func: string]: any }>({});

// 确定按钮点击事件
const onActionConfirm = async () => {
  // 验证必填字段
  const validationErrors = [];

  // 验证动作名称
  if (!selectedActionFunc.value) {
    validationErrors.push("动作名称为必填项，请选择动作");
  }

  // 验证资源选择
  if (selectedTool.value && selectedTool.value.required === 1) {
    if (!selectedResourceKey.value) {
      validationErrors.push("资源选择为必填项，请选择资源");
    }
  }

  // 验证动作参数
  if (currentAction.value && currentAction.value.input) {
    currentAction.value.input.forEach((param: any) => {
      if (param.required) {
        const value = actionForm.value[currentAction.value.func]?.[param.key];
        if (
          value === null ||
          value === undefined ||
          value === "" ||
          (typeof value === "string" && !value.trim())
        ) {
          validationErrors.push(`参数"${param.description}"为必填项，不能为空`);
        }
      }
    });
  }

  // 如果有验证错误，显示错误信息并返回
  if (validationErrors.length > 0) {
    ElMessage.error(validationErrors[0]); // 显示第一个错误
    return;
  }

  actionData.value.action_input = actionForm.value[currentAction.value.func];
  const result = await executeDrawerAction(
    chatStore.currentRoomId,
    "action",
    actionData.value,
    actionData.value
  );
  // 执行成功后关闭抽屉
  if (result && result.code === 0) {
    handleClose();
  }
};

const handleVersionChange = versionId => {
  const versionObj = toolVersionList.value.find(v => v.version === versionId);
  selectedVersion.value = versionObj;
  actionData.value.app_ver = versionId;
  ToolActionList(versionObj);
};

const resourceKeys = ref<{ id: string; name: string }[]>([]);
const selectedResourceKey = ref(""); // 选中的资源id

const onActionSelect = func => {
  const action = actionInputList.value.find(item => item.func === func);
  currentAction.value = action || null;
  if (action) {
    const formObj = {};
    (action.input || []).forEach(param => {
      if (param.type === "boolean") {
        formObj[param.key] =
          param.default !== undefined ? param.default : false;
      } else {
        formObj[param.key] = param.default ?? "";
      }
    });
    actionForm.value = { [action.func]: formObj };
    actionData.value.action_name = action.name;
    actionData.value.action = action.func;
  }
};
// 选择资源
const onResourceSelect = id => {
  const resource = resourceKeys.value.find(item => item.id === id);
  if (resource) {
    selectedResourceKey.value = resource.id;
    actionData.value.resource = resource.id;
  }
};
</script>

<style lang="scss">
.el-drawer__header {
  margin-bottom: 20px !important;
}

.el-drawer__body {
  padding: 8px 8px 8px 10px !important;
}

.drawer-flex {
  display: flex;
  height: 100%;
  position: relative;
}

.drawer-left {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 360px;
  transition: width 0.1s;
  background: #fff;
  z-index: 1;
}

.drawer-left.full-width {
  width: 100%;
}

.drawer-middle {
  width: 245px;
  min-width: 0;
  padding: 0px;
  z-index: 2;
  border-left: 1px solid #f0f0f0;
}

.drawer-right {
  width: 100%;
  min-width: 0;
  padding: 0px;
  background: #fafbfc;
}

.header-actions {
  margin-bottom: 20px;
}

.search-area {
  margin-bottom: 20px;
}

.tool {
  height: 55px;
  display: flex;
  align-items: center;
  gap: 20px;
}

.tool_logo {
  width: 42px;
  height: 42px;
  object-fit: contain;
}

.tool-detail-version {
  color: #888;
  font-size: 16px;
}

.table-scroll {
  flex: 1 1 0%;
  overflow: auto;
  min-height: 0;
}

.pagination-fixed {
  flex-shrink: 0;
  padding: 12px 0 0 0;
  background: #fff;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.03);
}

.version-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 55px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.version-desc {
  font-size: 16px;
  color: #222;
  font-weight: 500;
  margin-bottom: 4px;
}

.version-ver {
  font-size: 14px;
  color: #888;
}

.version-detail {
  width: 500px;
  min-width: 0;
  height: 100%;
  padding: 0 10px;
  background: #fff;
  z-index: 3;
  border-left: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  position: relative;
}

.drawer-footer-fixed {
  padding: 12px 0 12px 0;
  background: #fff;
  border-top: 1px solid #f0f0f0;
  text-align: right;
  /* 固定在底部 */
}

.el-scrollbar {
  flex: 1 1 0;
  min-height: 0;
}

.is-selected {
  background: #f0f7ff !important;
}

.drawer-action-scroll {
  height: calc(100vh - 100px); // 100px可根据实际header/footer高度调整
  overflow-y: auto;
  padding-right: 8px; // 防止滚动条遮挡内容
}

.param-list {
  width: 100%;
}

.param-item-vertical {
  display: block;
  margin-bottom: 18px;
  margin-top: 10px;
}

.param-label-vertical {
  color: #333;
  font-size: 14px;
  white-space: normal;
  word-break: break-all;
  line-height: 1.5;
  margin-bottom: 6px;
}

.param-input-vertical {
  width: 100%;
}
</style>
