<template>
  <div class="welcome-page">
    <div class="welcome-content">
      <div class="welcome-icon">
        <IconifyIconOffline
          class="icon"
          height="64px"
          icon="icon-park-outline:message-security"
          width="64px"
        />
      </div>
      <h1 class="welcome-title">欢迎来到作战室</h1>
      <p class="welcome-description">
        您可以在这里执行动作 / 事件<br />
        也可与其他用户进行实时沟通和协作
      </p>
      <div class="welcome-instructions">
        <p>
          <IconifyIconOffline
            class="icon"
            height="25px"
            icon="icon-park-outline:left"
            width="25px"
          />
          从左侧选择一个作战室加入，或创建新的作战室
        </p>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import IconifyIconOffline from "@/components/ReIcon/src/iconifyIconOffline";
defineOptions({
  name: "WelcomePage"
});
</script>

<style scoped>
.welcome-page {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-color: #f5f7fa;
}

.welcome-content {
  text-align: center;
  background-color: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  max-width: 600px;
}

.welcome-title {
  font-size: 24px;
  margin-bottom: 16px;
  color: #303133;
}

.welcome-description {
  color: #606266;
  margin-bottom: 30px;
  font-size: 16px;
}

.welcome-instructions {
  text-align: left;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.welcome-instructions p {
  margin: 12px 0;
  display: flex;
  align-items: center;
  gap: 10px;
}
</style>
