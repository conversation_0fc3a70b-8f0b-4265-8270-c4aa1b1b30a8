<script lang="ts" setup>
import { ref } from "vue";
import { IconifyIconOffline } from "@/components/ReIcon";
import { deleteTool } from "@/api/toolManagement";
import { ElMessage } from "element-plus";
import ResourcesAdd from "./resourcesAdd.vue";
import { Icon } from "@iconify/vue";

const props = defineProps({
  toolResources: {
    type: Array,
    default: () => []
  },
  toolModel: {
    type: Object,
    default: () => ({})
  },
  currentNodeData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(["refresh", "upload"]);

// ResourcesAdd组件引用
const resourcesAddRef = ref();

// 删除资源
const deleteResource = async (resource: any) => {
  try {
    const res = (await deleteTool({
      tool_name: resource.tool_name,
      name: resource.name
    })) as any;
    if (res.code === 0) {
      ElMessage.success(res.data.message);
      // 通知父组件刷新数据
      emit("refresh");
    } else {
      ElMessage.error("删除失败");
    }
  } catch (error) {
    console.error("删除资源失败:", error);
    ElMessage.error("删除失败");
  }
};

// 处理添加资源点击
const handleUploadClick = () => {
  resourcesAddRef.value.openDialog(
    props.toolModel.resources,
    props.toolModel.tool_name
  );
};

// 处理资源添加成功
const handleResourceAddSuccess = (data: any) => {
  // 只刷新资源列表
  emit("refresh", { name: data.name, refreshType: "resources" });
};

// 处理编辑资源
const handleEditResource = (resource: any) => {
  resourcesAddRef.value.openDialog(
    resource.resources,
    resource.tool_name,
    resource
  );
};
</script>

<template>
  <div class="resource-display">
    <!-- 应用名称 -->
    <div class="app-name">
      <span class="app-name-value">{{ currentNodeData.description }}</span>
    </div>
    <!-- 资源表格区域 -->
    <div class="content-container">
      <div class="action-bar">
        <Perms :value="['tool:c']">
          <el-button type="primary" @click="handleUploadClick">
            <IconifyIconOffline />
            添加资源
          </el-button>
        </Perms>
      </div>
      <el-scrollbar class="table-scrollbar">
        <el-table :data="toolResources" border style="width: 100%">
          <el-table-column type="expand">
            <template #default="props">
              <el-table
                :data="
                  Object.entries(props.row.resources || {}).map(
                    ([key, value]) => ({
                      name: key,
                      ...(typeof value === 'object' ? value : {})
                    })
                  )
                "
                border
                style="width: 90%; margin: 0 auto"
              >
                <el-table-column label="参数名称" prop="name" width="180" />
                <el-table-column label="参数描述" prop="description" />
                <el-table-column label="参数值" prop="value" width="200" />
              </el-table>
            </template>
          </el-table-column>
          <el-table-column label="资源名称" prop="name" />
          <el-table-column label="健康状态" prop="product_supplier" />
          <el-table-column label="资源描述" prop="description" />
          <el-table-column label="执行引擎">
            <template #default="scope">
              <el-tag v-if="scope.row.engine" size="small" type="warning">
                {{ scope.row.engine }}
              </el-tag>
              <span v-else />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template #default="scope">
              <Perms :value="['tool:r']">
                <el-tooltip :hide-after="0" content="编辑" placement="top">
                  <el-button
                    link
                    size="small"
                    type="primary"
                    @click="handleEditResource(scope.row)"
                  >
                    <Icon height="20" icon="ep:edit" width="20" />
                  </el-button>
                </el-tooltip>
              </Perms>
              <Perms :value="['tool:u']">
                <el-tooltip :hide-after="0" content="删除" placement="top">
                  <span class="ml-3">
                    <el-popconfirm
                      cancel-button-text="取消"
                      confirm-button-text="确认"
                      title="确认要删除该资源吗？"
                      @confirm="deleteResource(scope.row)"
                    >
                      <template #reference>
                        <el-button link size="small" type="danger">
                          <Icon height="20" icon="uiw:delete" width="20" />
                        </el-button>
                      </template>
                    </el-popconfirm>
                  </span>
                </el-tooltip>
              </Perms>
            </template>
          </el-table-column>
        </el-table>
      </el-scrollbar>
    </div>

    <!-- 资源添加组件 -->
    <ResourcesAdd ref="resourcesAddRef" @success="handleResourceAddSuccess" />
  </div>
</template>

<style scoped>
.resource-display {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.search-area {
  flex-shrink: 0;
  margin-bottom: 16px;
}

.content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.action-bar {
  flex-shrink: 0;
  margin-bottom: 10px;
  display: flex;
  justify-content: flex-end;
}

.table-scrollbar {
  flex: 1;
  overflow: hidden;
}

:deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
}

:deep(.el-table) {
  width: 100%;
}

.app-name {
  margin-bottom: 18px;
}

.app-name-value {
  font-size: 24px;
  font-weight: bold;
}
</style>
