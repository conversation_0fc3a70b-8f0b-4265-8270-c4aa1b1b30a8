/**
 * 智能解析自定义名称，把 event_name 拆分为字段和自定义内容
 * @param event_name 后端返回的事件名
 * @param logFields 日志字段名数组
 * @param getLogFieldValue (field) => 字段值
 * @returns 解析后的 selectedFields（如 ["aa", "device_type"]）
 */
export function parseEventNameSmart(
  event_name: string,
  logFields: string[],
  getLogFieldValue: (field: string) => string
): string[] {
  // 去除首尾引号
  let name = event_name;
  try {
    name = JSON.parse(event_name);
  } catch {
    name = event_name.replace(/^"+|"+$/g, "");
  }
  const selectedFields: string[] = [];
  let remain = name;

  // 优先用字段值匹配
  while (remain.length > 0) {
    let matched = false;
    for (const field of logFields) {
      const value = getLogFieldValue(field);
      if (value && remain.startsWith(value)) {
        selectedFields.push(field);
        remain = remain.slice(value.length);
        matched = true;
        break;
      }
    }
    if (!matched) {
      // 没有字段值匹配，取第一个字符
      selectedFields.push(remain[0]);
      remain = remain.slice(1);
    }
  }
  // 合并连续的自定义字符
  const result: string[] = [];
  let buffer = "";
  for (const item of selectedFields) {
    if (logFields.includes(item)) {
      if (buffer) {
        result.push(buffer);
        buffer = "";
      }
      result.push(item);
    } else {
      buffer += item;
    }
  }
  if (buffer) result.push(buffer);
  return result;
}

/**
 * 将后端条件数组 connect_type 还原为前端表单结构
 */
export function convertConditionsForEdit(conditions: any[]): any[] {
  if (!conditions || !conditions.length) return [];
  const arr = JSON.parse(JSON.stringify(conditions));
  for (let i = arr.length - 1; i > 0; i--) {
    arr[i].connect_type = arr[i - 1].connect_type;
  }
  if ("connect_type" in arr[0]) delete arr[0].connect_type;
  return arr;
}

/**
 * 根据剧本ID数组和选项，返回选中的 value 数组
 */
export function fillScriptOptionsFromIds(
  ids: string[],
  scriptOptions: { label: string; value: string }[]
): string[] {
  return ids.map(id => {
    const found = scriptOptions.find(opt => opt.value === id);
    return found ? found.value : id;
  });
}
