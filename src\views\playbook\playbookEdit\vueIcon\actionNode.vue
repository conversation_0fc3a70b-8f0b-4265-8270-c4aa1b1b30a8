<template>
  <div class="vue-wrap">
    <div class="vue-html">
      <div class="vue-html-title">
        <div class="vue-html-title-left">
          <svg
            class="icon"
            height="20px"
            p-id="6449"
            t="1745398574021"
            version="1.1"
            viewBox="0 0 1024 1024"
            width="20px"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M853.333333 298.666667h-213.333333V170.666667c0-72.533333-55.466667-128-128-128H170.666667C98.133333 42.666667 42.666667 98.133333 42.666667 170.666667v341.333333c0 72.533333 55.466667 128 128 128h128v213.333333c0 72.533333 55.466667 128 128 128h426.666666c72.533333 0 128-55.466667 128-128V426.666667c0-72.533333-55.466667-128-128-128zM170.666667 554.666667c-25.6 0-42.666667-17.066667-42.666667-42.666667V170.666667c0-25.6 17.066667-42.666667 42.666667-42.666667h341.333333c25.6 0 42.666667 17.066667 42.666667 42.666667v128h-128c-72.533333 0-128 55.466667-128 128v128H170.666667z m384-170.666667v128c0 25.6-17.066667 42.666667-42.666667 42.666667H384v-128c0-25.6 17.066667-42.666667 42.666667-42.666667h128z m341.333333 469.333333c0 25.6-17.066667 42.666667-42.666667 42.666667H426.666667c-25.6 0-42.666667-17.066667-42.666667-42.666667v-213.333333h128c72.533333 0 128-55.466667 128-128V384h213.333333c25.6 0 42.666667 17.066667 42.666667 42.666667v426.666666z"
              fill="#2c2c2c"
              p-id="6450"
            />
          </svg>
          <div class="text">动作</div>
        </div>
        <div class="vue-html-title-right">
          <div class="copy" @click.stop="$_copyNode()">
            <svg
              class="icon"
              height="20"
              p-id="2839"
              t="1748240616953"
              version="1.1"
              viewBox="0 0 1024 1024"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M720 192h-544A80.096 80.096 0 0 0 96 272v608C96 924.128 131.904 960 176 960h544c44.128 0 80-35.872 80-80v-608C800 227.904 764.128 192 720 192z m16 688c0 8.8-7.2 16-16 16h-544a16 16 0 0 1-16-16v-608a16 16 0 0 1 16-16h544a16 16 0 0 1 16 16v608z"
                fill="#2c2c2c"
                p-id="2840"
              />
              <path
                d="M848 64h-544a32 32 0 0 0 0 64h544a16 16 0 0 1 16 16v608a32 32 0 1 0 64 0v-608C928 99.904 892.128 64 848 64z"
                fill="#2c2c2c"
                p-id="2841"
              />
              <path
                d="M608 360H288a32 32 0 0 0 0 64h320a32 32 0 1 0 0-64zM608 520H288a32 32 0 1 0 0 64h320a32 32 0 1 0 0-64zM480 678.656H288a32 32 0 1 0 0 64h192a32 32 0 1 0 0-64z"
                fill="#2c2c2c"
                p-id="2842"
              />
            </svg>
          </div>
          <el-popconfirm
            cancel-button-text="取消"
            confirm-button-text="确认"
            title="确认删除该节点吗？"
            width="auto"
            @confirm.stop="$_deleteNode()"
          >
            <template #reference>
              <div class="delete" @click.stop>
                <svg
                  class="icon"
                  height="20"
                  p-id="3915"
                  t="1748240739833"
                  version="1.1"
                  viewBox="0 0 1024 1024"
                  width="20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M106.666667 213.333333h810.666666v42.666667H106.666667z"
                    fill="#d81e06"
                    p-id="3916"
                  />
                  <path
                    d="M640 128v42.666667h42.666667V128c0-23.573333-19.093333-42.666667-42.538667-42.666667H383.872A42.496 42.496 0 0 0 341.333333 128v42.666667h42.666667V128h256z"
                    fill="#d81e06"
                    p-id="3917"
                  />
                  <path
                    d="M213.333333 896V256H170.666667v639.957333C170.666667 919.552 189.653333 938.666667 213.376 938.666667h597.248C834.218667 938.666667 853.333333 919.68 853.333333 895.957333V256h-42.666666v640H213.333333z"
                    fill="#d81e06"
                    p-id="3918"
                  />
                  <path
                    d="M320 341.333333h42.666667v384h-42.666667zM490.666667 341.333333h42.666666v384h-42.666666zM661.333333 341.333333h42.666667v384h-42.666667z"
                    fill="#d81e06"
                    p-id="3919"
                  />
                </svg>
              </div>
            </template>
          </el-popconfirm>
        </div>
      </div>
      <div class="vue-html-container">
        <el-form>
          <el-form-item>{{ props.properties.app_name }}</el-form-item>
        </el-form>
      </div>
    </div>
  </div>

  <div v-if="props.properties.isWebSocket" class="webSocket">
    <el-button
      v-if="playbookStore.webSocketMsg"
      style="width: 200px"
      type="success"
      @click="openDialog()"
      >成功(用时9.99s)(点击查看)
    </el-button>
    <el-button v-else loading style="width: 200px" @click="openDialog()"
      >运行中
    </el-button>
  </div>
</template>

<script lang="ts" setup>
import { ElButton, ElForm, ElFormItem, ElPopconfirm } from "element-plus";
import usePlaybookStore from "@/store/modules/playbook";

const props = defineProps({
  id: {
    type: String
  },
  properties: {
    type: Object,
    required: true
  },
  model: {
    type: Object
  },
  graphModel: {
    type: Object
  },
  text: {
    type: String
  },
  onBtnCopyClick: Function,
  onBtnDelClick: Function,
  onBtnWebSocket: Function
});

const playbookStore = usePlaybookStore();

//打开webSocket通信结果dialog
const openDialog = () => {
  props.onBtnWebSocket();
};

//复制节点
const $_copyNode = () => {
  props.onBtnCopyClick();
  console.log(props.graphModel.nodes);
  console.log(props.model.properties);
};

//删除节点
const $_deleteNode = () => {
  props.onBtnDelClick();
  console.log(props.graphModel.nodes);
  console.log(props.model.properties);
};
</script>

<style lang="scss" scoped>
.vue-wrap {
  display: flex;
  align-items: center;
  justify-content: center;

  .vue-html {
    width: 190px;
    height: 90px;
    overflow: hidden;
    border: 1px solid #ccc;
    border-radius: 5px;
    filter: drop-shadow(3px 3px 3px rgba(50, 50, 0, 0.5));

    .vue-html-title {
      display: flex;
      justify-content: space-between;
      height: 30px;
      color: black;
      padding: 3px 5px;
      background: linear-gradient(#f2f2ff 0%, rgba(252, 252, 255, 1) 100%);

      .vue-html-title-left {
        display: flex;

        .text {
          padding-left: 5px;
        }
      }

      .vue-html-title-right {
        display: flex;

        .copy {
          cursor: pointer;
        }

        .delete {
          cursor: pointer;
          margin-left: 5px;
        }
      }
    }

    .vue-html-container {
      display: flex;
      justify-content: center;
      height: 70px;
      background: white;
    }
  }
}

.webSocket {
  width: 200px;
  height: auto;
  background: skyblue;
  color: white;
  margin-top: 10px;
  border-radius: 3%;
  overflow: hidden;

  .action-collapse-title {
    display: flex;
    align-items: center;
    margin: 0px 5px;

    .title-item {
      margin-right: 5px;
    }
  }
}
</style>
