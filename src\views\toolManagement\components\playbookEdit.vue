<template>
  <el-dialog
    v-model="visible"
    title="已关联的剧本"
    width="1000px"
    @close="handleClose"
  >
    <div>
      <div v-if="row && row.playbook && row.playbook.length">
        <el-table :data="row.playbook" border style="width: 100%">
          <el-table-column label="剧本名称" prop="name" />
          <el-table-column label="版本号" prop="version" />
          <el-table-column label="状态" prop="status">
            <template #default="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
                {{ scope.row.status === 1 ? "已发布" : "草稿" }}
              </el-tag>
            </template>
          </el-table-column>
          <!-- 操作 -->
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <el-tooltip :hide-after="0" content="编辑" placement="top">
                <el-button
                  link
                  type="primary"
                  @click="
                    toDetail(
                      {
                        flow_id: scope.row.id,
                        version_id: scope.row.pv_id,
                        status: scope.row.status
                      },
                      'params'
                    )
                  "
                >
                  <Icon
                    height="20"
                    icon="material-symbols:edit-square-outline"
                    width="20"
                  />
                </el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-else>暂无关联剧本</div>
    </div>
  </el-dialog>
</template>

<script setup>
import { defineEmits, defineProps, ref, watch } from "vue";
import { useDetail } from "@/views/playbook/hooks";
import { Icon } from "@iconify/vue";

const { toDetail } = useDetail();
const props = defineProps({
  modelValue: Boolean,
  row: Object
});
const emit = defineEmits(["update:modelValue"]);

const visible = ref(props.modelValue);

watch(
  () => props.modelValue,
  val => {
    visible.value = val;
  }
);

watch(visible, val => {
  emit("update:modelValue", val);
  if (val) {
    // 只在弹窗打开时打印一次
    console.log("props.row", props.row);
  }
});

const handleClose = () => {
  emit("update:modelValue", false);
};

const handleEdit = row => {
  console.log(row);
};
</script>
