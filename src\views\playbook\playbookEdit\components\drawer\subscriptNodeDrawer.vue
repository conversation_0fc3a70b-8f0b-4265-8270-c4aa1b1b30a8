<template>
  <div>
    <el-drawer v-model="isShowDrawer" size="50%">
      <template #header>
        <div class="subscript-drawer-title">节点信息</div>
      </template>
      <template #default>
        <div>
          <el-form
            ref="nodeEditFormRef"
            :model="nodeData.properties"
            :rules="rules"
            label-width="100px"
          >
            <!--            <el-form-item>{{ nodeData.properties }}</el-form-item>-->
            <el-form-item label="节点ID:">
              <el-button link type="primary" @click="$_copyNodeId(nodeData.id)"
                >{{ nodeData.id }}
              </el-button>
            </el-form-item>
            <el-form-item label="节点标题:" prop="node_name">
              <el-input v-model="nodeData.properties.node_name" />
            </el-form-item>
            <el-form-item label="剧本列表:">
              <el-select
                v-model="nodeData.properties.id"
                value-key="id"
                @change="switchPlaybookVersionsList"
              >
                <template #header>
                  <el-input
                    v-model="searchKeyword"

                    @input="getPlaybookList({ key: searchKeyword })"
                  >
                    <template #append>
                      <el-button :icon="Search" />
                    </template>
                  </el-input>
                </template>
                <el-option
                  v-for="item in tableData"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
                <template #footer>
                  <el-pagination
                    v-model:current-page="currentPage"
                    v-model:page-size="pageSize"
                    :page-sizes="[20, 30, 40, 50]"
                    :total="total"
                    layout="total, sizes, prev, pager, next, jumper"
                    size="small"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                  />
                </template>
              </el-select>
            </el-form-item>
            <el-form-item label="剧本版本:">
              <el-select
                v-model="nodeData.properties.flow_id"
                @change="getPlaybookFlowInput"
              >
                <el-option
                  v-for="item in versionList"
                  :key="item.version_id"
                  :label="item.version"
                  :value="item.version_id"
                >
                  <span style="float: left">{{ item.version }}</span>
                  <span
                    style="
                      float: right;
                      color: var(--el-text-color-secondary);
                      font-size: 13px;
                    "
                  >
                    {{ item.change_desc }}
                  </span>
                </el-option>
                <template #empty>
                  <div>暂无已发布剧本</div>
                </template>
              </el-select>
            </el-form-item>
            <el-form-item
              v-if="Object.keys(nodeData.properties.input_name).length > 0"
              label="输入参数:"
            >
              <div class="param-list" style="width: 100%">
                <el-form>
                  <!-- v-for中，先后顺序决定值，和名称无关 -->
                  <el-form-item
                    v-for="(item, index) in nodeData.properties.input_name"
                    :key="index"
                    style="padding-bottom: 10px"
                  >
                    <div class="param-item">
                      <div>{{ item.description }}</div>
                      <div>
                        <el-input
                          v-model="nodeData.properties.input[item.key]"
                          style="width: 100%"
                        />
                      </div>
                    </div>
                  </el-form-item>
                </el-form>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #footer>
        <div>
          <el-button @click="closeNodeEditDrawer">取消</el-button>
          <el-button type="primary" @click="nodeEditConfirm(nodeEditFormRef)"
            >确定
          </el-button>
        </div>
      </template>
    </el-drawer>
    <PlaybookViewDialog
      ref="playbookViewDialogRef"
      v-model="isShowViewPlaybook"
    />
  </div>
</template>

<script lang="ts" setup>
import LogicFlow from "@logicflow/core";
import { nextTick, onMounted, reactive, ref } from "vue";
import { Search } from "@element-plus/icons-vue";
import {
  apiGetPlaybookInput,
  apiGetPlaybookList,
  apiGetPlaybookVersionsList
} from "@/api/playbook";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import PlaybookViewDialog from "@/views/playbook/components/playbookViewDialog.vue";
import { useDetail } from "@/views/playbook/hooks";

interface RuleForm {
  node_name: string;

  [key: string]: any;
}

const props = defineProps({
  lf: LogicFlow
});

const isShowDrawer = ref(false);
const isShowViewPlaybook = ref(false);
const isShowFlowInputDialog = ref(false);
const nodeData = ref<LogicFlow.NodeData>();
const searchKeyword = ref();
const activeNames = ref([]);
const currentPage = ref(1); //当前页码
const pageSize = ref(15); //一页的数据量
const total = ref(0); //总数据量
const tableData = ref();
const versionList = ref([]);
const loading = ref(true);
const playbookViewDialogRef = ref();
const playbookFlowInputDialogRef = ref();
const nodeEditFormRef = ref<FormInstance>();
//获取剧本ID、剧本版本ID和剧本状态
const playbookId = ref();
const playbookVersionId = ref();
const playbookStatus = ref();
const { getParameter } = useDetail();
playbookId.value = getParameter.flow_id;
playbookVersionId.value = getParameter.version_id;
playbookStatus.value = getParameter.status;

const rules = reactive<FormRules<RuleForm>>({
  node_name: [{ required: true, message: "节点标题不能为空", trigger: "blur" }]
});

onMounted(() => {
  getPlaybookList({});
});
//打开抽屉(判断新旧节点)
const openNodeEditDrawer = (data: LogicFlow.NodeData) => {
  nodeData.value = data;
  getPlaybookList({});
  //判断新旧节点
  if (nodeData.value.properties.isOld === true) {
    //旧节点请求剧本版本列表
    getPlaybookVersionsList(nodeData.value.properties.id);
  } else {
    nodeData.value.properties.input_name = {};
    nodeData.value.properties.input = {};
  }
  isShowDrawer.value = true;
  //清空残留的表单校验
  nextTick(() => {
    nodeEditFormRef.value.clearValidate(); // 只清除清除验证
  });
};

//关闭抽屉
const closeNodeEditDrawer = () => {
  isShowDrawer.value = false;
};

//请求剧本列表数据
const getPlaybookList = async ({ page = 1, size = 15, key = "" }) => {
  currentPage.value = page;
  pageSize.value = size;
  searchKeyword.value = key;
  let res: any = await apiGetPlaybookList({
    page: currentPage.value,
    size: pageSize.value,
    keyword: searchKeyword.value
  });
  tableData.value = res.data.data.filter(item => item.id !== playbookId.value);
  total.value = res.data.total;
  console.log(tableData.value);
};

//页面展示数据量改变时触发
const handleSizeChange = (val: number) => {
  getPlaybookList({ size: val });
};

//页脚改变时触发
const handleCurrentChange = (val: number) => {
  getPlaybookList({ page: val });
};

//预览剧本
const viewPlaybook = (item: any) => {
  playbookViewDialogRef.value.viewLogicflowData(item);
  console.log(item);
};

//获取剧本版本
const getPlaybookVersionsList = async (id: any) => {
  let res: any = await apiGetPlaybookVersionsList({ playbook_id: id });
  versionList.value = res.data.filter(item => item.status === 1);
  console.log("versionList.value:", versionList.value);
};

//切换剧本版本
const switchPlaybookVersionsList = async (id: any) => {
  nodeData.value.properties.flow_id = "";
  nodeData.value.properties.input_name = {};
  nodeData.value.properties.input = {};
  let res: any = await apiGetPlaybookVersionsList({ playbook_id: id });
  versionList.value = res.data.filter(item => item.status === 1);
  console.log("versionList.value:", versionList.value);
};

//根据剧本版本ID获取该剧本是否需要填写输入参数
const getPlaybookFlowInput = async (id: any) => {
  nodeData.value.properties.input_name = {};
  nodeData.value.properties.input = {};
  console.log(id);
  let res: any = await apiGetPlaybookInput({ flow_id: id });
  console.log(res);
  //该剧本有输入参数，则显示dialog让用户填写参数
  if (res.code === 0 && res.data.length > 0) {
    nodeData.value.properties.input_name = res.data.map(item => {
      const key = Object.keys(item)[0]; // 提取动态键名（如'abc'）
      return {
        key: key,
        type: item[key].input.type, // 提取输入类型
        description: item[key].input.description // 提取描述文本
      };
    });
  }
};
//确认并保存数据到nodeData的properties上
const nodeEditConfirm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      nodeData.value.properties.isOld = true;
      //推送数据到节点的properties上并更新(很重要，没送上来你前面怎么改都没用)
      props.lf.setProperties(nodeData.value.id, nodeData.value.properties!);
      console.log(props.lf.getGraphData());
      isShowDrawer.value = false;
    } else {
      console.log("error submit!", fields);
    }
  });
};

// 复制节点ID
const $_copyNodeId = async (id: string) => {
  try {
    // 使用现代Clipboard API替代已废弃的execCommand
    await navigator.clipboard.writeText(`\$\{${id}\}`);
    ElMessage.success("已复制该节点ID");
  } catch (err) {
    console.error("复制失败:", err);
    // 添加失败反馈（可根据项目需求替换为错误提示）
  }
};

//对外暴露方法
defineExpose({
  openNodeEditDrawer,
  closeNodeEditDrawer
});
</script>

<style lang="scss" scoped>
.playbook-list {
  width: 100%;
}

.playbook-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.param-item {
  width: 100%;
  border: 1px solid #ccc;
  padding: 0px 10px 10px 10px;
}
</style>
