<script lang="ts" setup>
import { nextTick, onMounted, ref, watch } from "vue";
import { Search } from "@element-plus/icons-vue";
import {
  getToolActionList,
  getToolDetail,
  getTool<PERSON>ist,
  getTool<PERSON>ode<PERSON>,
  getT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ToolHealthConfig,
  updateToolHealthCheck
} from "@/api/toolManagement";
import ListDetailed from "./components/ListDetailed.vue";
import ToolUpload from "./components/toolUpload.vue";
import ReHealth from "@/components/ReHealth/index.vue";
import ToolSet from "./components/toolSet.vue";
import { ElMessage } from "element-plus";
import playbookEdit from "./components/playbookEdit.vue";
import { apiGetPlaybookPermissionsCurrent } from "@/api/playbook";

// 左侧树形数据
const treeData = ref<any[]>([]);
const tableData = ref<any[]>([]);
const currentNode = ref<string>("");
const currentNodeData = ref<any>(null);
const searchKeyword = ref("");
const treeRef = ref();
const loading = ref(false);

// 资源相关数据
const toolResources = ref([]);
const toolModel = ref({
  id: "",
  tool_name: "",
  resources: {}
});

const listDetailedRef = ref();

// 分页相关
const treePage = ref(1);
const treeSize = ref(10);
const treeTotal = ref(0);

const defaultProps = {
  children: "children",
  label: "label"
};

// 监听搜索关键字变化
watch(searchKeyword, val => {
  treeRef.value?.filter(val);
  ToolList(); // 搜索时自动刷新列表
});

// 格式化时间
const formatDateTime = (dateTimeString: string) => {
  if (!dateTimeString) return "";
  const date = new Date(dateTimeString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// 获取资源列表
const fetchToolResources = async (data: any) => {
  try {
    // 获取资源模型
    const modelRes = (await getToolModel({
      name: data.name
    })) as any;
    if (modelRes?.data) {
      toolModel.value = modelRes.data;
      console.log(modelRes);
    }
    // 获取资源详情
    const detailRes = (await getToolDetail({
      name: data.name
    })) as any;
    // 如果返回的是数组就使用，否则就设为空数组
    toolResources.value = Array.isArray(detailRes?.data) ? detailRes.data : [];
  } catch (error) {
    toolResources.value = [];
  }
};

// 处理树节点点击
const handleNodeClick = async (data: any) => {
  currentNode.value = data.id;
  currentNodeData.value = data;
  if (data.children?.length > 0) {
    await fetchToolResources(data);
    tableData.value = [];
    return;
  }
  loading.value = true;
  try {
    await fetchToolActions(data);
  } catch (error) {
    console.error("获取动作列表失败:", error);
  } finally {
    loading.value = false;
  }
};

// 获取应用列表
const ToolList = async () => {
  const lastSelectedId = currentNode.value;
  loading.value = true;
  try {
    const res = (await getToolList({
      page: treePage.value,
      size: treeSize.value,
      key: searchKeyword.value
    })) as any;
    if (res?.data?.tools) {
      treeData.value = res.data.tools.map((item: any) => ({
        id: "tool-" + item.id,
        label: item.description,
        name: item.name,
        description: item.description,
        version: item.version,
        internal: item.internal,
        children: [],
        logo: item.logo
      }));
      await Promise.all(treeData.value.map(item => fetchToolVersions(item)));
      await nextTick();

      let nodeToSelect = null;
      // 尝试恢复之前的选择
      if (lastSelectedId) {
        const findNode = (nodes, id) => {
          for (const node of nodes) {
            if (node.id === id) return node;
            if (node.children) {
              const found = findNode(node.children, id);
              if (found) return found;
            }
          }
          return null;
        };
        nodeToSelect = findNode(treeData.value, lastSelectedId);
      }

      // 如果找不到或者之前就没选，则选择第一个
      if (!nodeToSelect && treeData.value.length > 0) {
        nodeToSelect = treeData.value[0];
      }

      if (nodeToSelect) {
        currentNode.value = nodeToSelect.id;
        treeRef.value?.setCurrentKey(nodeToSelect.id);
        handleNodeClick(nodeToSelect);
      }

      treeTotal.value = res.data.total || 0;
    }
  } catch (error) {
    console.error("获取应用列表失败:", error);
  } finally {
    loading.value = false;
  }
  //获取用户剧本权限
  let res2: any = await apiGetPlaybookPermissionsCurrent({});
  if (res2.code == 0) {
    let playbookPermissions = Object.fromEntries(
      res2.data.map(item => [item.id, item.permissions])
    );
    localStorage.setItem(
      "playbookPermissions",
      JSON.stringify(playbookPermissions)
    );
  }
};

// 获取工具版本列表
const fetchToolVersions = async (node: any) => {
  try {
    const res = (await getToolVersionList({
      name: node.name
    })) as any;
    if (res.code === 0 && Array.isArray(res.data)) {
      node.children = res.data.map((version: any) => ({
        id: "ver-" + version.id,
        uuid: version.id,
        label: `版本号：${version.version}`,
        name: node.name,
        version: version.version,
        ctime: `(${formatDateTime(version.ctime)})`,
        tag: node.tag,
        internal: node.internal
      }));
    }
  } catch (error) {
    console.error(`获取 ${node.name} 的版本列表失败:`, error);
    node.children = [];
  }
};

// 获取动作列表
const fetchToolActions = async (data: any) => {
  try {
    const version = data.version.includes("版本号：")
      ? data.version.replace("版本号：", "")
      : data.version;
    const res = (await getToolActionList({
      name: data.name,
      version: version
    })) as any;
    if (res.code === 0) {
      tableData.value = res.data.actions || [];
    }
    console.log(res);
  } catch (error) {
    console.error("获取动作列表失败:", error);
    tableData.value = [];
  }
};

// 处理应用列表分页变化
const handleTreeSizeChange = (val: number) => {
  treeSize.value = val;
  treePage.value = 1;
  ToolList();
};

const handleTreeCurrentChange = (val: number) => {
  treePage.value = val;
  ToolList();
};

// 处理工具上传成功后的刷新
const handleToolUploadSuccess = () => {
  ToolList();
};

// 健康检查相关
const cronValue = ref("");
const handleHealthSave = async (config: any) => {
  console.log("工具健康检查配置：", config);
  // 这里可以调用对应的 API
  const res = (await updateToolHealthCheck({
    switch: config.switch,
    cron: config.cronExpression
  })) as any;
  if (res.code === 0) {
    ElMessage.success(res.data.message);
    ToolList();
  }
};
const getToolHealthConfig = async (callback: (data: any) => void) => {
  const res = (await ToolHealthConfig({})) as any;
  if (res.code === 0) {
    callback(res.data);
  }
};

// 挂载时加载数据
onMounted(() => {
  ToolList();
});

const forceExpand = (data, node, instance) => {
  // 强制展开所有节点
  nextTick(() => {
    instance.store.nodesMap[node.key].expanded = true;
  });
};
////////////////////////////////
const showPlaybookEdit = ref(false);
const currentRow = ref(null);

const handlePlaybookClick = row => {
  currentRow.value = row;
  showPlaybookEdit.value = true;
  console.log(row);
};

// 过滤树节点的方法（只保留searchKeyword相关逻辑）
const filterNode = (value: string, data: any) => {
  if (!value) return true;
  const searchValue = value.toLowerCase();
  const currentLabel = data.label.toLowerCase();
  // 如果是父节点
  if (data.children) {
    if (currentLabel.includes(searchValue)) {
      return true;
    }
    return data.children.some(child =>
      child.label.toLowerCase().includes(searchValue)
    );
  }
  // 如果是子节点,检查父节点是否匹配
  const parentNode = treeData.value.find(item =>
    item.children?.some(child => child.id === data.id)
  );
  if (parentNode && parentNode.label.toLowerCase().includes(searchValue)) {
    return true;
  }
  return currentLabel.includes(searchValue);
};
</script>

<template>
  <div class="tool-all-container bg-white p-5 dark:bg-[#141414]">
    <div class="layout-container">
      <!-- 左侧树形控件 -->
      <div class="left-tree">
        <div class="tree-header">
          <div class="header-top">
            <h3 class="section-title">应用列表</h3>
            <div class="header-actions">
              <Perms :value="['tool:r']">
                <ReHealth
                  v-model="cronValue"
                  style="display: none"
                  tip-text="提示：工具健康定时检查设置为每天执行一次即可！"
                  title="工具健康检查配置"
                  @save="handleHealthSave"
                  @get-config="getToolHealthConfig"
                />
                <ToolUpload @refresh="handleToolUploadSuccess" />
              </Perms>
            </div>
          </div>
          <div class="search-wrapper">
            <Perms :value="['tool:r']">
              <el-input
                v-model="searchKeyword"
                class="search-input"
                clearable
                placeholder="搜索工具..."
                size="default"
              >
                <template #prefix>
                  <el-icon>
                    <Search />
                  </el-icon>
                </template>
              </el-input>
            </Perms>
          </div>
        </div>
        <div class="tree-content">
          <el-scrollbar>
            <div v-loading="loading">
              <el-tree
                ref="treeRef"
                :data="treeData"
                :expand-on-click-node="false"
                :filter-node-method="filterNode"
                :props="defaultProps"
                default-expand-all
                node-key="id"
                @node-click="handleNodeClick"
                @node-collapse="forceExpand"
                @node-expand="forceExpand"
              >
                <template #default="{ node, data }">
                  <div
                    :class="{
                      'is-child': !data.children || !data.children.length,
                      'is-selected': currentNode === data.id
                    }"
                    class="custom-tree-node"
                  >
                    <div class="node-content">
                      <!-- 父节点显示图标 -->
                      <div
                        v-if="data.children && data.children.length"
                        class="parent-icon"
                      >
                        <img
                          v-if="data.logo"
                          :src="`data:image/png;base64,${data.logo}`"
                          alt="工具"
                          class="node-icon"
                        />
                      </div>
                      <span
                        :class="{
                          'parent-node': data.children && data.children.length,
                          'child-node': !data.children || !data.children.length
                        }"
                      >
                        {{ node.label }}
                        <span v-if="!data.children" class="time-text">
                          {{ data.ctime }}
                        </span>
                      </span>
                    </div>
                    <ToolSet :node="data" @refresh="ToolList" />
                  </div>
                </template>
              </el-tree>
            </div>
          </el-scrollbar>
        </div>
        <!-- 应用列表分页 -->
        <div class="tree-pagination">
          <el-pagination
            v-model:current-page="treePage"
            v-model:page-size="treeSize"
            :page-sizes="[10, 20, 50]"
            :pager-count="5"
            :total="treeTotal"
            background
            layout="total, sizes, prev, pager, next"
            size="small"
            @size-change="handleTreeSizeChange"
            @current-change="handleTreeCurrentChange"
          />
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <div class="right-content">
        <el-scrollbar>
          <template v-if="treeData.length === 0">
            <el-empty description="暂无工具数据" />
          </template>
          <template v-else>
            <!-- 父节点展示资源列表 -->
            <template v-if="currentNodeData?.children">
              <ListDetailed
                ref="listDetailedRef"
                :current-node-data="currentNodeData"
                :tool-model="toolModel"
                :tool-resources="toolResources"
                @refresh="
                  data => {
                    fetchToolResources(data);
                    ToolList();
                  }
                "
              />
            </template>
            <!-- 动作表格区域 -->
            <template v-if="currentNodeData && !currentNodeData.children">
              <div class="app-name">
                <span class="app-name-value">{{
                  currentNodeData.description
                }}</span>
              </div>
              <el-scrollbar class="table-scrollbar">
                <el-table
                  v-loading="loading"
                  :data="tableData"
                  border
                  style="width: 100%"
                >
                  <el-table-column
                    align="center"
                    label="序号"
                    type="index"
                    width="70"
                  />
                  <el-table-column
                    label="动作名称"
                    min-width="200"
                    prop="name"
                  />
                  <el-table-column
                    label="动作分类"
                    prop="classify"
                    width="120"
                  />
                  <el-table-column
                    label="描述"
                    min-width="250"
                    prop="func"
                    show-overflow-tooltip
                  />
                  <el-table-column align="center" label="关联剧本" width="100">
                    <template #default="scope">
                      <el-link
                        type="primary"
                        @click="handlePlaybookClick(scope.row)"
                      >
                        <template v-if="Array.isArray(scope.row.playbook)">
                          {{ scope.row.playbook.length }}
                        </template>
                      </el-link>
                    </template>
                  </el-table-column>
                </el-table>
              </el-scrollbar>
            </template>
          </template>
        </el-scrollbar>
      </div>
    </div>
    <!-- 剧本版本列表 -->
    <playbookEdit v-model="showPlaybookEdit" :row="currentRow" />
  </div>
</template>

<style lang="scss" scoped>
.tool-all-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 5px;
  // height: calc(100vh - 111px);
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
}

.layout-container {
  width: 100%;
  height: 100%;
  display: flex;
  overflow: hidden;
}

.left-tree {
  width: 27%;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--el-border-color-light);
  background-color: var(--el-bg-color-overlay);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.tree-header {
  padding: 0 16px;
  margin-bottom: 16px;
  background-color: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-lighter);
  flex-shrink: 0;
}

.header-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.search-wrapper {
  display: flex;
  flex-direction: column;
  padding-bottom: 10px;

  .search-input {
    :deep(.el-input__wrapper) {
      box-shadow: 0 0 0 1px var(--el-border-color-light);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 0 0 1px var(--el-color-primary-light-3);
      }

      &.is-focus {
        box-shadow: 0 0 0 1px var(--el-color-primary);
      }
    }

    :deep(.el-input__inner) {
      &::placeholder {
        color: var(--el-text-color-placeholder);
      }
    }
  }
}

.tree-content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.right-content {
  flex: 1;
  padding: 10px;
  overflow: hidden;
  position: relative;
}

.table-scrollbar {
  height: calc(100vh - 180px);
  overflow: hidden;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  box-sizing: border-box;
  width: 100%;
}

.node-content {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
  overflow: hidden;
}

.parent-icon,
.child-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

// 图标样式
.node-icon {
  max-width: 32px;
  max-height: 32px;
  width: auto;
  height: auto;
  object-fit: contain;
}

.version-icon {
  max-width: 36px;
  max-height: 36px;
  opacity: 0.7;
  transform: scale(0.9);
}

.parent-node {
  font-weight: 600;
  color: var(--el-color-primary);
  font-size: 16px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.child-node {
  font-weight: normal;
  color: var(--el-text-color-primary);
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.el-tree-node__content) {
  height: auto;
  min-height: auto;
  padding: 0;
  flex-wrap: nowrap;
}

:deep(.el-tree-node__content:hover) {
  background-color: var(--el-fill-color-light);
}

.tree-pagination {
  padding: 8px;
  flex-shrink: 0;
  border-top: 1px solid var(--el-border-color-lighter);
}

:deep(.el-pagination) {
  justify-content: center;
  white-space: nowrap;
}

:deep(.el-scrollbar) {
  height: 100%;
}

:deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
}

:deep(.el-table) {
  height: auto !important;
  max-height: calc(100% - 60px);
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  padding-top: 15px;
}

.time-text {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-left: 8px;
}

:deep(.el-tree-node__expand-icon) {
  display: none !important;
}

// 让所有子节点内容左对齐
:deep(.custom-tree-node.is-child) {
  // margin-left: 32px; // 24px为el-tree默认缩进宽度，可根据实际调整
  padding-left: 32px;
}

// 只给一级节点（父节点）加下间距
:deep(.el-tree > .el-tree-node) {
  margin-bottom: 18px; // 你想要的间距
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.is-selected {
  background: var(--el-color-primary-light-9, #ecf5ff);
  color: var(--el-color-primary, #268ef6);
  border-radius: 4px;
}
</style>
