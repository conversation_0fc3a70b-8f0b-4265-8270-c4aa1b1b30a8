# LION-WEB

AIOE 产品前端

基于 pure-vue-admin + logicflow

## 本地开发说明

首先需要自行在根目录下创建 `.env` 文件，内容如下：

```
# 平台本地运行端口号
VITE_PORT = 8848

# 是否隐藏首页 隐藏 true 不隐藏 false （勿删除，VITE_HIDE_HOME只需在.env文件配置）
VITE_HIDE_HOME = false

# 线上环境平台打包路径
VITE_PUBLIC_PATH = /

# 线上环境路由历史模式（Hash模式传"hash"、HTML5模式传"h5"、Hash模式带base参数传"hash,base参数"、HTML5模式带base参数传"h5,base参数"）
VITE_ROUTER_HISTORY = "hash"

# host主机地址，0.0.0.0 表示允许所有网络访问
VITE_LION_MGMT_HOST_PATH_PREFIX = "0.0.0.0"

# 后端API域名
VITE_LION_MGMT_HOST = "https://aioe.hongcloud.net"

# 后端API路径前缀
VITE_LION_MGMT_HOST_PATH = "/api"
```

### 安装依赖
pnpm install

### 启动
pnpm dev
