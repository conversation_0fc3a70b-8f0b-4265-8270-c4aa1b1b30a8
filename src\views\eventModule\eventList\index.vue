<!-- eslint-disable vue/no-unused-vars -->
<template>
  <div class="event-module-container bg-white dark:bg-[#141414]">
    <el-card>
      <!-- 顶部搜索和操作区 -->
      <div class="top-actions">
        <Perms :value="['event:r']">
          <SearchComponent @search="handleSearch" />
        </Perms>
        <Perms :value="['event:c']">
          <div class="operation-buttons">
            <el-button type="primary" @click="handleCreate">创建事件</el-button>
          </div>
        </Perms>
      </div>

      <!-- 表格区域 -->
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="事件名称" min-width="100" prop="event_name" />
        <el-table-column label="状态" prop="event_process_status" width="130">
          <template #default="scope">
            <el-tag
              :type="
                scope.row.event_process_status === '待处理'
                  ? 'danger'
                  : scope.row.event_process_status === '处理中'
                    ? 'primary'
                    : 'success'
              "
              effect="light"
            >
              {{ scope.row.event_process_status }}
              <el-icon v-if="scope.row.isWarning">
                <warning />
              </el-icon>
            </el-tag>
            <div class="status-detail">{{ scope.row.statusDetail }}</div>
          </template>
        </el-table-column>
        <el-table-column label="责任人" prop="owner.user" show-overflow-tooltip>
          <template #default="scope">
            <span
              v-if="
                Array.isArray(scope.row.owner?.user) &&
                scope.row.owner.user.length
              "
            >
              <span v-for="(userObj, idx) in scope.row.owner.user" :key="idx">
                {{ (Object.values(userObj)[0] as any).display_name }}
                <span v-if="idx < scope.row.owner.user.length - 1">, </span>
              </span>
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="事件来源" prop="event_source" width="150">
          <template #default="scope">
            <el-tooltip :hide-after="0" placement="right" raw-content>
              <template #content>
                <div v-html="getEventSourceTooltip(scope.row.event_source)" />
              </template>
              <span class="event-source-text">
                {{ getEventSourceDisplay(scope.row.event_source) }}
              </span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          label="事件概要"
          prop="event_summary"
          show-overflow-tooltip
        />
        <el-table-column label="创建时间/更新时间" prop="ctime" width="200" />
        <el-table-column fixed="right" label="操作" width="200">
          <template #default="scope">
            <div class="operation-icons">
              <el-tooltip :hide-after="0" content="进入作战室">
                <IconifyIconOffline
                  color="#2c7fe4"
                  height="20px"
                  icon="gridicons:share-computer"
                  width="20px"
                  @click="handleEnterWarRoom(scope.row)"
                />
              </el-tooltip>
              <Perms :value="['event:u']">
                <el-tooltip
                  v-if="
                    scope.row.event_process_status === '待处理' ||
                    scope.row.event_process_status === '处理中'
                  "
                  :hide-after="0"
                  content="将状态设置为 '已处理'"
                >
                  <IconifyIconOffline
                    color="#2c7fe4"
                    height="20px"
                    icon="lets-icons:done-round-fill"
                    width="20px"
                    @click="statusEdit(scope.row)"
                  />
                </el-tooltip>
                <el-tooltip
                  v-else-if="scope.row.event_process_status === '已处理'"
                  :hide-after="0"
                  content="已处理"
                >
                  <IconifyIconOffline
                    color="grey"
                    height="20px"
                    icon="lets-icons:done-ring-round"
                    width="20px"
                  />
                </el-tooltip>
              </Perms>
              <el-tooltip :hide-after="0" content="修改">
                <IconifyIconOffline
                  color="#2c7fe4"
                  height="20px"
                  icon="mingcute:edit-line"
                  width="20px"
                  @click="eventEdit(scope.row)"
                />
              </el-tooltip>
              <Perms :value="['event:u']">
                <el-tooltip :hide-after="0" content="详情">
                  <IconifyIconOffline
                    color="#2c7fe4"
                    height="20px"
                    icon="gg:details-more"
                    width="20px"
                    @click="handleMore(scope.row)"
                  />
                </el-tooltip>
              </Perms>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <div class="pagination-info" />
        <el-pagination
          v-model:current-page="page"
          v-model:page-size="size"
          :page-sizes="[15, 50, 100]"
          :total="total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      <event-add
        ref="eventAddRef"
        :edit-data="editData"
        :is-edit="isEdit"
        @refresh="handleSearch(currentSearchParams)"
      />
      <eventDetails ref="eventDetailsRef" />
      <!-- <el-dialog
        v-model="statusDialogVisible"
        title="修改事件状态"
        width="350px"
      >
        <el-radio-group
          v-model="statusForm.event_process_status"
          style="display: flex; justify-content: center"
        >
          <el-radio label="待处理">
            <el-tag type="danger" effect="light" disable-transitions
              >待处理</el-tag
            >
          </el-radio>
          <el-radio label="处理中">
            <el-tag type="primary" effect="light" disable-transitions
              >处理中</el-tag
            >
          </el-radio>
          <el-radio label="已处理">
            <el-tag type="success" effect="light" disable-transitions
              >已处理</el-tag
            >
          </el-radio>
        </el-radio-group>
        <template #footer>
          <el-button @click="statusDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitStatus">确定</el-button>
        </template>
      </el-dialog> -->
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { Warning } from "@element-plus/icons-vue";
import { EventDetails, EventList, eventUpdate } from "@/api/event";
import SearchComponent from "./components/SearchModule.vue";
import eventAdd from "./components/eventadd.vue";
import eventDetails from "./components/eventDetails.vue";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";
import { useChatStore } from "@/store/warChat";
import { joinRoom } from "@/api/warroom";
import IconifyIconOffline from "@/components/ReIcon/src/iconifyIconOffline";

const router = useRouter();
const chatStore = useChatStore();
// 分页相关
const page = ref(1);
const size = ref(15);
const total = ref(0);

// 存储当前搜索条件
const currentSearchParams = ref({});

// 加载数据
onMounted(() => {
  handleSearch({
    event_process_status: ["待处理", "处理中"],
    page: page.value,
    size: size.value
  });
});

// 表格数据
const tableData = ref([]);

// 引入事件添加组件
const eventAddRef = ref();

// 修改创建事件方法
const handleCreate = () => {
  isEdit.value = false;
  editData.value = {};
  eventAddRef.value?.open();
};
// 进入作战室
const handleEnterWarRoom = async (row: any) => {
  // 1. 获取事件详情，拿到房间id
  const res = (await EventDetails({ id: row.id })) as any;
  if (res.code === 0) {
    const roomId = res.data.room;
    // 2. 判断是否已在房间
    if (chatStore.isUserInRoom(roomId)) {
      // 已在房间，直接跳转
      router.push(`/warroom/index/${roomId}`);
    } else {
      // 未加入，先加入
      const joinRes = (await joinRoom({ id: roomId })) as any;
      if (joinRes.code === 0) {
        await chatStore.fetchRooms(); // 刷新房间列表
        ElMessage.success("成功加入作战室");
        router.push(`/warroom/index/${roomId}`);
      } else {
        ElMessage.error(joinRes.message || "加入作战室失败");
      }
    }
  } else {
    ElMessage.error("进入作战室失败");
  }
};
//编辑
const eventEdit = (row: any) => {
  isEdit.value = true;
  editData.value = row;
  eventAddRef.value?.open(row);
  console.log(row);
};
// 修改状态
const statusEdit = (row: any) => {
  statusForm.value.id = row.id;
  statusForm.value.event_process_status = "已处理";
  eventUpdate({
    id: statusForm.value.id,
    event_process_status: statusForm.value.event_process_status
  }).then((res: any) => {
    if (res.code === 0) {
      ElMessage.success("状态修改成功");
      handleSearch(currentSearchParams.value);
    } else {
      ElMessage.error("状态修改失败");
    }
  });
};

// 修改搜索方法
const handleSearch = (searchResult: any) => {
  // 保存当前搜索参数
  currentSearchParams.value = { ...searchResult };

  EventList(searchResult).then((res: any) => {
    if (res.code === 0) {
      tableData.value = res.data.events;
      total.value = res.data.total;
      page.value = res.data.page;
      size.value = res.data.size;
    } else {
      ElMessage.error("搜索失败");
    }
  });
};

// 详情弹窗ref
const eventDetailsRef = ref();

// 更多操作
const handleMore = row => {
  eventDetailsRef.value?.open(row.id);
};

// 每页条数
const handleSizeChange = val => {
  size.value = val;
  // 使用当前搜索参数加上分页信息重新搜索
  handleSearch({
    ...currentSearchParams.value,
    page: page.value,
    size: size.value
  });
};

// 分页
const handleCurrentChange = val => {
  page.value = val;
  // 使用当前搜索参数加上分页信息重新搜索
  handleSearch({
    ...currentSearchParams.value,
    page: page.value,
    size: size.value
  });
};

// 选中的行数据
const selectedRows = ref([]);

// 处理多选变化
const handleSelectionChange = val => {
  selectedRows.value = val;
  console.log("选中的行:", val);
};

const isEdit = ref(false);
const editData = ref({});

const statusForm = ref({
  id: "",
  event_process_status: ""
});

// 获取事件来源显示文本
const getEventSourceDisplay = (eventSource: any) => {
  if (!eventSource) return "-";
  if (typeof eventSource === "string") {
    return eventSource;
  }
  if (typeof eventSource === "object" && eventSource.describe) {
    return eventSource.describe;
  }
  return "-";
};

// 获取事件来源提示信息
const getEventSourceTooltip = (eventSource: any) => {
  if (!eventSource) return "无事件来源信息";
  if (typeof eventSource === "string") {
    return `事件来源: ${eventSource}`;
  }
  if (typeof eventSource === "object") {
    const details = [];
    if (eventSource.name) details.push(`配置名: ${eventSource.name}`);
    if (eventSource.describe) details.push(`描述: ${eventSource.describe}`);
    // if (eventSource.transforms_type)
    //   details.push(`解析类型: ${eventSource.transforms_type}`);
    if (eventSource.enabled !== undefined)
      details.push(`状态: ${eventSource.enabled ? "启用" : "禁用"}`);
    if (eventSource.ctime) details.push(`创建时间: ${eventSource.ctime}`);
    if (eventSource.creator_id?.display_name)
      details.push(`创建人: ${eventSource.creator_id.display_name}`);
    return details.length > 0 ? details.join("<br>") : "无详细信息";
  }
  return "无事件来源信息";
};
</script>

<style lang="scss" scoped>
.event-module-container {
  .top-actions {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    .search-area {
      display: flex;
      gap: 10px;

      .el-input {
        width: 300px;
      }
    }

    .operation-buttons {
      display: flex;
      gap: 10px;
    }
  }

  .el-table {
    margin-bottom: 16px;

    .status-detail {
      margin-top: 5px;
      font-size: 12px;
      color: #f56c6c;
    }

    .operation-icons {
      display: flex;
      gap: 18px;

      .el-icon {
        font-size: 18px;
        color: #2c7fe4;
        cursor: pointer;

        &:hover {
          color: #66b1ff;
        }
      }
    }
  }

  .pagination-container {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .pagination-info {
      color: #606266;
    }
  }

  .event-source-text {
    cursor: pointer;
    color: #409eff;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}
</style>
