<script lang="ts" setup>
import { emitter } from "@/utils/mitt";
import { useNav } from "@/layout/hooks/useNav";
import LaySearch from "../lay-search/index.vue";
import LayNotice from "../lay-notice/index.vue";
import { responsiveStorageNameSpace } from "@/config";
import { computed, nextTick, onMounted, onUnmounted, ref } from "vue";
import { isAllEmpty, storageLocal } from "@pureadmin/utils";
import { usePermissionStoreHook } from "@/store/modules/permission";
import LaySidebarItem from "../lay-sidebar/components/SidebarItem.vue";
import LaySidebarFullScreen from "../lay-sidebar/components/SidebarFullScreen.vue";
import AccountSettingsIcon from "@iconify-icons/ri/account-box-line";
import LogoutCircleRLine from "@iconify-icons/ri/logout-circle-r-line";
import { useRouter } from "vue-router";
import usePlaybookStore from "@/store/modules/playbook";
import { getSceneName } from "@/api/scene";
import { getPicture } from "@/api/system";

const router = useRouter();
const playbookStore = usePlaybookStore();
const menuRef = ref();
const showLogo = ref(
  storageLocal().getItem<StorageConfigs>(
    `${responsiveStorageNameSpace()}configure`
  )?.showLogo ?? true
);
const logo = ref<string>("");

const {
  route,
  title,
  logout,
  onPanel,
  getLogo,
  username,
  userAvatar,
  backTopMenu,
  avatarsStyle,
  toAccountSettings
} = useNav();

const defaultActive = computed(() =>
  !isAllEmpty(route.meta?.activePath) ? route.meta.activePath : route.path
);

nextTick(() => {
  menuRef.value?.handleResize();
});

onMounted(async () => {
  emitter.on("logoChange", key => {
    showLogo.value = key;
  });
  logo.value = await getPicture("logo");
  //挂载时，获取一次当前场景的name
  await sceneres();
});

onUnmounted(() => {
  if (logo.value) URL.revokeObjectURL(logo.value);
});

//获取当前场景的name
const sceneres = async () => {
  const res = (await getSceneName({})) as any;
  if (res.code === 0) {
    console.log(res.data.scene);
    playbookStore.selectedScene = res.data.scene || "";
  } else {
    playbookStore.selectedScene = "";
  }
};

// 打开场景
const addScene = () => {
  router.push({
    name: "playbookSceneView"
  });
};
</script>

<template>
  <div
    v-loading="usePermissionStoreHook().wholeMenus.length === 0"
    class="horizontal-header"
  >
    <div v-if="showLogo" class="horizontal-header-left" @click="backTopMenu">
      <!--      <img :src="getLogo()" alt="logo" />-->
      <img v-if="logo" :src="logo" alt="加载失败" />
      <img v-else alt="加载失败" src="/logo.svg" />
      <span>{{ title }}</span>
    </div>
    <el-menu
      :key="defaultActive"
      ref="menuRef"
      :default-active="defaultActive"
      class="horizontal-header-menu"
      mode="horizontal"
      popper-class="pure-scrollbar"
    >
      <LaySidebarItem
        v-for="route in usePermissionStoreHook().wholeMenus"
        :key="route.path"
        :base-path="route.path"
        :item="route"
      />
    </el-menu>
    <div class="horizontal-header-right">
      <el-button
        link
        size="large"
        style="
          color: whitesmoke;
          border-color: darkgrey;
          font-size: 13px;
          margin-right: 15px;
          padding: 5px 10px;
        "
        @click="addScene()"
        >当前场景:{{ (playbookStore.selectedScene || "").slice(0, 10) }}
      </el-button>
      <!-- 菜单搜索 -->
      <LaySearch id="header-search" />
      <!-- 全屏 -->
      <LaySidebarFullScreen id="full-screen" />
      <!-- 消息通知 -->
      <LayNotice id="header-notice" />
      <!-- 退出登录 -->
      <el-dropdown trigger="click">
        <span class="el-dropdown-link navbar-bg-hover">
          <!--          <img :src="userAvatar" :style="avatarsStyle" />-->
          <p v-if="username" class="dark:text-white">{{ username }}</p>
        </span>
        <template #dropdown>
          <el-dropdown-menu class="logout">
            <el-dropdown-item @click="toAccountSettings">
              <IconifyIconOffline
                :icon="AccountSettingsIcon"
                style="margin: 5px"
              />
              个人中心
            </el-dropdown-item>
            <el-dropdown-item @click="logout">
              <IconifyIconOffline
                :icon="LogoutCircleRLine"
                style="margin: 5px"
              />
              退出系统
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <!-- <span
        class="set-icon navbar-bg-hover"
        title="打开系统配置"
        @click="onPanel"
      >
        <IconifyIconOffline :icon="Setting" />
      </span> -->
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-loading-mask) {
  opacity: 0.45;
}

.logout {
  width: 120px;

  ::v-deep(.el-dropdown-menu__item) {
    display: inline-flex;
    flex-wrap: wrap;
    min-width: 100%;
  }
}
</style>
