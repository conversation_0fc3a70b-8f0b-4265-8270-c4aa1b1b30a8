<!-- eslint-disable vue/no-unused-vars -->
<template>
  <div class="audit-container">
    <el-card>
      <!-- 搜索区域 -->
      <Perms :value="['audit:r']">
        <div class="search-area dark:bg-[#141414]">
          <div class="search-item">
            <span class="dark:text-white">用户：</span>
            <el-input
              v-model="searchParams.username"
              clearable

            />
          </div>
          <div class="search-item">
            <span class="dark:text-white">IP：</span>
            <el-input
              v-model="searchParams.ip"
              clearable

            />
          </div>
          <div class="search-item date-range">
            <span class="dark:text-white">时间：</span>
            <el-date-picker
              v-model="dateRange"
              end-placeholder="结束时间"
              range-separator="至"
              start-placeholder="开始时间"
              type="datetimerange"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </div>
          <!--          <div class="search-item">-->
          <!--            <span class="dark:text-white">结果：</span>-->
          <!--            <el-select v-model="searchParams.result" placeholder="请选择结果">-->
          <!--              <el-option label="成功" value="成功" />-->
          <!--              <el-option label="失败" value="失败" />-->
          <!--            </el-select>-->
          <!--          </div>-->
          <div class="search-item">
            <el-button type="primary" @click="handleSearch">查询</el-button>
          </div>
          <div class="search-buttons">
            <!--            <el-button type="primary" @click="handleSearch">外发配置</el-button>-->
          </div>
        </div>
      </Perms>
      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        class="dark:bg-[#141414]"
        style="width: 100%; margin: 20px 0"
      >
        <el-table-column
          :index="
            index => (searchParams.page - 1) * searchParams.size + index + 1
          "
          label="序号"
          type="index"
          width="70"
        />
        <el-table-column
          label="用户名"
          prop="username"
          show-overflow-tooltip
          width="200"
        >
          <template #default="scope">
            {{ scope.row.display_name || "API账号" }} ({{ scope.row.username }})
          </template>
        </el-table-column>
        <el-table-column label="动作" width="80">
          <template #default="scope"> 访问</template>
        </el-table-column>
        <el-table-column label="URI" prop="uri" width="260" />
        <el-table-column
          label="请求参数"
          prop="request_data"
          show-overflow-tooltip
          width="300"
        >
          <template #default="scope">
            {{ JSON.stringify(scope.row.request_data) }}
          </template>
        </el-table-column>
        <el-table-column label="结果" prop="result" width="100">
          <template #default="scope">
            <el-tag
              :type="scope.row.result === '成功' ? 'success' : 'danger'"
              size="small"
            >
              {{ scope.row.result }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="IP" prop="client_ip" width="160" />
        <el-table-column label="UA" prop="client_ua" show-overflow-tooltip />
        <el-table-column label="时间" prop="ctime" width="200">
          <template #default="scope">
            {{ formatTime(scope.row.ctime) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="searchParams.page"
          :page-size="searchParams.size"
          :page-sizes="[15, 50, 100]"
          :total="total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { systemLog } from "@/api/system";

// 搜索参数
const searchParams = reactive({
  username: "",
  start_time: "",
  end_time: "",
  ip: "",
  page: 1,
  size: 15
});

const tableData = ref([]);
const total = ref(0);
const loading = ref(false);
const dateRange = ref("");

// 获取审计日志列表
const getAuditList = async () => {
  loading.value = true;
  try {
    const res = await systemLog(searchParams);
    if (res.code === 0) {
      console.log("请求成功", res.data);
      tableData.value = res.data.data;
      total.value = res.data.total;
    } else {
      ElMessage.error(res.message || "获取审计日志失败");
    }
  } catch (error) {
    console.error("获取审计日志失败", error);
    ElMessage.error("获取审计日志失败");
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  console.log(dateRange.value);
  console.log(searchParams);
  searchParams.page = 1;
  getAuditList();
};

// 重置
const handleReset = () => {
  dateRange.value = "";
  Object.assign(searchParams, {
    start_time: "",
    end_time: "",
    username: "",
    ip: "",
    page: 1,
    size: 15
  });
  getAuditList();
};

// 改变每页显示数量
const handleSizeChange = val => {
  searchParams.size = val;
  getAuditList();
};

// 改变页码
const handleCurrentChange = val => {
  searchParams.page = val;
  getAuditList();
};

// 时间格式化函数
const formatTime = timestamp => {
  if (!timestamp) return "";
  const date = new Date(timestamp);
  return date
    .toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false
    })
    .replace(/\//g, "-");
};

// 监听 dateRange，自动赋值给 searchParams
watch(
  dateRange,
  val => {
    if (Array.isArray(val) && val.length === 2) {
      searchParams.start_time = val[0];
      searchParams.end_time = val[1];
    } else {
      searchParams.start_time = "";
      searchParams.end_time = "";
    }
  },
  { immediate: true }
);

// 页面加载时获取数据
onMounted(() => {
  getAuditList();
});
</script>

<style lang="scss" scoped>
.audit-container {
  padding: 0;

  .search-area {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    margin-bottom: 20px;
    border-radius: 4px;

    .search-item {
      display: flex;
      align-items: center;
      margin-right: 15px;
      white-space: nowrap;

      span {
        margin-right: 5px;
        white-space: nowrap;
      }

      .el-input,
      .el-select {
        width: 150px;
      }
    }

    .search-buttons {
      margin-left: auto;
      white-space: nowrap;
    }
  }

  .pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>
