import { getApiUserList } from "@/api/userList";
import { ref } from "vue";

// 定义接口类型
interface ApiUserData {
  id: string;
  ctime: string;
  utime: string;
  username: string;
  description: string;
  token: string;
  status: boolean;
  creator: string;
  expiration: string;
  roles: string[];
}

// 分页相关的响应式数据
export const Page = ref(1);
export const size = ref(15);
export const totals = ref(0);
export const input1 = ref("");

// API账号数据
export const ApiUser = ref<ApiUserData[]>([]);

// 获取API账号列表
export const fetchApiUserList = async () => {
  try {
    const res = (await getApiUserList({
      page: Page.value,
      size: size.value,
      keyword: input1.value
    })) as any;
    if (res.code === 0) {
      ApiUser.value = res.data.data;
      totals.value = res.data.total;
    }
  } catch (error) {
    console.error("获取API账号列表失败:", error);
  }
};

// 处理搜索
export const handleSearch = () => {
  Page.value = 1;
  fetchApiUserList();
};

// 处理清除搜索
export const handleClearSearch = () => {
  input1.value = "";
  handleSearch();
};

// 处理页码改变
export const handleCurrentChange = (val: number) => {
  Page.value = val;
  fetchApiUserList();
};

// 处理每页条数改变
export const handleSizeChange = (val: number) => {
  size.value = val;
  Page.value = 1;
  fetchApiUserList();
};
