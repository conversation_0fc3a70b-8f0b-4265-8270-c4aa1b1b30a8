<script lang="ts" setup>
import { ref } from "vue";
import { UploadFilled } from "@element-plus/icons-vue";
import { uploadTool } from "@/api/toolManagement";
import { ElMessage } from "element-plus";
import { message } from "@/utils/message";
import { IconifyIconOffline } from "@/components/ReIcon";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import CheckLine from "@iconify-icons/ri/check-line";

// 定义emit和状态变量
const emit = defineEmits(["refresh"]);
const uploadVisible = ref(false);
const uploadRef = ref(); // 上传组件引用
const fileList = ref([]); // 当前待上传的文件列表
const uploadProgress = ref(0); // 上传进度
const uploadHistory = ref([]); // 所有上传成功的历史记录
const isUploading = ref(false); // 控制进度条和按钮禁用状态
let chunkBuffer = "";

// 上传前校验 - 只允许ZIP文件
const beforeUpload = file => {
  const isZip =
    file.type === "application/x-zip-compressed" ||
    file.type === "application/zip";
  if (!isZip) {
    message("只能上传 ZIP 文件！", { type: "warning" });
    return false;
  }
  return true;
};

// 处理文件变化和移除
const handleChange = (_, uploadFiles) => (fileList.value = uploadFiles);
const handleRemove = (_, uploadFiles) => (fileList.value = uploadFiles);

// 打开上传对话框
const handleUploadTool = () => (uploadVisible.value = true);

// 开始上传文件
const startUpload = async () => {
  if (!fileList.value.length) {
    message("请先选择要上传的文件", { type: "warning" });
    return;
  }

  const formData = new FormData();
  fileList.value.forEach(file => formData.append("file", file.raw));

  isUploading.value = true;
  uploadProgress.value = 0;

  try {
    await uploadTool(formData, chunk => {
      chunkBuffer += chunk;

      const progressMatches = chunk.matchAll(/data: 解压进度：(\d+)%/g);
      const progressValues = Array.from(progressMatches).map(match =>
        parseInt(match[1])
      );
      if (progressValues.length > 0) {
        uploadProgress.value = progressValues[progressValues.length - 1];
      }

      const jsonMatch = chunkBuffer.match(/data: (\[.*\])/s);
      if (jsonMatch?.[1]) {
        try {
          const toolsInfo = JSON.parse(jsonMatch[1].replace(/'/g, '"'));
          // 将新成功的记录追加到历史记录中
          uploadHistory.value.push(...toolsInfo);
          ElMessage.success("工具上传成功");
          emit("refresh");
          chunkBuffer = ""; // 解析后清空
        } catch (error) {
          console.error("解析工具信息失败:", error);
        }
      }
    });
  } catch (error) {
    console.error("上传失败:", error);
    ElMessage.error("上传失败: " + error.message);
  } finally {
    // 单次上传结束后，重置状态以便再次上传
    isUploading.value = false;
    uploadProgress.value = 0;
    fileList.value = [];
    uploadRef.value?.clearFiles();
  }
};

// 关闭对话框时重置所有状态
const handleClose = () => {
  uploadProgress.value = 0;
  uploadVisible.value = false;
  uploadHistory.value = [];
  isUploading.value = false;
  uploadRef.value?.clearFiles();
  fileList.value = [];
  chunkBuffer = "";
};
</script>

<template>
  <div class="tool-upload-container">
    <!-- 上传按钮 -->
    <Perms :value="['tool:c']">
      <el-tooltip
        :hide-after="0"
        content="上传工具"
        effect="light"
        placement="bottom"
      >
        <el-button
          circle
          class="upload-button"
          size="default"
          type="primary"
          @click="handleUploadTool"
        >
          <el-icon>
            <UploadFilled />
          </el-icon>
        </el-button>
      </el-tooltip>
    </Perms>
    <!-- 上传对话框 -->
    <el-dialog
      v-model="uploadVisible"
      title="上传工具"
      width="500px"
      @close="handleClose"
    >
      <!-- 上传区域 -->
      <el-upload
        ref="uploadRef"
        :auto-upload="false"
        :before-upload="beforeUpload"
        :on-change="handleChange"
        :on-remove="handleRemove"
        accept=".zip"
        action="#"
        class="upload-area"
        drag
        multiple
      >
        <el-icon class="el-icon--upload">
          <upload-filled />
        </el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip">只能上传 ZIP 格式文件</div>
        </template>
      </el-upload>

      <!-- 上传进度 -->
      <el-progress
        v-if="isUploading"
        :percentage="uploadProgress"
        class="upload-progress"
      />

      <!-- 上传结果列表 -->
      <div v-if="uploadHistory.length" class="upload-result-list">
        <div
          v-for="(file, index) in uploadHistory"
          :key="index"
          class="upload-result-item"
        >
          <div class="upload-result-status">
            <IconifyIconOffline
              :icon="useRenderIcon(CheckLine)"
              class="success-icon"
            />
            <span class="tool-name">{{ file.tool }}</span>
          </div>
          <span class="upload-msg">{{ file.msg }}</span>
        </div>
      </div>

      <template #footer>
        <!-- 按钮区域 -->
        <span class="dialog-footer">
          <el-button @click="uploadVisible = false">关闭</el-button>
          <el-button
            type="primary"
            :disabled="isUploading || fileList.length === 0"
            @click="startUpload"
          >
            {{ isUploading ? "上传中..." : "开始上传" }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.tool-upload-container {
  display: inline-block;
}

.upload-button {
  display: inline-flex;
  align-items: center;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.upload-area {
  :deep(.el-upload) {
    width: 100%;
  }

  :deep(.el-upload-dragger) {
    width: 100%;
    height: 180px; /* 减少高度为结果列表留出空间 */
  }

  :deep(.el-upload-list) {
    max-height: 120px;
    overflow-y: auto;
  }
}

.dialog-footer {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.upload-result-list {
  margin: 16px 0;
  max-height: 150px; /* 调整最大高度 */
  overflow-y: auto;
  border-top: 1px solid #eee;
  padding-top: 10px;
}

.upload-result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
  background: var(--el-fill-color-light);
  border-radius: 4px;
  transition: all 0.3s;

  &:hover {
    background: var(--el-fill-color);
  }
}

.upload-result-status {
  display: flex;
  align-items: center;
  gap: 8px;

  .success-icon {
    color: var(--el-color-success);
    font-size: 16px;
  }

  .tool-name {
    font-weight: 500;
  }
}

.upload-msg {
  color: var(--el-text-color-secondary);
  font-size: 13px;
}

.upload-progress {
  margin: 16px 0 10px;
}
</style>
