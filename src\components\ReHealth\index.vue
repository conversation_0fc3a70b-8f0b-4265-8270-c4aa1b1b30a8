<script lang="ts" setup>
import { computed, ref } from "vue";
import cronstrue from "cronstrue/i18n";
import cronParser from "cron-parser";
import { ToolHealthConfig, updateToolHealthCheck } from "@/api/toolManagement";
import { ElMessage } from "element-plus";

interface HealthConfigResponse {
  code: number;
  data: {
    switch: "on" | "off";
    cron: string;
  };
}

const props = defineProps<{
  modelValue?: string;
  title?: string;
}>();

const emit = defineEmits<{
  (e: "update:modelValue", value: string): void;
}>();

const dialogVisible = ref(false);
const switchStatus = ref("off");

// Cron表达式字段 [分, 时, 日, 月, 周]
const cronFields = ref(["*", "*", "*", "*", "*"]);

// 更新cron字段
const updateCronField = (value: string, index: number) => {
  cronFields.value[index] = value;
};

// 计算下次执行时间描述
const nextExecutionTime = computed(() => {
  try {
    const cronExpression = cronFields.value.join(" ");
    if (cronExpression && switchStatus.value === "on") {
      return cronstrue.toString(cronExpression, { locale: "zh_CN" });
    }
  } catch (e) {
    console.log("cron表达式处理失败：", e);
    return "";
  }
  return "";
});

// 计算下次具体执行时间
const nextCronTime = computed(() => {
  if (switchStatus.value === "on") {
    const cronExpr = cronFields.value.join(" ");
    try {
      const interval = cronParser.parseExpression(cronExpr);
      const next = interval.next().toDate();
      return `${next.getFullYear()}年${String(next.getMonth() + 1).padStart(2, "0")}月${String(next.getDate()).padStart(2, "0")}日 ${String(next.getHours()).padStart(2, "0")}:${String(next.getMinutes()).padStart(2, "0")}`;
    } catch (e) {
      return "表达式不支持";
    }
  }
  return "";
});

const handleConfirm = async () => {
  try {
    if (switchStatus.value === "off") {
      const config = {
        switch: "off",
        cron: ""
      };
      await updateToolHealthCheck(config);
      emit("update:modelValue", "");
    } else {
      // 开关开启时，保存cron表达式
      const cronExpression = cronFields.value.join(" ");
      const config = {
        switch: "on",
        cron: cronExpression
      };
      await updateToolHealthCheck(config);
      emit("update:modelValue", cronExpression);
    }
    ElMessage.success("健康检查配置保存成功");
    dialogVisible.value = false;
  } catch (error) {
    console.error("保存健康检查配置失败：", error);
    ElMessage.error("保存健康检查配置失败");
  }
};

const handleOpen = async () => {
  try {
    const response = (await ToolHealthConfig({})) as HealthConfigResponse;
    if (response.code === 0 && response.data) {
      // 设置开关状态
      switchStatus.value = response.data.switch === "on" ? "on" : "off";
      // 如果有 cron 表达式，解析它
      if (response.data.cron) {
        const cronParts = response.data.cron.split(" ");
        if (cronParts.length >= 5) {
          cronFields.value = cronParts.slice(0, 5);
        }
      } else {
        // 如果没有cron表达式，重置为默认值
        cronFields.value = ["*", "*", "*", "*", "*"];
      }
    }
  } catch (error) {
    console.error("获取健康检查配置失败：", error);
    ElMessage.error("获取健康检查配置失败");
  }
  dialogVisible.value = true;
};
</script>

<template>
  <div class="re-health">
    <el-button type="primary" @click="handleOpen">健康检查配置</el-button>

    <el-dialog
      v-model="dialogVisible"
      :title="props.title || '修改工具健康检查配置信息 (需要工具更新权限)'"
      destroy-on-close
      width="600px"
    >
      <div class="health-form">
        <!-- 开关控制 -->
        <div class="form-item">
          <div class="enable-switch">
            <el-checkbox
              v-model="switchStatus"
              :false-value="'off'"
              :true-value="'on'"
            >
              开启健康定时检查
            </el-checkbox>
          </div>
        </div>

        <!-- Cron表达式设置 -->
        <div v-if="switchStatus === 'on'" class="form-item">
          <div class="form-label">
            <span class="required">*</span>
            Cron表达式设置
          </div>
          <div class="cron-input-container">
            <div class="cron-header">
              <span class="cron-label">分</span>
              <span class="cron-label">时</span>
              <span class="cron-label">日</span>
              <span class="cron-label">月</span>
              <span class="cron-label">周</span>
            </div>
            <div class="cron-visual-editor">
              <div
                v-for="(field, index) in cronFields"
                :key="index"
                class="cron-field"
              >
                <input
                  :value="field"
                  class="cron-field-input"
                  @input="
                    e =>
                      updateCronField(
                        (e.target as HTMLInputElement).value,
                        index
                      )
                  "
                />
              </div>
            </div>
            <div class="next-execution">
              cron格式: 分 时 日 月 周, 如: 0 48 14 * *
              表示每月一的14点48分执行一次
            </div>
            <div class="next-execution">{{ nextExecutionTime }}</div>
            <div class="next-execution">执行时间：{{ nextCronTime }}</div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirm">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.re-health {
  display: inline-block;
}

.health-form {
  padding: 0;

  .form-item {
    margin-bottom: 24px;
  }

  .form-label {
    font-size: 14px;
    margin-bottom: 10px;
    margin-top: 8px;
    display: flex;
    align-items: center;
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  .required {
    color: var(--el-color-danger);
    margin-right: 6px;
    font-weight: bold;
  }

  .cron-input-container {
    margin-top: 16px;
    padding: 16px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  .cron-header {
    display: flex;
    margin-bottom: 8px;
  }

  .cron-label {
    flex: 1;
    text-align: center;
  }

  .cron-visual-editor {
    display: flex;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
  }

  .cron-field {
    flex: 1;
    position: relative;
    border-right: 1px solid #dcdfe6;
  }

  .cron-field:last-child {
    border-right: none;
  }

  .cron-field-input {
    width: 100%;
    height: 36px;
    border: none;
    outline: none;
    text-align: center;
    background-color: #fff;
    color: #606266;
    font-size: 14px;
    padding: 0 5px;
  }

  .next-execution {
    text-align: center;
    margin-top: 12px;
    padding: 8px;
    border-radius: 4px;
    color: #606266;
    font-size: 14px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
