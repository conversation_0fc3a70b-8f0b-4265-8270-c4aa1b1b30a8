<!-- eslint-disable vue/no-mutating-props -->
<template>
  <!-- 抽屉组件 -->
  <el-drawer
    :before-close="handleClose"
    :model-value="visible"
    :size="800"
    :title="drawerTitle"
    @update:model-value="handleVisibleChange"
  >
    <div class="drawer-content">
      <!-- 表单内容 -->
      <div class="form-container">
        <div class="section-title">基础信息</div>
        <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
          <!-- 配置名称 -->
          <el-form-item label="配置名称" prop="event_name" required>
            <el-input
              v-model="form.event_name"

            />
          </el-form-item>
          <!-- 描述 -->
          <el-form-item label="描述" prop="event_describe" required>
            <el-input
              v-model="form.event_describe"

              type="textarea"
            />
          </el-form-item>
          <!-- 接入方式 -->
          <el-form-item label="接入方式" prop="event_source_type" required>
            <el-select
              v-model="form.event_source_type"
              placeholder="请选择事件来源类型"
              @change="handleEventSourceTypeChange"
            >
              <el-option label="syslog" value="socket" />
              <el-option label="kafka" value="kafka" />
            </el-select>
          </el-form-item>
          <!-- 协议 -->
          <el-form-item
            v-if="form.event_source_type === 'socket'"
            label="协议"
            prop="communication_type"
          >
            <el-select
              v-model="form.communication_type"
              placeholder="请选择协议"
            >
              <el-option label="tcp" value="tcp" />
              <el-option label="udp" value="udp" />
            </el-select>
          </el-form-item>
          <!-- syslog端口 -->
          <el-form-item
            v-if="form.event_source_type === 'socket'"
            label="syslog端口"
            prop="syslog_port"
            required
          >
            <el-input
              v-model="form.syslog_port"
              max="600"
              min="500"

              type="number"
            />
          </el-form-item>
          <!-- 事件来源地址 -->
          <el-form-item
            v-if="form.event_source_type === 'kafka'"
            label="事件来源地址"
            prop="event_source_address"
          >
            <el-input
              v-model="form.event_source_address"
              placeholder="多个事件来源地址之间用逗号分隔"
              type="textarea"
            />
            <!-- 服务器配置说明 -->
            <div class="tip-text">
              <el-icon>
                <InfoFilled />
              </el-icon>
              多个服务器之间用逗号分隔；如果server端绑定hostname，请使用hostname访问，示例:
              ***********:9092, lion-kafka:9092
            </div>
          </el-form-item>
          <!-- 消费者组ID -->
          <el-form-item
            v-if="form.event_source_type === 'kafka'"
            label="消费者组ID"
            prop="kafka_group_id"
          >
            <el-input
              v-model="form.kafka_group_id"

            />
          </el-form-item>
          <!-- 主题名称 -->
          <el-form-item
            v-if="form.event_source_type === 'kafka'"
            label="主题名称"
            prop="kafka_topic"
          >
            <el-input v-model="form.kafka_topic"  />
          </el-form-item>
          <!-- kafka相关高级配置 -->
          <template v-if="form.event_source_type === 'kafka'">
            <el-form-item label="高级配置">
              <div class="advanced-options">
                <!-- <el-checkbox v-model="enable_ssl">启用SSL</el-checkbox>-->
                <el-checkbox v-model="enable_sasl">启用SASL</el-checkbox>
              </div>
            </el-form-item>
            <!-- SSL配置 -->
            <!-- SASL配置 -->
            <template v-if="enable_sasl">
              <el-form-item label="认证机制" prop="sasl_mechanism">
                <el-select
                  v-model="form.sasl_mechanism"
                  placeholder="请选择认证机制"
                >
                  <el-option label="SCRAM-PLAINTEXT(PLAIN)" value="PLAIN" />
                  <el-option
                    label="SCRAM-PLAINTTEXT(SCRAM-SHA-256)"
                    value="SCRAM-SHA-256"
                  />
                  <el-option
                    label="SCRAM-PLAINTEXT(SCRAM-SHA-512)"
                    value="SCRAM-SHA-512"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="用户名" prop="sasl_username">
                <el-input
                  v-model="form.sasl_username"

                />
              </el-form-item>
              <el-form-item label="密码" prop="sasl_password">
                <el-input
                  v-model="form.sasl_password"

                  show-password
                  type="password"
                />
              </el-form-item>
            </template>
          </template>
          <!-- 启用状态 -->
          <!-- <el-form-item label="启用状态" prop="enabled_status">
            <el-switch v-model="form.enabled_status" />
          </el-form-item> -->
        </el-form>
      </div>
      <!-- 底部按钮区域 -->
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script lang="ts" setup>
import { defineEmits, defineProps, ref } from "vue";
import { InfoFilled } from "@element-plus/icons-vue"; // 引入信息图标
import { eventIngestionAdd } from "@/api/event";
import { ElMessage } from "element-plus";
import { useUserStoreHook } from "@/store/modules/user";
// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false // 控制抽屉显示隐藏
  },
  isEdit: {
    type: Boolean,
    default: false // 是否为编辑模式
  }
});
// 获取本地存储内容
const userStore = useUserStoreHook();
// 定义组件事件
const emit = defineEmits(["update:visible", "refresh"]);

// 组件数据定义
const drawerTitle = ref("事件接入添加"); // 抽屉标题
const formRef = ref(); // 基础信息表单引用

// 表单数据
const form = ref({
  event_name: "", // 配置名称
  event_describe: "", // 描述
  event_source_type: "", // 接入方式
  syslog_port: "", // syslog端口
  event_source_address: "", // 事件来源地址，用逗号分隔的字符串
  communication_type: "", // 协议
  kafka_group_id: "", // 消费者组ID
  kafka_topic: "", // 主题名称
  sasl_mechanism: "", // 认证机制
  sasl_username: "", // 用户名
  sasl_password: "", // 密码
  transforms_config: "", // 数据解析配置 (str) 只有Grok时需要填写
  enabled_status: 1, // 启用状态(int) 启用为 1 ，禁用为 0
  linked_execution: 0, // 联动执行(int) 0 未发布 1 已发布
  created_id: userStore.id // 创建人ID
});

// 用于控制高级配置显示的状态变量
const enable_ssl = ref(false);
const enable_sasl = ref(false);

// 表单验证规则
const rules = {
  event_name: [
    { required: true, message: "请输入配置名称", trigger: "blur" },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (!value) {
          callback();
        } else if (!/^[a-z0-9_]+$/.test(value)) {
          callback(new Error("仅支持小写字母、数字、下划线"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  event_describe: [{ required: true, message: "请输入描述", trigger: "blur" }],
  event_source_type: [
    { required: true, message: "请选择事件来源类型", trigger: "change" }
  ],
  created_id: [{ required: true, message: "请输入创建人ID", trigger: "blur" }],
  syslog_port: [
    { required: true, message: "请输入syslog端口", trigger: "blur" },
    {
      validator: (rule: any, value: number, callback: any) => {
        if (value < 500 || value > 600) {
          callback(new Error("端口范围应在500-600之间"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  communication_type: [
    { required: true, message: "请选择协议", trigger: "change" }
  ]
};

// 处理抽屉显示状态变化
const handleVisibleChange = (val: boolean) => {
  emit("update:visible", val);
};

// 处理关闭抽屉
const handleClose = () => {
  form.value = {
    event_name: "",
    event_describe: "",
    event_source_type: "",
    syslog_port: "",
    event_source_address: "",
    communication_type: "",
    kafka_group_id: "",
    kafka_topic: "",
    sasl_mechanism: "",
    sasl_username: "",
    sasl_password: "",
    transforms_config: "",
    enabled_status: 1,
    linked_execution: 0,
    created_id: ""
  };
  enable_ssl.value = false;
  enable_sasl.value = false;
  emit("update:visible", false);
};

// 事件来源类型改变
const handleEventSourceTypeChange = (val: string) => {
  // 清除所有相关字段
  if (val === "syslog") {
    // 切换到 syslog 时清除 kafka 相关字段
    form.value.event_source_address = "";
    form.value.kafka_group_id = "";
    form.value.kafka_topic = "";
    form.value.sasl_mechanism = "";
    form.value.sasl_username = "";
    form.value.sasl_password = "";
    // 重置高级配置开关
    enable_ssl.value = false;
    enable_sasl.value = false;
  } else if (val === "kafka") {
    // 切换到 kafka 时清除 syslog 相关字段
    form.value.syslog_port = "";
    form.value.communication_type = "";
  }
};

// 处理表单提交
const handleSubmit = async () => {
  if (!formRef.value) return;
  const valid = await formRef.value.validate().catch(() => false);
  if (valid) {
    // 处理事件来源地址为数组
    let submitData = {
      ...form.value,
      // 将逗号分隔的地址字符串转换为数组，并去除空白字符
      event_source_address: form.value.event_source_address
        .split(",")
        .map(addr => addr.trim())
        .filter(addr => addr),
      // 将主题转换为数组，并去除空白字符
      kafka_topic: form.value.kafka_topic
        .split(",")
        .map(topic => topic.trim())
        .filter(topic => topic),
      // 将启用状态转换为数字
      enabled_status: form.value.enabled_status ? 1 : 0,
      // 如果未启用SSL，则清空相关字段
      ...(enable_ssl.value
        ? {}
        : {
            ssl_cert_path: "",
            ssl_key_path: ""
          }),
      // 如果未启用SASL，则清空相关字段
      ...(enable_sasl.value
        ? {}
        : {
            sasl_mechanism: "",
            sasl_username: "",
            sasl_password: ""
          })
    };
    // 清除空参数
    const filteredData = Object.fromEntries(
      Object.entries(submitData).filter(([_, value]) => {
        // 过滤掉空字符串、null、undefined和空数组
        if (value === "" || value === null || value === undefined) return false;
        if (Array.isArray(value) && value.length === 0) return false;
        return true;
      })
    );
    // 打印提交的表单数据
    console.log("提交的表单数据:", filteredData);
    //调用接口
    eventIngestionAdd(filteredData as any).then((res: any) => {
      if (res.code === 0) {
        console.log("添加成功", res);
        ElMessage.success("添加成功");
        emit("refresh");
        handleClose();
      }
    });
  }
};
</script>

<style lang="scss" scoped>
/* 抽屉内容样式 */
.drawer-content {
  /* 标题样式 */
  .section-title {
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }

  /* 提示文本样式 */
  .tip-text {
    display: flex;
    gap: 4px;
    align-items: center;
    margin-top: 4px;
    font-size: 12px;
    color: #909399;
  }

  /* 底部按钮区域样式 */
  .drawer-footer {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding: 20px;
  }
}

/* 表单标签样式 */
:deep(.el-form-item__label) {
  font-weight: normal;
}

/* 复选框样式 */
:deep(.el-checkbox) {
  margin-right: 20px;
}
</style>
