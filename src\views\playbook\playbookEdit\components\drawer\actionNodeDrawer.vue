<template>
  <div>
    <!-- 动作节点drawer -->
    <el-drawer v-model="isShowDrawer" size="50%">
      <template #header>
        <div class="action-drawer-title">节点信息</div>
      </template>
      <template #default>
        <div class="action-drawer-body">
          <!-- 节点通用信息 -->
          <el-form
            ref="nodeDataRef"
            :model="nodeData.properties"
            :rules="NodeDataRules"
            label-width="100px"
          >
            <!--            <el-form-item>{{ nodeData }}</el-form-item>-->
            <el-form-item label="节点ID:">
              <el-button link type="primary" @click="$_copyNodeId(nodeData.id)"
                >{{ nodeData.id }}
              </el-button>
            </el-form-item>
            <el-form-item label="节点标题:" prop="node_name">
              <el-input v-model="nodeData.properties.node_name" />
            </el-form-item>
            <!-- 切换应用 -->
            <el-form-item label="切换应用:" prop="name">
              <!-- 绑定值为对象类型时，value-key必填 -->
              <el-select
                v-model="nodeData.properties.name"
                value-key="id"
                @change="switchTool"
              >
                <template #header>
                  <el-input
                    v-model="toolSearchKeyword"
                    :suffix-icon="Search"
                    placeholder="搜索工具"
                    @input="getToolList({ key: toolSearchKeyword })"
                  />
                </template>
                <el-option
                  v-for="item in allToolList"
                  :key="item.id"
                  :label="item.description"
                  :value="item"
                >
                  <span style="float: left">{{ item.description }}</span>
                  <span
                    style="
                      float: right;
                      color: var(--el-text-color-secondary);
                      font-size: 13px;
                    "
                  >
                    {{ item.name }}
                  </span>
                </el-option>
                <template #footer>
                  <el-pagination
                    v-model:current-page="toolCurrentPage"
                    v-model:page-size="toolPageSize"
                    :page-sizes="[20, 30, 40, 50]"
                    :total="total"
                    layout="total, sizes, prev, pager, next, jumper"
                    size="small"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                  />
                </template>
              </el-select>
            </el-form-item>
            <!-- 选择版本和动作 -->
            <el-form-item label="请选择版本:" prop="version">
              <el-select
                v-model="nodeData.properties.version"
                value-key="id"
                @change="getActionList"
              >
                <el-option
                  v-for="item in versionList"
                  :key="item.id"
                  :label="item.version"
                  :value="item"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              v-if="displayActionList"
              label="请选择动作:"
              prop="action_name"
            >
              <el-select
                v-model="nodeData.properties.action_name"
                value-key="id"
                @change="showSetActionForm"
              >
                <template #header>
                  <el-input
                    v-model="actionSearchKeyword"
                    :suffix-icon="Search"
                    placeholder="搜索动作"
                    @input="searchActionList()"
                  />
                </template>
                <el-option
                  v-for="item in displayActionList"
                  :key="item.id"
                  :label="item.name"
                  :value="item"
                />
              </el-select>
              <!-- <el-row>
                <el-col v-for="(item, index) in actionList" :key="index">
                  <el-button
                    link
                    type="primary"
                    @click="showSetActionForm(item)"
                    >{{ item.name }}</el-button
                  >
                </el-col>
              </el-row> -->
            </el-form-item>

            <!-- <el-form-item label="动作:"></el-form-item> -->

            <!-- 默认展示该版本的第一个动作的setActionForm -->
            <el-form-item label="输入参数:">
              <div class="param-list" style="width: 100%">
                <el-form>
                  <!-- v-for中，先后顺序决定值，和名称无关 -->
                  <el-form-item
                    v-for="(item, index) in nodeData.properties.input_name"
                    :key="index"
                    style="padding-bottom: 10px"
                  >
                    <div class="row-item required">
                      <span
                        v-if="item.required == true"
                        style="color: red; margin-right: 5px"
                        >*</span
                      >
                      <span>{{ item.description }}({{ item.key }})</span>
                    </div>

                    <div v-if="item.type === 'boolean'" class="row-item">
                      <el-switch
                        v-model="nodeData.properties.input[item.key]"
                      />
                    </div>
                    <div v-else class="row-item input-item">
                      <uuid-display-input
                        v-model="nodeData.properties.input[item.key]"
                        :lf="props.lf"
                        :selected-tool="selectedTool"
                      />
                      <QuickValueSelection
                        ref="quickValueSelectionRef"
                        :upstreamNode="upstreamNode"
                        @click="handleQuickValueSelect()"
                        @copyNodeId="payload => addParams(payload, item)"
                      />
                    </div>
                  </el-form-item>
                </el-form>
              </div>
            </el-form-item>
            <el-form-item label="执行资源:">
              <el-select
                v-model="nodeData.properties.resources"
                multiple
                placeholder="新增资源"
                value-key="id"
                @change="handleResourceChange"
              >
                <el-option
                  v-for="item in resourceDetailed"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <!-- 资源详情 -->
            <el-form-item label="资源详情:" style="width: 100%">
              <div style="width: 100%">
                <div class="resource-header">
                  <el-button
                    type="primary"
                    size="small"
                    @click="handleAddResource"
                  >
                    <Icon icon="ep:plus" />
                    添加资源
                  </el-button>
                </div>
                <div
                  v-if="selectedResourceDetails.length > 0"
                  style="width: 100%; margin-top: 12px"
                >
                  <el-collapse
                    v-model="activeResourceNames"
                    class="resource-collapse"
                  >
                    <el-collapse-item
                      v-for="resourceDetail in selectedResourceDetails"
                      :key="resourceDetail.name"
                      :name="resourceDetail.name"
                      class="resource-collapse-item"
                    >
                      <template #title>
                        <div class="resource-title">
                          <el-tag type="primary" size="small">{{
                            resourceDetail.name
                          }}</el-tag>
                          <span class="param-count"
                            >({{
                              resourceDetailTableData(resourceDetail).length
                            }}个参数)</span
                          >
                        </div>
                      </template>
                      <el-table
                        :data="resourceDetailTableData(resourceDetail)"
                        border
                        size="small"
                        style="width: 100%"
                      >
                        <el-table-column
                          prop="paramName"
                          label="参数名称"
                          width="120"
                        />
                        <el-table-column
                          prop="description"
                          label="参数描述"
                          min-width="150"
                          show-overflow-tooltip
                        />
                        <el-table-column
                          prop="value"
                          label="参数值"
                          min-width="200"
                        />
                      </el-table>
                    </el-collapse-item>
                  </el-collapse>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="重试次数:">
              <el-input-number
                v-model="nodeData.properties.retry_times"
                :max="3"
                :min="0"
                class="mr-4"
                controls-position="right"
              />
              次
            </el-form-item>
            <el-form-item label="超时时间:">
              <el-input-number
                v-model="nodeData.properties.timeout"
                :min="1"
                class="mr-4"
                controls-position="right"
              />
              秒
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #footer>
        <div>
          <el-button @click="closeNodeEditDrawer()">取消</el-button>
          <el-button type="primary" @click="nodeEditConfirm(nodeDataRef)"
            >保存
          </el-button>
        </div>
      </template>
    </el-drawer>
  </div>
  <!-- 资源添加组件 -->
  <ResourcesAdd ref="resourcesAddRef" @success="handleResourceAddSuccess" />
</template>

<script lang="ts" setup>
import LogicFlow from "@logicflow/core";
import { computed, nextTick, onMounted, reactive, ref } from "vue";
import {
  apiGetActionList,
  apiGetResourceDetailed,
  apiGetToolList,
  apiGetToolVersionsList
} from "@/api/playbook";
import { Search } from "@element-plus/icons-vue";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import usePlaybookStore from "@/store/modules/playbook";
import UuidDisplayInput from "@/components/UuidDisplayInput.vue";
import QuickValueSelection from "../QuickValueSelection.vue";
import ResourcesAdd from "@/views/toolManagement/components/resourcesAdd.vue";

interface NodeDataForm {
  main: string;
  name: string;
  app_name: string;
  version: string;
  node_name: string;
  action_name: string;
  resource: string;
}

interface actionNodeParams {
  main: string;
  name: string;
  count: string;
  input: Object;
  scale: string;
  width: string;
  action: string;
  height: string;
  version: string;
  app_name: string;
  node_name: string;
  input_name: Object;
  action_name: string;
  isWebSocket: boolean;
}

interface resourceDetailedType {
  id: string;
  ctime: string;
  utime: string;
  deleted: boolean;
  tool_name: string;
  name: string;
  engine: null;
  resources: Object;
  creator: string;
  updater: string;
}

const props = defineProps({
  lf: LogicFlow
});

const isShowDrawer = ref(false);
const isShowActionDrawer = ref(false);
const isShowSetActionForm = ref(false);
const nodeData = ref<LogicFlow.NodeData>();
const versionList = ref();
const versionSelectedValue = ref();
const actionSelectedValue = ref();
const actionList = ref([]);
const displayActionList = ref([]);
const setActionList = ref<any>([]);
const activeName = ref("first");
const toolSearchKeyword = ref(); //应用搜索关键词
const actionSearchKeyword = ref(); //动作搜索关键词
const nodeDataRef = ref<FormInstance>();
const playbookStore = usePlaybookStore();
const toolList = ref(playbookStore.allToolList); // 初始值从 store 中获取
const actionNodeParams = ref<actionNodeParams>();
const selectedResources = ref<resourceDetailedType[]>([]);
const previousSelectedResourceIds = ref<string[]>([]);
const selectedResourceDetails = ref([]);
const resourceDetailed = ref([]);
const selectValue = ref();
const toolSelectedValue = ref();
const toolCurrentPage = ref(1);
const toolPageSize = ref(15);
const allToolList = ref([]);
const takeValue = ref(false);
const displayInput = ref<string[]>();
const takeValueSelectedValue = ref();
const selectedTool = ref();
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);
const upstreamNode = ref();
const quickValueSelectionRef = ref();
const activeResourceNames = ref<string[]>([]);
const retry = ref({
  count: 1,
  outTime: 300
});

onMounted(() => {
  getToolList({});
});

// 修改资源选择变化处理
const handleResourceChange = (selectedIds: string[]) => {
  selectedResources.value = resourceDetailed.value.filter(resource =>
    selectedIds.includes(resource.id)
  );
  // 清理不再选中的资源详情
  const selectedResourceNames = selectedResources.value.map(r => r.name);
  selectedResourceDetails.value = selectedResourceDetails.value.filter(detail =>
    selectedResourceNames.includes(detail.name)
  );

  // 清理不再选中资源的折叠面板状态
  activeResourceNames.value = activeResourceNames.value.filter(name =>
    selectedResourceNames.includes(name)
  );
  const prevSet = new Set(previousSelectedResourceIds.value);
  const addedIds = selectedIds.filter(id => !prevSet.has(id));
  if (addedIds.length > 0) {
    const addedResources = resourceDetailed.value.filter(r =>
      addedIds.includes(r.id)
    );
    addedResources.forEach(resource => {
      getResourceDetailed(nodeData.value.properties.app_name);
      // 自动展开新添加的资源
      if (!activeResourceNames.value.includes(resource.name)) {
        activeResourceNames.value.push(resource.name);
      }
    });
  }
  previousSelectedResourceIds.value = [...selectedIds];
};

// 获取资源详情
const getResourceDetailed = async (resourceName: string) => {
  let res: any = await apiGetResourceDetailed({
    name: resourceName
  });
  console.log(res);

  // 根据选中的资源名称过滤对应的资源详情
  if (res.code === 0 && res.data) {
    const selectedResourceNames = selectedResources.value.map(r => r.name);
    selectedResourceDetails.value = res.data.filter(item =>
      selectedResourceNames.includes(item.name)
    );
  }
};

// 将资源详情转换为表格数据格式
const resourceDetailTableData = resourceDetail => {
  if (!resourceDetail.resources) return [];

  return Object.entries(resourceDetail.resources).map(([key, value]) => ({
    paramName: key,
    description: (value as any).description || "",
    value: (value as any).value || ""
  }));
};

// 合并所有资源详情数据
const allResourceTableData = computed(() => {
  const allData = [];
  selectedResourceDetails.value.forEach(resourceDetail => {
    const resourceData = resourceDetailTableData(resourceDetail);
    resourceData.forEach(param => {
      allData.push({
        resourceName: resourceDetail.name,
        paramName: param.paramName,
        description: param.description,
        value: param.value
      });
    });
  });
  return allData;
});

const NodeDataRules = reactive<FormRules<NodeDataForm>>({
  node_name: [
    { required: true, message: "节点标题不能为空", trigger: "change" }
  ],
  name: [{ required: true, message: "请选择应用", trigger: "change" }],
  version: [{ required: true, message: "请选择版本", trigger: "blur" }],
  action_name: [{ required: true, message: "请选择动作", trigger: "blur" }],
  resource: [{ required: true, message: "请选择资源", trigger: "blur" }]
});

//打开抽屉(一级抽屉)
const openNodeEditDrawer = async (data: LogicFlow.NodeData) => {
  //获取节点数据
  nodeData.value = data;
  //清空上一次查询工具和动作的纪录
  toolSearchKeyword.value = "";
  actionSearchKeyword.value = "";
  //保存当前选中工具的信息
  allToolList.value.forEach(item => {
    if (item.name == nodeData.value.properties.app_name) {
      selectedTool.value = item;
      console.log("selectedTool.value:", selectedTool.value);
    }
  });

  //最后展示drawer
  isShowDrawer.value = true;

  //将应用名称填入选择应用下拉框中
  toolSelectedValue.value = nodeData.value.properties.name;

  //获取该工具的所有版本列表
  let res1: any = await apiGetToolVersionsList({
    name: nodeData.value.properties.app_name
  });
  versionList.value = res1.data;

  //获取该工具的所有资源模型
  let res2: any = await apiGetResourceDetailed({
    name: nodeData.value.properties.app_name
  });
  resourceDetailed.value = res2.data;
  console.log(res2);
  // 初始化上次选择与已选资源，避免首次打开误判为新增
  previousSelectedResourceIds.value = Array.isArray(
    nodeData.value.properties.resources
  )
    ? [...nodeData.value.properties.resources]
    : [];
  selectedResources.value = resourceDetailed.value.filter(resource => {
    return previousSelectedResourceIds.value.includes(resource.id);
  });
  // 自动选择时，逐个打印已选择资源
  selectedResources.value.forEach(resource => {
    console.log("已选择资源:", resource.name);
    getResourceDetailed(nodeData.value.properties.app_name);
  });
  //没有action_name的话，清空input和input_name
  if (!nodeData.value.properties.action_name) {
    nodeData.value.properties.input = {};
    nodeData.value.properties.input_name = [];
  }
  //判断该节点是新节点还是旧节点
  if (nodeData.value.properties.isOld == true) {
    // //清空上一次的表单校验结果
    // nextTick(() => {
    //   nodeDataRef.value.clearValidate();
    // });
    //是旧节点的话，将已选择的版本和动作填入下拉选择框中
    versionSelectedValue.value = nodeData.value.properties.version;
    actionSelectedValue.value = nodeData.value.properties.action_name;

    //请求该工具对应版本的动作列表数据
    await getActionList({
      name: nodeData.value.properties.app_name,
      version: nodeData.value.properties.version
    });
    //将对应动作的data保存到setActionList中
    Object.values(actionList.value).forEach(value => {
      if (value.name == nodeData.value.properties.action_name) {
        setActionList.value = value;
      }
    });
    //如果有重试次数和超时时间，修改为已有的重试次数和超时时间
    if (nodeData.value.properties.count && nodeData.value.properties.outTime) {
      retry.value.count = nodeData.value.properties.count;
      retry.value.outTime = nodeData.value.properties.outTime;
    }
    console.log("该节点为已有数据节点");
  } else {
    //新节点的话，默认展示最新的版本
    //请求该工具的所有版本
    versionSelectedValue.value = versionList.value[0].version;
    //请求最新版本的所有动作
    await getActionList({
      name: nodeData.value.properties.app_name,
      version: versionList.value[0].version
    });
    // //默认获取第一个动作
    // setActionList.value = actionList.value[0];
    // console.log("setActionList.value:", setActionList.value);
    // actionSelectedValue.value = setActionList.value.name;

    //添加input和input_name
    nodeData.value.properties.input = {};
    nodeData.value.properties.input_name = [];
    //清空setActionList
    setActionList.value = [];
    // //然后将动作的input的中文名保存到nodeData.value.properties.input_name中
    // if (setActionList.value.input) {
    //   setActionList.value.input.forEach(item => {
    //     nodeData.value.properties.input_name[item.key] = item.description;
    //   });
    // }
  }

  //清空上一次的表单校验结果
  nextTick(() => {
    if (nodeDataRef.value) {
      nodeDataRef.value.clearValidate();
    }
  });
};

//关闭抽屉
const closeNodeEditDrawer = () => {
  isShowActionDrawer.value = false;
  isShowDrawer.value = false;
};

//获取全部的工具
const getToolList = async ({ page = 1, size = 20, key = "" }) => {
  currentPage.value = page;
  pageSize.value = size;
  toolSearchKeyword.value = key;
  let res: any = await apiGetToolList({
    page: currentPage.value,
    size: pageSize.value,
    key: toolSearchKeyword.value
  });
  allToolList.value = res.data.tools;
  total.value = res.data.total;
};

const handleSizeChange = (val: number) => {
  getToolList({ size: val });
  console.log(`每页 ${val} 条`);
};
const handleCurrentChange = (val: number) => {
  getToolList({ page: val });
  console.log(`当前页: ${val}`);
};

// 切换工具
const switchTool = async item => {
  console.log("toolSelectedValue.value:", toolSelectedValue.value);
  console.log("item:", item);
  // 清空并修改nodeData的properties属性
  nodeData.value.properties = {
    main: item.main,
    name: item.description,
    app_name: item.name,
    node_name: item.description,
    retry_times: 0,
    timeout: 300,
    scale: 1,
    isOld: false,
    input: {},
    input_name: {}
  };
  //清空actionList和setActionList
  actionList.value = [];
  setActionList.value = [];
  console.log(nodeData.value);

  openNodeEditDrawer(nodeData.value);
  //清空上一次的表单校验结果
  nextTick(() => {
    if (nodeDataRef.value) {
      nodeDataRef.value.clearValidate();
    }
  });
};

// // 获取应用列表
// const searchToolList = () => {
//   // 如果有搜索关键词，则过滤应用列表
//   if (toolSearchKeyword.value) {
//     const filteredTools = toolList.value.filter(tool => {
//       return (
//         tool.description.includes(toolSearchKeyword.value) ||
//         tool.name.includes(toolSearchKeyword.value)
//       );
//     });
//     // 将过滤后的应用列表赋值给el-select的显示列表
//     // 假设你有一个响应式的应用列表用于显示，比如 displayToolList
//     toolList.value = filteredTools;
//   } else {
//     // 如果没有搜索关键词，则显示所有工具
//     toolList.value = playbookStore.allToolList;
//   }
// };

//获取动作列表
const searchActionList = () => {
  // 如果有搜索关键词，则过滤应用列表
  if (actionSearchKeyword.value) {
    const filteredActionList = actionList.value.filter(action => {
      return action.name.includes(actionSearchKeyword.value);
    });
    // 将过滤后的应用列表赋值给el-select的显示列表
    // 假设你有一个响应式的应用列表用于显示，比如 displayActionList
    displayActionList.value = filteredActionList;
  } else {
    // 如果没有搜索关键词，则显示所有工具
    displayActionList.value = actionList.value;
  }
};

//获取工具当前版本的所有动作列表
const getActionList = async item => {
  let res: any = await apiGetActionList({
    name: item.name,
    version: item.version
  });
  actionList.value = res.data.actions;
  displayActionList.value = res.data.actions;
  console.log("actionList");
  console.log(actionList.value[0]);
  //暂存工具version到nodeData.value.properties上
  nodeData.value.properties.version = item.version;
};

//用户选择动作后，显示该内容
const showSetActionForm = (item: any) => {
  //清空上一次的表单校验结果
  nextTick(() => {
    if (nodeDataRef.value) {
      nodeDataRef.value.clearValidate();
    }
  });
  setActionList.value = item;
  nodeData.value.properties.action_name = item.name;
  nodeData.value.properties.action = item.func;
  //先清空nodeData.value.properties.input_name
  nodeData.value.properties.input_name = [];
  //将动作的input保存到nodeData.value.properties.input_name中
  nodeData.value.properties.input_name = setActionList.value.input;
  //
  nodeData.value.properties.input_name.forEach(item => {
    if (item.type === "boolean") {
      nodeData.value.properties.input[item.key] = false;
    } else {
      nodeData.value.properties.input[item.key] = "";
    }
  });
  // 动作切换时，打印当前已选资源（逐个）
  const currentSelectedIds = Array.isArray(nodeData.value.properties.resources)
    ? nodeData.value.properties.resources
    : [];
  selectedResources.value = resourceDetailed.value.filter(r =>
    currentSelectedIds.includes(r.id)
  );
  selectedResources.value.forEach(resource => {
    console.log("动作切换-当前选中资源:", resource.name);
    getResourceDetailed(nodeData.value.properties.app_name);
  });
};

// 用户保存数据按钮confirm
const nodeEditConfirm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;

  // 在 validate 方法中传入一个 Promise 风格的函数来等待验证结果
  const valid = await new Promise<boolean>(resolve => {
    formEl.validate((valid, fields) => {
      // 如果表单本身的基础验证失败，则直接返回 false
      if (!valid) {
        resolve(false);
        return;
      }

      // 自定义验证：检查必填项
      let allRequiredFieldsFilled = true;
      nodeData.value.properties.input_name.forEach(item => {
        if (
          item.required === true &&
          !nodeData.value.properties.input[item.key] &&
          typeof nodeData.value.properties.input[item.key] !== "boolean"
        ) {
          ElMessage.error(`${item.key}(${item.description})为必填项`);
          allRequiredFieldsFilled = false;
        }
      });

      resolve(allRequiredFieldsFilled);
    });
  });

  if (valid) {
    // 新节点标识改为旧节点
    nodeData.value.properties.isOld = true;
    // 将全部暂存的数据保存到nodeData的properties上(这步好像没必要？先留着看看要不要改)
    nodeData.value.properties.action = setActionList.value.func;
    nodeData.value.properties.action_name = setActionList.value.name;
    // 在用户保存数据时，对input内的数据进行过滤，只收集目前选择的动作节点所需的参数
    // // (在用户关闭drawer时，也需要进行过滤，防止因为是响应式数据，用户输入了数据，却没有保存，直接退出了，污染了input的数据)
    // nodeData.value.properties.input = filterInput(
    //   nodeData.value.properties.input,
    //   nodeData.value.properties.input_name
    // );

    // 推送数据到节点的properties上并更新(很重要，没送上来你前面怎么改都没用)
    props.lf.setProperties(nodeData.value.id, nodeData.value.properties!);
    isShowDrawer.value = false;
    console.log("submit!");
    console.log(props.lf.getGraphData());
  } else {
    console.log("表单输入有问题或必填项未填写");
  }
};

// //过滤掉input中不需要的参数
// const filterInput = (input, input_name) => {
//   // 获取input_name的所有键名
//   const keys = input_name.forEach(item => {
//     console.log(item);
//   });
//   // 筛选出input中键名在keys中的属性
//   const result = {};
//   for (let key in input) {
//     if (keys.includes(key)) {
//       result[key] = input[key];
//     }
//   }
//   return result;
// };

//快速取值
const handleQuickValueSelect = () => {
  upstreamNode.value = getUpstreamNodes(props.lf, nodeData.value.id);
  console.log("upstreamNode.value:", upstreamNode.value);
};

/**
 * 获取指定节点的所有上游节点（递归追溯）
 * @param {LogicFlow} lf - LogicFlow 实例
 * @param {string|object} node - 节点 ID 或节点对象
 * @param {object} options - 配置项
 *   - full: boolean 是否返回完整节点对象（默认 false，返回 ID）
 *   - includeSelf: boolean 是否包含自身（默认 false）
 *   - dedupe: boolean 是否去重（默认 true）
 * @returns {Array<string|object>} 上游节点 ID 或节点对象列表
 */
function getUpstreamNodes(
  lf,
  node,
  options = { full: true, includeSelf: false, dedupe: true }
) {
  const { full, includeSelf, dedupe } = options;

  const graphData = lf.getGraphData();
  const nodeMap = new Map(graphData.nodes.map(n => [n.id, n]));

  // 构建上游映射：target -> [source, source, ...]
  const upstreamMap = new Map();
  graphData.edges.forEach(edge => {
    const { sourceNodeId, targetNodeId } = edge;
    if (!upstreamMap.has(targetNodeId)) {
      upstreamMap.set(targetNodeId, []);
    }
    upstreamMap.get(targetNodeId).push(sourceNodeId);
  });

  const nodeId = typeof node === "string" ? node : node.id;
  const visited = new Set();
  const result = [];

  function dfs(id) {
    if (dedupe && visited.has(id)) return;
    visited.add(id);

    if (id !== nodeId || includeSelf) {
      result.push(id);
    }

    const parents = upstreamMap.get(id) || [];
    for (const parentId of parents) {
      dfs(parentId);
    }
  }

  dfs(nodeId);

  return full ? result.map(id => nodeMap.get(id)) : result;
}

const addParams = (payload: any, item: any) => {
  console.log(payload);
  console.log(item);
  nodeData.value.properties.input[item.key] += payload;
};

// 添加组件引用
const resourcesAddRef = ref();

// 处理添加资源
const handleAddResource = () => {
  // 获取当前选择的工具信息
  const toolName = nodeData.value.properties.app_name;
  const toolModel = {
    resources: {}, // 这里需要根据实际情况获取工具的资源配置
    tool_name: toolName
  };
  // 打开资源添加弹窗
  resourcesAddRef.value?.openDialog(toolModel.resources, toolName);
};

// 处理资源添加成功
const handleResourceAddSuccess = async (data: any) => {
  console.log("资源添加成功:", data);

  // 刷新资源列表选项
  if (nodeData.value.properties.app_name) {
    try {
      // 重新获取资源列表
      let res: any = await apiGetResourceDetailed({
        name: nodeData.value.properties.app_name
      });
      if (res.code === 0 && res.data) {
        // 更新资源选项列表
        resourceDetailed.value = res.data;
        // 根据返回的资源名称自动选中新添加的资源
        if (data.name) {
          const newResource = res.data.find(item => item.name === data.name);
          if (
            newResource &&
            !nodeData.value.properties.resources.includes(newResource.id)
          ) {
            nodeData.value.properties.resources = [
              ...nodeData.value.properties.resources,
              newResource.id
            ];
            // 触发资源选择变化处理
            handleResourceChange(nodeData.value.properties.resources);
          }
        }
      }
    } catch (error) {
      console.error("刷新资源列表失败:", error);
      ElMessage.error("刷新资源列表失败");
    }
  } else {
    ElMessage.error("工具名称不存在，无法刷新资源列表");
  }
};

// 复制节点ID
const $_copyNodeId = async (id: string) => {
  try {
    // 使用现代Clipboard API替代已废弃的execCommand
    await navigator.clipboard.writeText(`\$\{${id}\}`);
    ElMessage.success("已复制该节点ID");
  } catch (err) {
    console.error("复制失败:", err);
    // 添加失败反馈（可根据项目需求替换为错误提示）
  }
};

defineExpose({
  openNodeEditDrawer,
  closeNodeEditDrawer
});
</script>

<style lang="scss" scoped>
.action-drawer-body {
  padding-right: 50px;
}

.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 6000;
}

.param-list {
  border: 1px solid #dcdfe6;
  padding: 0px 10px;
}

.row-item {
  width: 100%;
}

.required {
  display: flex;
  align-items: center;
}

.input-item {
  display: flex;
}

.resource-item {
  margin: 5px 0;
  padding: 5px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.resource-detail-item {
  width: 100%;
  margin: 15px 0;
  padding: 0px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fafafa;
}

.resource-name {
  font-weight: bold;
  color: #303133;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.resource-detail-item .el-table) {
  width: 100% !important;
}

:deep(.resource-detail-item .el-table .cell) {
  word-break: break-all;
  white-space: normal;
}

:deep(.el-table .cell) {
  word-break: break-all;
  white-space: normal;
  line-height: 1.5;
}

.resource-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 8px;
}

.resource-collapse {
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  overflow: hidden;
}

.resource-collapse-item {
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.resource-collapse-item:last-child {
  border-bottom: none;
}

.resource-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.param-count {
  color: var(--el-text-color-secondary);
  font-size: 12px;
}

:deep(.el-collapse-item__header) {
  padding: 12px 16px;
  background-color: var(--el-fill-color-lighter);
}

:deep(.el-collapse-item__content) {
  padding: 0;
}
</style>
