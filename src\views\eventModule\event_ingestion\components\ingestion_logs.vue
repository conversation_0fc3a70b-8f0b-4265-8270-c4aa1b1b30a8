<template>
  <div class="binding-container">
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      :destroy-on-close="true"
      :lock-scroll="true"
      class="binding-dialog"
      top="4vh"
      width="90vw"
      @open="onOpen"
    >
      <template #header>
        <div style="display: flex; align-items: center">
          <span>事件接入日志（最新10条）</span>
          <el-button
            :loading="loading"
            circle
            class="refresh-button"
            style="margin-left: 12px"
            type="primary"
            @click="refreshLogs"
          >
            <template #icon>
              <el-icon>
                <Refresh />
              </el-icon>
            </template>
          </el-button>
        </div>
      </template>
      <div class="dialog-content">
        <el-table :data="logs" border height="100%" style="width: 100%">
          <el-table-column label="序号" type="index" width="60">
            <template #default="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="原始日志">
            <template #default="scope">
              <div class="log-item">
                <div class="log-content">
                  <template v-if="scope.row.raw && scope.row.raw !== '-'">
                    {{ scope.row.raw }}
                  </template>
                  <el-empty v-else :image-size="40" description="暂无数据" />
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="处理后日志">
            <template #default="scope">
              <div class="log-item">
                <div class="log-content">
                  <json-viewer
                    v-if="
                      scope.row.parsed &&
                      scope.row.parsed !== '-' &&
                      scope.row.parsed !== 'null'
                    "
                    :value="scope.row.parsed"
                    :expand-depth="999"
                    copyable
                    sort
                  />
                  <el-empty v-else :image-size="40" description="暂无数据" />
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { defineEmits, defineProps, ref, watch } from "vue";
import { eventIngestionOriginalLog } from "@/api/event";
import { Refresh } from "@element-plus/icons-vue";
import JsonViewer from "vue-json-viewer";

const props = defineProps<{ eventId?: string | number; visible: boolean }>();
const emit = defineEmits(["update:visible"]);

// 控制弹窗显示
const dialogVisible = ref(props.visible);
watch(
  () => props.visible,
  val => {
    dialogVisible.value = val;
  }
);
watch(dialogVisible, val => {
  if (!val) emit("update:visible", false);
});

// 日志相关
const logs = ref<{ raw: string; parsed: string }[]>([]);
const originalLogs = ref<string[]>([]);
const loading = ref(false);

// 新增：日志数据请求与处理函数
const fetchLogs = async () => {
  loading.value = true;
  try {
    const res = (await eventIngestionOriginalLog({ id: props.eventId })) as any;
    logs.value = (res.data || []).map((item: any) => {
      // 原始日志：直接使用原始数据，不做任何处理
      let raw = "";
      let parsed = "-";
      try {
        if (typeof item === "string") {
          const obj = JSON.parse(item);
          raw = obj.message || item; // 优先取message字段
          parsed = obj.log || "-"; // 处理后日志取log字段
        } else if (typeof item === "object" && item !== null) {
          raw = item.message || JSON.stringify(item); // 优先取message字段
          parsed = item.log || "-"; // 处理后日志取log字段
        } else {
          raw = String(item);
          parsed = "-";
        }
      } catch {
        raw = typeof item === "string" ? item : JSON.stringify(item);
        parsed = "-";
      }
      return { raw, parsed };
    });
    // 原始日志数组
    originalLogs.value = logs.value.map(item => item.raw);
  } finally {
    loading.value = false;
  }
};

const refreshLogs = () => {
  fetchLogs();
};

const handleClose = (done: () => void) => {
  onClose();
  done();
};

const onClose = () => {
  emit("update:visible", false);
  logs.value = [];
  originalLogs.value = [];
};

const onOpen = () => {
  fetchLogs();
};
</script>

<style lang="scss" scoped>
.log-item {
  position: relative;
  display: flex;
  padding: 8px 0;

  .log-content {
    flex: 1;
    border-radius: 4px;
    padding: 12px;
    font-family: "Menlo", "Monaco", "Consolas", "Courier New", monospace;
    font-size: 13px;
    line-height: 1.5;
    white-space: pre-wrap;
    word-break: break-all;
    color: var(--el-text-color-primary);
    margin: 0;
  }
}

.dialog-content {
  height: 80vh;
  overflow: auto;
}

.log-raw {
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
