<script lang="ts" setup>
import Motion from "./utils/motion";
import { useRouter } from "vue-router";
import { message } from "@/utils/message";
import { loginRules } from "./utils/rule";
import { useNav } from "@/layout/hooks/useNav";
import type { FormInstance } from "element-plus";
import { useLayout } from "@/layout/hooks/useLayout";
import { useUserStoreHook } from "@/store/modules/user";
import { getTopMenu, initRouter } from "@/router/utils";
import { avatar } from "./utils/static";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { onMounted, onUnmounted, reactive, ref } from "vue";
import { useDataThemeChange } from "@/layout/hooks/useDataThemeChange";
import Lock from "@iconify-icons/ri/lock-fill";
import User from "@iconify-icons/ri/user-3-fill";
import { getConfig } from "@/config";
import ReQrcode from "./components/ReQrcode.vue";
import { getPicture } from "@/api/system";

const TITLE = getConfig("Title");
const VERSION = getConfig("Version");
const COPYRIGHT = getConfig("Content");

defineOptions({
  name: "Login"
});
const router = useRouter();
const loading = ref(false);
const ruleFormRef = ref<FormInstance>();
const qrcodeRef = ref();

const { initStorage } = useLayout();
initStorage();

const { dataTheme, overallStyle, dataThemeChange } = useDataThemeChange();
dataThemeChange(overallStyle.value);

const { title } = useNav();

const ruleForm = reactive({
  username: "",
  password: ""
});

const onLogin = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      loading.value = true;
      useUserStoreHook()
        .loginByUsername({
          username: ruleForm.username,
          password: ruleForm.password
        })
        .then(res => {
          if (res.code === 0) {
            // MFA已启用的情况
            if (res.data.mfa_enable === true) {
              message(
                res.data.mfa_bind
                  ? "请输入MFA验证码"
                  : "首次使用需要完成MFA绑定",
                {
                  type: res.data.mfa_bind ? "info" : "warning"
                }
              );
              qrcodeRef.value.openDialog({
                username: ruleForm.username,
                mfa_uri: res.data.mfa_uri,
                mfa_bind: res.data.mfa_bind
              });
              return;
            }
            // MFA未启用，直接登录
            return initRouter().then(() => {
              router.push(getTopMenu(true).path).then(() => {
                message("登录成功", { type: "success" });
              });
            });
          }
        })
        .finally(() => (loading.value = false));
    }
  });
};

const bg = ref<string>("");

onMounted(async () => {
  bg.value = await getPicture("background");
});

onUnmounted(() => {
  if (bg.value) URL.revokeObjectURL(bg.value);
});
</script>

<template>
  <div class="select-none">
    <!-- <img :src="bg" class="wave" /> -->
    <div class="flex-c absolute right-5 top-3">
      <!-- 主题 -->
      <!-- <el-switch
        v-model="dataTheme"
        inline-prompt
        :active-icon="dayIcon"
        :inactive-icon="darkIcon"
        @change="dataThemeChange"
      /> -->
    </div>
    <div class="login-container">
      <div class="img">
        <img v-if="bg" :src="bg" alt="加载失败" />
        <img v-else alt="加载失败" src="/bg.png" />
      </div>
      <div class="login-box">
        <div class="login-form">
          <avatar class="avatar" />
          <Motion>
            <h2 class="outline-none">{{ title }}</h2>
          </Motion>

          <el-form
            ref="ruleFormRef"
            :model="ruleForm"
            :rules="loginRules"
            size="large"
            style="width: 100%"
          >
            <Motion :delay="100">
              <el-form-item
                :rules="[
                  {
                    required: true,
                    message: '请输入账号',
                    trigger: 'blur'
                  }
                ]"
                prop="username"
              >
                <el-input
                  v-model="ruleForm.username"
                  :prefix-icon="useRenderIcon(User)"
                  clearable
                  placeholder="账号"
                />
              </el-form-item>
            </Motion>

            <Motion :delay="150">
              <el-form-item prop="password">
                <el-input
                  v-model="ruleForm.password"
                  :prefix-icon="useRenderIcon(Lock)"
                  clearable
                  placeholder="密码"
                  show-password
                  @keyup.enter.prevent="onLogin(ruleFormRef)"
                />
              </el-form-item>
            </Motion>
            <Motion :delay="250">
              <el-button
                :loading="loading"
                class="w-full mt-4"
                size="default"
                type="primary"
                @click="onLogin(ruleFormRef)"
                @keyup.enter.prevent="onLogin(ruleFormRef)"
              >
                登录
              </el-button>
            </Motion>
          </el-form>
        </div>
      </div>
    </div>
    <ReQrcode ref="qrcodeRef" />
  </div>
  <footer
    class="layout-footer text-[rgba(0,0,0,0.6)] dark:text-[rgba(220,220,242,0.8)]"
  >
    <span>{{ TITLE }} {{ VERSION }} {{ COPYRIGHT }}</span>
  </footer>
</template>

<style scoped>
@import url("@/style/login.css");
</style>

<style lang="scss" scoped>
:deep(.el-input-group__append, .el-input-group__prepend) {
  padding: 0;
}

.layout-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  text-align: center;
  width: 100%;
  padding: 0 0 8px;
  font-size: 14px;
}
</style>
