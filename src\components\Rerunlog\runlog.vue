<template>
  <div class="runlog-card">
    <div class="runlog-header">
      <div class="runlog-title">
        <span>{{ title }}</span>
        <span v-if="timestamp" class="runlog-time">{{
          formatTime(timestamp)
        }}</span>
      </div>
    </div>

    <!-- 概要信息 -->
    <div v-if="showSummary" class="info-row">
      <div class="info-block">
        <div class="info-label">开始时间：</div>
        <div class="info-value">
          {{ getFirstStartTime() }}
        </div>
      </div>
      <div class="info-block">
        <div class="info-label">耗时：</div>
        <div class="info-value">
          {{ getDuration() }}
        </div>
      </div>
      <div class="info-block">
        <div class="info-label">状态：</div>
        <div class="info-value">
          <span :class="getOverallStatusClass()" class="status-tag">
            {{ getOverallStatusText() }}
          </span>
        </div>
      </div>
    </div>

    <!-- 详情折叠面板 -->
    <el-collapse v-model="activeCollapse" accordion>
      <el-collapse-item :name="collapseKey" title="查看详情">
        <div class="runlog-content">
          <!-- 时间轴展示 -->
          <div v-if="items && items.length > 0" class="custom-timeline">
            <div
              v-for="(item, index) in items"
              :key="item.id || index"
              class="timeline-node"
            >
              <div class="timeline-line-container">
                <div class="timeline-dot" />
                <div class="timeline-line" />
              </div>
              <div class="timeline-node-content">
                <div class="runlog-item-header">
                  <span class="node-type">{{ item.node_name }}</span>
                </div>
                <div class="runlog-item-details">
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="开始时间">
                      {{ item.start_time || item.timestamp || "-" }}
                    </el-descriptions-item>
                    <el-descriptions-item label="结束时间">
                      {{ item.end_time || item.utime || "-" }}
                    </el-descriptions-item>
                    <el-descriptions-item
                      v-if="item.tool_display_name || item.tool_name"
                      label="应用名称"
                    >
                      {{ item.tool_display_name || "系统工具" }}
                    </el-descriptions-item>
                    <el-descriptions-item
                      v-if="!item.action_name || !item.action_display_name"
                      label="剧本名称"
                    >
                      {{ item.playbook_name || "未命名" }}
                    </el-descriptions-item>
                    <el-descriptions-item
                      v-if="item.node_type === 'action-node'"
                      label="应用版本"
                    >
                      {{ item.tool_version }}
                    </el-descriptions-item>
                    <el-descriptions-item
                      v-if="item.action_name || item.action_display_name"
                      label="动作名称"
                    >
                      {{
                        item.action_display_name || item.action_name || "未命名"
                      }}
                    </el-descriptions-item>
                    <el-descriptions-item label="状态">
                      <span
                        :class="getStatusClass(item.status, item.node_type)"
                        class="status-tag"
                      >
                        {{ getStatusText(item.status, item.node_type) }}
                      </span>
                    </el-descriptions-item>
                    <el-descriptions-item
                      v-if="item.resource_name"
                      label="资源名称"
                    >
                      {{ item.resource_name || "未命名" }}
                    </el-descriptions-item>
                    <el-descriptions-item
                      v-if="
                        item.node_type == 'start-node' ||
                        (item.node_type != 'end-node' && item.args)
                      "
                      :label="
                        item.node_type === 'start-node'
                          ? '剧本参数'
                          : '输入参数'
                      "
                      :span="2"
                    >
                      <div class="input-params">
                        <json-viewer
                          v-if="item.args"
                          :expand-depth="1"
                          :value="parseArgs(item.args)"
                          sort
                        />
                        <p v-else style="font-size: 14px">无</p>
                      </div>
                    </el-descriptions-item>
                    <el-descriptions-item
                      v-if="
                        item.node_type != 'start-node' &&
                        item.node_type != 'end-node'
                      "
                      :span="2"
                      label="输出结果"
                    >
                      <div class="result-output">
                        <template v-if="item.result || item.output">
                          <json-viewer
                            :expand-depth="999"
                            :value="parseArgs(item.result || item.output)"
                            copyable
                            sort
                          />
                        </template>
                      </div>
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </div>
            </div>
          </div>
          <!-- 单条记录的JSON展示 -->
          <div v-else-if="singleData">
            <div class="log-json-label">详细信息</div>
            <json-viewer
              :expand-depth="999"
              :value="parseArgs(singleData)"
              copyable
              sort
              style="background-color: #f5f7fa"
            />
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from "vue";
import { formatTime } from "@/utils/warChatTime";
import JsonViewer from "vue-json-viewer";
import {
  getStatusClass as getStatusClassUtil,
  getStatusText as getStatusTextUtil,
  isFailStatus,
  isRunningStatus
} from "@/utils/statusUtils";

// Props定义
interface RunlogItem {
  id?: string;
  node_id?: string;
  node_name?: string;
  node_type?: string;
  tool_name?: string;
  tool_version?: string;
  tool_display_name?: string;
  action_name?: string;
  action_display_name?: string;
  status: number | string;
  args?: any;
  result?: string;
  output?: string;
  start_time?: string;
  end_time?: string;
  timestamp?: string;
  utime?: string;
  resource_name?: string;
  playbook_name?: string;
}

const props = defineProps<{
  // 标题
  title?: string;
  // 时间戳
  timestamp?: string;
  // 多条记录（用于分组展示）
  items?: RunlogItem[];
  // 单条记录数据
  singleData?: any;
  // 是否显示概要信息
  showSummary?: boolean;
  // 折叠面板的key
  collapseKey?: string;
  defaultOpen?: boolean;
}>();

// 折叠面板状态
const activeCollapse = ref<string[]>([]);

// 自动展开逻辑
const openCollapse = () => {
  if (props.defaultOpen && props.collapseKey) {
    activeCollapse.value = [props.collapseKey];
  } else {
    activeCollapse.value = [];
  }
};

// 首次挂载时
onMounted(() => {
  openCollapse();
});

// 监听 defaultOpen 或 collapseKey 变化
watch(
  () => [props.defaultOpen, props.collapseKey],
  () => {
    openCollapse();
  }
);

// 获取第一条记录的开始时间
const getFirstStartTime = () => {
  if (!props.items || props.items.length === 0) return "-";
  const firstItem = props.items[0];
  return firstItem.start_time || firstItem.timestamp || "-";
};

// 计算执行耗时
const getDuration = () => {
  if (!props.items || props.items.length === 0) return "-";

  const firstItem = props.items[0];
  const lastItem = props.items[props.items.length - 1];

  const start = firstItem.start_time || firstItem.timestamp;
  const end = lastItem.end_time || lastItem.utime;

  if (start && end) {
    const startTime = new Date(start).getTime();
    const endTime = new Date(end).getTime();
    const durationMs = endTime - startTime;
    const durationSeconds = Math.round((durationMs / 1000) * 100) / 100;
    return `${durationSeconds} 秒`;
  }
  return "-";
};

// 获取整体状态样式类
const getOverallStatusClass = () => {
  if (!props.items || props.items.length === 0) return "fail";

  // 如果是单个记录
  if (props.items.length === 1) {
    const item = props.items[0];
    return getStatusClassUtil(item.status);
  }

  // 多条记录：有一个失败就整体失败
  const hasFailedItem = props.items.some(item => isFailStatus(item.status));
  if (hasFailedItem) {
    return "fail";
  }

  // 检查是否有end-node
  const hasEndNode = props.items.some(item => item.node_type === "end-node");
  if (hasEndNode) {
    const endNode = props.items.find(item => item.node_type === "end-node");
    return getStatusClassUtil(endNode?.status);
  }

  // 检查是否有运行中或等待中的状态
  const hasRunningOrWaiting = props.items.some(item =>
    isRunningStatus(item.status)
  );
  if (hasRunningOrWaiting) {
    return "running";
  }

  // 默认成功
  return "success";
};

// 获取整体状态文本
const getOverallStatusText = () => {
  if (!props.items || props.items.length === 0) return "失败";

  // 如果是单个记录
  if (props.items.length === 1) {
    const item = props.items[0];
    return getStatusTextUtil(item.status);
  }

  // 多条记录：有一个失败就整体失败
  const hasFailedItem = props.items.some(item => isFailStatus(item.status));
  if (hasFailedItem) {
    return "失败";
  }

  // 检查是否有end-node
  const hasEndNode = props.items.some(item => item.node_type === "end-node");
  if (hasEndNode) {
    const endNode = props.items.find(item => item.node_type === "end-node");
    return getStatusTextUtil(endNode?.status);
  }

  // 检查是否有运行中或等待中的状态
  const hasRunningOrWaiting = props.items.some(item =>
    isRunningStatus(item.status)
  );
  if (hasRunningOrWaiting) {
    return "执行中";
  }

  // 默认成功
  return "成功";
};

// 获取单个节点状态样式类
const getStatusClass = (status: any, nodeType?: string) => {
  // 只有在多组合并的情况下才应用特殊逻辑
  if (props.items && props.items.length > 1) {
    const hasFailedItem = props.items.some(item => isFailStatus(item.status));
    if (hasFailedItem) {
      return "fail";
    }
    const hasEndNode = props.items.some(item => item.node_type === "end-node");
    if (hasEndNode) {
      return "success";
    }
    if (nodeType !== "start-node") {
      return "running";
    }
  }

  // 使用统一的状态处理函数
  return getStatusClassUtil(status);
};

// 获取单个节点状态文本
const getStatusText = (status: any, nodeType?: string) => {
  // 只有在多组合并的情况下才应用特殊逻辑
  if (props.items && props.items.length > 1) {
    const hasFailedItem = props.items.some(item => isFailStatus(item.status));
    if (hasFailedItem) {
      return "失败";
    }
    const hasEndNode = props.items.some(item => item.node_type === "end-node");
    if (hasEndNode) {
      return "成功";
    }
    if (nodeType !== "start-node") {
      return "执行中";
    }
  }
  // 使用统一的状态处理函数
  return getStatusTextUtil(status);
};

// 解析参数函数，用于vue-json-viewer
const parseArgs = (args: any) => {
  try {
    // if (!args) return "无参数";
    // 如果是字符串，尝试解析为JSON
    if (typeof args === "string") {
      // 先尝试解析为JSON
      try {
        return JSON.parse(args);
      } catch (jsonError) {
        // 如果JSON解析失败，说明是普通字符串，直接返回字符串
        return args;
      }
    }
    // 如果已经是对象，直接返回
    if (typeof args === "object") {
      return args;
    }
    // 其他情况返回空对象
    return {};
  } catch (error) {
    console.error("解析参数失败:", error);
    return {};
  }
};
</script>

<style scoped>
.runlog-card {
  background: #f8fafc;
  border: 1px solid #e0e7ef;
  border-radius: 8px;
  padding: 16px;
  margin: 10px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
}

.runlog-card :deep(.el-collapse-item__header) {
  padding: 12px 16px;
  border-radius: 6px;
}

.runlog-card :deep(.el-collapse-item__content) {
  padding: 0;
}

.runlog-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.runlog-title {
  display: flex;
  flex-direction: column;
}

.runlog-title span:first-child {
  font-weight: bold;
  color: #2c7fe4;
}

.runlog-time {
  font-size: 12px;
  color: #888;
}

/* 概要信息样式 */
.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #fafafa;
  border-radius: 6px;
}

.info-block {
  text-align: left;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  white-space: nowrap;
}

.info-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 0;
  white-space: nowrap;
}

.info-value {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
}

.status-tag {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  color: #fff;
}

.status-tag.success {
  background: #52c41a;
}

.status-tag.fail {
  background: #f5222d;
}

.status-tag.running {
  background: #f3c42b;
}

/* 时间轴样式 */
.custom-timeline {
  padding: 0px 0;
}

.timeline-node {
  display: flex;
  align-items: stretch;
  margin-bottom: 24px;
  position: relative;
}

.timeline-node:last-child {
  margin-bottom: 0;
}

.timeline-node:last-child .timeline-line {
  bottom: 0;
}

.timeline-line-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 16px;
  margin-left: 5px;
  margin-top: 5px;
  position: relative;
}

.timeline-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #2c7fe4;
  border: 2px solid #fff;
  box-shadow: 0 0 0 2px #2c7fe4;
  position: relative;
  z-index: 2;
  flex-shrink: 0;
}

.timeline-line {
  width: 2px;
  background-color: #2c7fe4;
  position: absolute;
  top: 16px;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
}

.timeline-node-content {
  flex: 1;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.runlog-item-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.node-type {
  background-color: #2c7fe4;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.runlog-item-details {
  margin-top: 8px;
}

.runlog-item-details :deep(.el-descriptions__label) {
  font-weight: 500;
  color: #666;
  width: 120px;
  min-width: 120px;
  max-width: 140px;
  white-space: nowrap;
}

.runlog-item-details :deep(.el-descriptions__content) {
  color: #333;
  word-break: break-all;
  white-space: pre-wrap;
  min-width: 140px;
  max-width: 400px;
}

.result-output,
.input-params {
  max-height: 500px;
  overflow-y: auto;
  padding: 8px;
  border-radius: 4px;
  font-family: "Fira Mono", "Consolas", monospace;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 单条记录JSON展示 */
.runlog-content {
  background: #fff;
  border-radius: 4px;
  font-family: "Fira Mono", "Consolas", monospace;
  font-size: 13px;
  color: #333;
  white-space: pre-wrap;
  word-break: break-all;
}

.log-json-label {
  font-size: 13px;
  color: #888;
  margin-bottom: 4px;
}

.log-json-content {
  background: transparent;
  color: #333;
  font-size: 13px;
  white-space: pre-wrap;
  word-break: break-all;
  margin: 0;
  padding: 0;
}
</style>
