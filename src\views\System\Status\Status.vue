<template>
  <div class="status-container">
    <el-card>
      <div class="header">
        <h2>系统状态</h2>
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        :span-method="handleSpanMethod"
        class="dark:bg-[#141414]"
        style="margin: 20px 0"
      >
        <el-table-column label="主机IP" prop="host" width="200" />
        <el-table-column label="服务名称" prop="name" width="200" />
        <el-table-column label="运行状态" prop="run_status" width="150" />
        <el-table-column label="启动时间" prop="start_time" width="250">
          <template #default="scope">
            {{ formatTime(scope.row.start_time) }}
          </template>
        </el-table-column>
        <el-table-column
          label="CPU使用率（上限是CPU核数*100%）"
          prop="cpu_percent"
        >
          <template #default="scope">
            {{ scope.row.cpu_percent }}
          </template>
        </el-table-column>
        <el-table-column label="内存使用率（上限是100%）" prop="mem_percent">
          <template #default="scope">
            {{ scope.row.mem_percent }}
          </template>
        </el-table-column>
        <el-table-column label="健康检查结果" prop="health" width="200">
          <template #default="scope">
            <el-tag
              :type="scope.row.health === 'healthy' ? 'success' : 'danger'"
              size="small"
            >
              {{ scope.row.health === "healthy" ? "正常" : "异常" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="检查时间" prop="check_time" width="250">
          <template #default="scope">
            {{ formatTime(scope.row.check_time) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import { ElMessage } from "element-plus";
import { statusAll } from "@/api/system";

const tableData = ref([]);
const loading = ref(false);

// const getAuditList = () => {
//   statusAll({})
//     .then(res => {
//       console.log(res);
//       tableData.value = res.data || [];
//     })
//     .catch(err => {
//       console.error(err);
//       ElMessage.error("获取状态数据失败");
//     });
// };

// 时间格式化函数
const formatTime = timestamp => {
  if (!timestamp) return "";
  const date = new Date(timestamp);
  return date
    .toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false
    })
    .replace(/\//g, "-");
};
// 新增合并算法
const spanArr = ref([]); // 存储合并配置
const computeSpans = data => {
  const arr = [];
  let pos = 0;
  data.forEach((item, index) => {
    if (index === 0) {
      arr.push(1);
      pos = 0;
    } else {
      if (data[index].host === data[index - 1].host) {
        arr[pos] += 1;
        arr.push(0);
      } else {
        arr.push(1);
        pos = index;
      }
    }
  });
  return arr;
};

// 合并单元格处理方法
const handleSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  if (columnIndex === 0) {
    // 只在主机IP列应用合并
    const _row = spanArr.value[rowIndex];
    const _col = _row > 0 ? 1 : 0;
    return {
      rowspan: _row,
      colspan: _col
    };
  }
};

// 修改获取数据方法
const getAuditList = () => {
  statusAll({})
    .then(res => {
      tableData.value = res.data || [];
      spanArr.value = computeSpans(tableData.value); // 计算合并配置
    })
    .catch(err => {
      console.error(err);
      ElMessage.error("获取状态数据失败");
    });
};
// 页面加载时获取数据
onMounted(() => {
  getAuditList();
});
</script>

<style lang="scss" scoped>
.status-container {
  padding: 0;

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 500;
    }
  }
}

:deep(.el-card__body) {
  padding: 20px;
}

.el-table {
  border-radius: 4px;
}

.el-table th {
  font-weight: bold;
  color: #333;
  background-color: #f2f4f5;
}

.el-table td {
  color: #666;
}
</style>
