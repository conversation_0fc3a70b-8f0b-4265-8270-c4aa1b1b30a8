<template>
  <div class="toolbar">
    <Perms :value="['workflow:r']">
      <el-tooltip
        v-if="playbookStore.isPlaybookStarting == false"
        :hide-after="0"
        content="试运行"
        effect="dark"
        placement="top"
      >
        <div class="toolbar-item playbook-start">
          <el-button
            :disabled="hasPermission('p')"
            link
            @click.stop="$_start()"
          >
            <Start />
          </el-button>
        </div>
      </el-tooltip>
      <el-tooltip
        v-else
        :hide-after="0"
        content="终止"
        effect="dark"
        placement="top"
      >
        <div class="toolbar-item playbook-end">
          <el-button :disabled="hasPermission('p')" link @click.stop="$_end()">
            <End />
          </el-button>
        </div>
      </el-tooltip>
    </Perms>
    <el-tooltip :hide-after="0" content="放大" effect="dark" placement="top">
      <div class="toolbar-item">
        <el-button :disabled="hasPermission('u')" link @click="$_zoomIn()">
          <zoom-in />
        </el-button>
      </div>
    </el-tooltip>
    <el-tooltip :hide-after="0" content="缩小" effect="dark" placement="top">
      <div class="toolbar-item">
        <el-button :disabled="hasPermission('u')" link @click="$_zoomOut()">
          <zoom-out />
        </el-button>
      </div>
    </el-tooltip>
    <el-tooltip
      :hide-after="0"
      content="自适应视图"
      effect="dark"
      placement="top"
    >
      <div class="toolbar-item">
        <el-button :disabled="hasPermission('u')" link @click="$_zoomReset()">
          <zoom-reset />
        </el-button>
      </div>
    </el-tooltip>
    <el-tooltip :hide-after="0" content="撤回" effect="dark" placement="top">
      <div :class="{ disabled: !undoAble }" class="toolbar-item">
        <el-button :disabled="hasPermission('u')" link @click="$_undo()">
          <step-back />
        </el-button>
      </div>
    </el-tooltip>
    <el-tooltip :hide-after="0" content="重做" effect="dark" placement="top">
      <div :class="{ disabled: !redoAble }" class="toolbar-item">
        <el-button :disabled="hasPermission('u')" link @click="$_redo()">
          <step-foward />
        </el-button>
      </div>
    </el-tooltip>
  </div>
</template>

<script lang="ts" setup>
import Start from "@/views/playbook/playbookEdit/icon/Start.vue";
import End from "@/views/playbook/playbookEdit/icon/End.vue";
import ZoomIn from "@/views/playbook/playbookEdit/icon/ZoomIn.vue";
import ZoomOut from "@/views/playbook/playbookEdit/icon/ZoomOut.vue";
import ZoomReset from "@/views/playbook/playbookEdit/icon/ZoomReset.vue";
import StepBack from "@/views/playbook/playbookEdit/icon/StepBack.vue";
import StepFoward from "@/views/playbook/playbookEdit/icon/StepFoward.vue";
import LogicFlow from "@logicflow/core";
import { onUnmounted, ref } from "vue";
import usePlaybookStore from "@/store/modules/playbook";
import { apiGetPlaybookInput } from "@/api/playbook";
import { useDetail } from "../../../hooks";
import { ElMessage } from "element-plus";
// 引入lodash的debounce函数
import debounce from "lodash/debounce";

const props = defineProps({
  lf: {
    type: LogicFlow
  }
});

const emit = defineEmits(["openWebSocket"]);

const undoAble = ref(true);
const redoAble = ref(true);
const graphData = ref();
const flow_json = ref();
const playbookStore = usePlaybookStore();
const playbookId = ref();
const playbookVersionId = ref();
const playbookPermissions = JSON.parse(
  localStorage.getItem("playbookPermissions")
);

// 获取剧本ID、剧本版本ID和剧本状态
const { getParameter } = useDetail();
playbookId.value = getParameter.flow_id;
playbookVersionId.value = getParameter.version_id;

//组件销毁时，将isPlaybookStarting设置为false，并切断SSE连接
onUnmounted(() => {
  playbookStore.isPlaybookStarting = false;
  playbookStore.abortConnection();
});

// 执行剧本
const $_start = debounce(async () => {
  //获取流程绘图数据
  graphData.value = props.lf.getGraphData();
  // 判断流程图节点的isOld是否为false，如果存在有isOld为flase的节点，则禁止试运行
  if (graphData.value.nodes.find(item => item.properties.isOld === false)) {
    ElMessage.error("流程图有还未配置的节点，请配置完成后再尝试运行");
    return;
  }
  // //判断是否有人工节点，有的话，直接return，并提示用户试运行不允许包含人工节点
  // if (graphData.value.nodes.find(item => item.type === "approval-node")) {
  //   ElMessage.error("试运行不允许包含人工节点");
  //   return;
  // }
  //获取剧本的输入参数
  let res: any = await apiGetPlaybookInput({
    flow_id: playbookVersionId.value
  });
  // 2. 处理API响应
  if (res.code === 0) {
    // 3. 重构数据结构（核心修改逻辑）
    playbookStore.startFlow_input = res.data.map(item => {
      const key = Object.keys(item)[0]; // 提取动态键名（如'abc'）
      return {
        key: key,
        type: item[key].input.type, // 提取输入类型
        description: item[key].input.description // 提取描述文本
      };
    });
  }
  // const nodesToUpdate = graphData.value.nodes.filter(
  //   item => item.type === "action-node"
  // );
  // nodesToUpdate.forEach(item => {
  //   props.lf.getNodeModelById(item.id).updateField();
  // });
  // //收集用户输入，对用户输入进行判断，如果是开始节点的output，则保存对应的input_name到playbookStore中
  // // 获取startNodeId
  // const startNodeId = playbookStore.startNodeId;
  // // 构建正则表达式，匹配 `${startNodeId}.output` 后跟任意数值的模式
  // const targetPattern = new RegExp(
  //   `\\$\\{${startNodeId}\\.(output|input)\\.[a-zA-Z0-9_]+\\}`
  // );
  // playbookStore.flow_inputName = [];
  // playbookStore.flow_input = {};
  // // 遍历nodes数组
  // graphData.value.nodes.forEach(node => {
  //   const data = node.properties;
  //   // 检查data中是否存在input键
  //   if (data.input) {
  //     // 遍历input键值对
  //     for (const key in data.input) {
  //       if (data.input.hasOwnProperty(key)) {
  //         const value = data.input[key];
  //         console.log("value:", value);
  //         // 检查值是否为目标值
  //         if (targetPattern.test(value)) {
  //           console.log("符合规格的value:", value);
  //           // 如果input_name中存在相同的键名
  //           if (data.input_name.hasOwnProperty(key)) {
  //             // 将input_name的键值对以对象形式保存到Pinia仓库
  //             playbookStore.addFlowInputName({ [key]: data.input_name[key] });
  //           }
  //         }
  //       }
  //     }
  //   }
  // });
  // console.log("flow_inputName去重前:", playbookStore.flow_inputName);
  // //对收集到的用户输入进行去重
  // playbookStore.flow_inputName = Array.from(
  //   new Set(playbookStore.flow_inputName.map(item => JSON.stringify(item)))
  // ).map(item => JSON.parse(item));
  // console.log("flow_inputName去重后:", playbookStore.flow_inputName);
  if (playbookStore.startFlow_input.length === 0) {
    //如果用户没有输入，则直接运行webSocket通信
    emit("openWebSocket");
    //剧本正在执行中
    ElMessage.success("开始执行");
    playbookStore.isPlaybookStarting = true;
    //启用清空剧本执行结果按钮
    playbookStore.isPlaybookStart = true;
  } else {
    //如果用户有输入，显示用户输入dialog
    playbookStore.isShowFlowInputDialog = true;
    //并清空flowInput
    playbookStore.flowInput = {};
  }
}, 300);

// 终止剧本
const $_end = debounce(() => {
  playbookStore.isPlaybookStarting = false;
  playbookStore.abortConnection();
  ElMessage.error("终止执行");
}, 300);

//放大
const $_zoomIn = () => {
  props.lf.zoom(true);
};

//缩小
const $_zoomOut = () => {
  props.lf.zoom(false);
};

//调整视图
const $_zoomReset = () => {
  props.lf.resetZoom();
};

//上一步
const $_undo = () => {
  props.lf.undo();
};

//下一步
const $_redo = () => {
  props.lf.redo();
};

//是否有按钮权限
const hasPermission = (permission: string) => {
  return !playbookPermissions[playbookId.value].includes(permission);
};

//获取禁用时按钮颜色
const getDisabledColor = () => {
  return playbookPermissions[playbookId.value].includes("u")
    ? "#0d1733"
    : "#ccc";
};

defineExpose({
  $_start
});
</script>

<style lang="scss" scoped>
.toolbar {
  display: flex;
  border-radius: 50px;
  background: #ccc;
  margin-bottom: 20px;
  box-shadow: var(--el-box-shadow-light);

  .toolbar-item {
    padding: 5px 10px;
  }

  .playbook-start {
    background: #00b23c;
    border-radius: 50px 0px 0px 50px;
  }

  .playbook-end {
    background: #ff0000;
    border-radius: 50px 0px 0px 50px;
  }
}
</style>
