import { defineStore } from "pinia";
import { refreshTokenApi } from "@/api/user";
import { setToken } from "@/utils/auth";
import { useUserStoreHook } from "@/store/modules/user";
import { apiApproveTodolist } from "@/api/approve";
import { noticesData } from "@/layout/components/lay-notice/data";

/**
 * Token 刷新管理 Store
 * 用于管理 token 的自动刷新机制，包括定时刷新和状态维护
 */
export const useRefreshTokenStore = defineStore({
  id: "refresh-token",
  state: () => ({
    // 定时器引用，用于清除定时器
    timer: null as NodeJS.Timer | null,
    // 上次刷新token的时间戳，从localStorage中获取以保持持久化
    lastRefreshTime: localStorage.getItem("lastTokenRefreshTime")
      ? parseInt(localStorage.getItem("lastTokenRefreshTime")!)
      : 0,
    // 定时器运行状态标志
    isRunning: false,
    // 刷新间隔时间：2.5分钟（单位：毫秒）
    REFRESH_INTERVAL: 2.5 * 60 * 1000,
    // 日志打印间隔（单位：秒）
    LOG_INTERVAL: 10
  }),

  actions: {
    /**
     * 刷新 Token
     * 调用 API 获取新的 token，并更新存储
     * 成功时重置定时器，失败时退出登录
     */
    async refreshToken() {
      try {
        const response = await refreshTokenApi({});
        if (!response || !response.data) {
          throw new Error("刷新token响应数据无效");
        }
        const { data } = response;
        // 设置新的 token 信息
        setToken({
          accessToken: data["aioe-auth"],
          refreshToken: data["aioe-auth"],
          expires: new Date(Date.now() + 24 * 60 * 60 * 1000) // 设置24小时过期
        });
        // 更新最后刷新时间
        this.updateLastRefreshTime();
        // 输出当前刷新状态
        // console.log("Token刷新成功，下次刷新将在2.5分钟后");
      } catch (error: unknown) {
        console.error("刷新token失败:", error);
        this.stopRefreshTimer();
        setTimeout(() => {
          useUserStoreHook().logOut();
        }, 700);
      }
    },

    /**
     * 获取待办列表
     */
    async approveTodolist() {
      const res: any = await apiApproveTodolist({});
      console.log(res);
      // 更新待办数据（遵循类型安全规范）
      if (res.code === 0) {
        // 数据转换（遵循接口类型定义规范）
        const transformedData = res.data;

        // 查找待办tab索引（遵循数组操作规范）
        const todoIndex = noticesData.findIndex(tab => tab.key === "2");

        // 更新数据（遵循响应式数据管理规范）
        if (todoIndex >= 0) {
          noticesData[todoIndex].list = transformedData;
          console.log("成功更新待办数据:", transformedData);
        } else {
          throw new Error("未找到待办tab项");
        }
      } else {
        throw new Error("审批数据请求失败");
      }
    },
    /**
     * 更新最后刷新时间
     */
    updateLastRefreshTime() {
      this.lastRefreshTime = Date.now();
      localStorage.setItem(
        "lastTokenRefreshTime",
        this.lastRefreshTime.toString()
      );
    },

    /**
     * 重置定时器
     */
    resetTimer() {
      this.stopRefreshTimer();
      // 设置新的定时器，包含定期日志输出
      this.timer = setInterval(() => {
        const remainingTime = this.getTimeUntilNextRefresh();
        const remainingSeconds = Math.floor(remainingTime / 1000);
        // 每 LOG_INTERVAL 秒输出一次日志
        if (remainingSeconds % this.LOG_INTERVAL === 0) {
          // console.log(`距离下次刷新token还剩：${remainingSeconds}秒`);
        }
        // 时间到了就刷新token
        if (remainingSeconds <= 0) {
          this.refreshToken();
        }
      }, 1000);
      this.isRunning = true;
      console.log("token定时器已启动");
    },

    /**
     * 启动刷新定时器
     */
    startRefreshTimer() {
      if (this.isRunning) {
        console.log("定时器已在运行，跳过启动");
        return;
      }
      this.resetTimer();
      this.refreshToken(); // 立即执行一次刷新
      this.approveTodolist(); // 获取待办列表
    },

    /**
     * 停止刷新定时器
     */
    stopRefreshTimer() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
        this.isRunning = false;
        console.log("停止刷新定时器");
      }
    },

    /**
     * 获取距离下次刷新的剩余时间
     */
    getTimeUntilNextRefresh(): number {
      if (!this.lastRefreshTime) return 0;
      const now = Date.now();
      const nextRefreshTime = this.lastRefreshTime + this.REFRESH_INTERVAL;
      return Math.max(0, nextRefreshTime - now);
    }
  }
});
