<script lang="ts" setup>
import { ref } from "vue";
import { message } from "@/utils/message";
import { createUser } from "@/api/userList";
import { useColumns } from "../columns";
// import { useRoleableStore } from "@/store/modules/roletable";
import { QuestionFilled } from "@element-plus/icons-vue";
import { getRoleNameList } from "@/api/role";

const { fetchUserData } = useColumns();
// const roleStore = useRoleableStore();
const visible = ref(false);
const formRef = ref();
const roleOptions = ref([]);
const loading = ref(false);
const filteredRoleOptions = ref([]);
const emit = defineEmits(["refresh"]);

// 表单数据
const form = ref({
  username: "",
  display_name: "",
  phone: "",
  email: "",
  password: "",
  disabled: false,
  mfa_enable: false,
  role_names: [],
  permit_ip: ""
});

const ipShow = ref(false);

// 添加表单验证规则
const rules = {
  username: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  password: [{ required: true, message: "请输入密码", trigger: "blur" }],
  display_name: [{ required: true, message: "请输入姓名", trigger: "blur" }]
};

// 获取角色列表
const fetchRoleList = async () => {
  try {
    const res = (await getRoleNameList({})) as any;
    if (res.code === 0 && res.data && Array.isArray(res.data.roles)) {
      const roles = res.data.roles.map(role => ({
        label: role.name,
        value: role.name
      }));
      roleOptions.value = roles;
      filteredRoleOptions.value = roles;
    } else {
      console.error("获取角色列表失败: 数据格式错误", res);
    }
  } catch (err) {
    console.error("获取角色列表失败:", err);
  }
};

// 打开弹窗
const openDialog = async () => {
  // 确保角色列表已加载
  if (roleOptions.value.length === 0) {
    await fetchRoleList();
  }
  // 重置表单
  form.value = {
    username: "",
    display_name: "",
    phone: "",
    email: "",
    password: "",
    disabled: false,
    mfa_enable: false,
    role_names: [],
    permit_ip: ""
  };
  ipShow.value = false;
  visible.value = true;
};

// 过滤掉空值的函数
const filterEmptyValues = (obj: Record<string, any>) => {
  const result: Record<string, any> = {};
  Object.entries(obj).forEach(([key, value]) => {
    if (value !== "" && value !== null && value !== undefined) {
      result[key] = value;
    }
  });
  return result;
};

// 提交表单
const handleSubmit = () => {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      const params = filterEmptyValues({
        ...form.value
      });
      if (!ipShow.value) {
        params.permit_ip = "";
      }
      console.log(params);
      createUser(params).then((res: any) => {
        if (res.code === 0) {
          message("创建成功", { type: "success" });
          visible.value = false;
          emit("refresh"); // 触发刷新事件
        } else {
          message(res.message || "创建失败", { type: "error" });
        }
      });
    }
  });
};

// 关闭弹窗
const handleClose = () => {
  visible.value = false;
};

// 过滤角色
const filterRoles = (query: string) => {
  if (query) {
    filteredRoleOptions.value = roleOptions.value.filter(option =>
      option.label.toLowerCase().includes(query.toLowerCase())
    );
  } else {
    filteredRoleOptions.value = roleOptions.value;
  }
};

defineExpose({
  openDialog
});
</script>

<template>
  <el-dialog
    v-model="visible"
    :close-on-click-modal="false"
    title="新建用户"
    width="900px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-position="right"
      label-width="120px"
    >
      <el-form-item label="用户名" prop="username" required>
        <el-input v-model="form.username"  />
      </el-form-item>
      <el-form-item label="姓名" prop="display_name" required>
        <el-input v-model="form.display_name"  />
      </el-form-item>
      <el-form-item label="密码" prop="password" required>
        <el-input
          v-model="form.password"

          show-password
          type="password"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input v-model="form.phone"  />
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="form.email"  />
      </el-form-item>
      <el-form-item label="角色" prop="role_names">
        <el-select
          v-model="form.role_names"
          :loading="loading"
          filterable
          multiple
          placeholder="请选择角色"
          style="width: 100%"
        >
          <el-option
            v-for="item in filteredRoleOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="账号状态" prop="disabled">
        <el-switch
          v-model="form.disabled"
          :active-value="false"
          :inactive-value="true"
          active-text="启用"
          inactive-text="禁用"
          inline-prompt
        />
      </el-form-item>
      <el-form-item label="多因素认证" prop="mfa_enable">
        <el-switch
          v-model="form.mfa_enable"
          :active-value="true"
          :inactive-value="false"
          active-text="开启"
          inactive-text="关闭"
          inline-prompt
        />
      </el-form-item>
      <el-form-item>
        <template #label>
          <span class="flex items-center">
            IP白名单:
            <el-tooltip
              :hide-after="0"
              content="只有白名单内的IP地址能够登录该账号"
              placement="top"
            >
              <el-icon class="ml-2 cursor-help"><QuestionFilled /></el-icon>
            </el-tooltip>
          </span>
        </template>
        <el-input
          v-model="form.permit_ip"
          autosize

          type="textarea"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>

<style scoped>
.login-form span {
  font-size: 15px;
  color: #999;
  margin-bottom: 15px;
  display: block;
}
</style>
