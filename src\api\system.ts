import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

export const systemUpload = (formData: FormData) => {
  return http.post(baseUrlApi("system/upload"), undefined, {
    headers: {
      "Content-Type": "multipart/form-data"
    },
    data: formData
  });
};

// 获取系统定制配置图片
export const getPicture = async (data: string) => {
  const resp = await http.get<ArrayBuffer, any>(
    baseUrlApi(`system/picture?type=${data}`),
    {
      responseType: "arraybuffer"
    }
  );
  const isRespEmpty =
    !resp || (resp instanceof ArrayBuffer && resp.byteLength === 0);

  if (isRespEmpty) {
    return null;
  } else {
    const blob = new Blob([resp]);
    return URL.createObjectURL(blob);
  }
};

// 获取全部系统状况
export const statusAll = (data: any) => {
  return http.post(baseUrlApi("system/status/all"), { data });
};
//查询审计日志
export const systemLog = (data: any) => {
  return http.post(baseUrlApi("system/audit"), { data });
};
// 获取oauth2配置
export const getOauthConfig = (data: any) => {
  return http.post(baseUrlApi("system/oauth/get"), { data });
};
// 保存oauth2配置
export const saveOauthConfig = (data: any) => {
  return http.post(baseUrlApi("system/oauth/update"), { data });
};

// 获取oauth2是否开启
export const getOauth2Status = (data: any) => {
  return http.post(baseUrlApi("system/oauth/status"), { data });
};
