<script lang="ts" setup>
import { ref } from "vue";
import { FieldValues, PlusColumn, PlusDialogForm } from "plus-pro-components";
import { message } from "@/utils/message";
import { editUser } from "@/api/userList";
import { useColumns } from "../columns";

const { fetchUserData } = useColumns();

const passwordColumns: PlusColumn[] = [
  {
    label: "新密码",
    prop: "password",
    valueType: "input",
    fieldProps: {
      placeholder: "请输入密码",
      showPassword: true,
      type: "password"
    }
  },
  {
    label: "确认密码",
    prop: "confirmPassword",
    valueType: "input",
    fieldProps: {
      placeholder: "请再次输入密码",
      showPassword: true,
      type: "password"
    }
  }
];

const visible = ref(false);
const values = ref<FieldValues>({
  password: "",
  confirmPassword: ""
});

// 打开弹窗
const openDialog = (row: any) => {
  values.value = {
    password: "",
    confirmPassword: "",
    username: row.username
  };
  visible.value = true;
};

// 提交表单
const handleFormSubmit = () => {
  if (values.value.password !== values.value.confirmPassword) {
    message("两次输入的密码不一致", { type: "error" });
    return;
  }
  editUser({
    username: values.value.username,
    password: values.value.password
  }).then((res: any) => {
    if (res.code === 0) {
      console.log(res);
      message("修改成功", { type: "success" });
      visible.value = false;
      fetchUserData(1, 15, "");
    } else {
      message(res.message, { type: "error" });
    }
  });
};
defineExpose({
  openDialog
});
</script>

<template>
  <PlusDialogForm
    v-model="values"
    v-model:visible="visible"
    :form="{ columns: passwordColumns }"
    cancel-text="取消"
    confirm-text="保存"
    title="修改密码"
    @confirm="handleFormSubmit"
  />
</template>
