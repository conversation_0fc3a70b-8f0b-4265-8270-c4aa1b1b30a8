<template>
  <div
    ref="menuRef"
    class="custom-context-menu"
    @mouseenter="handleMouseEnter()"
    @mouseleave="handleMouseLeave()"
  >
    <div class="search">
      <el-input
        v-model="searchKeyword"
        :prefix-icon="Search"
        placeholder="搜索工具"
        @input="getToolList({ key: searchKeyword })"
      />
    </div>
    <div class="context-logic">
      <span class="context-logic-title">
        <!-- <svg
          t="1747879009527"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="2759"
          width="20"
          height="20"
        >
          <path
            d="M802.24 0a12.8 12.8 0 0 1 7.424 2.368l208.96 149.312a12.8 12.8 0 0 1 0 20.8l-208.96 149.248a12.8 12.8 0 0 1-20.224-10.368v-102.4H530.304c-7.424 0-15.488 8.128-17.152 20.544l-0.32 4.864v506.368c0 13.44 7.168 23.04 14.72 25.024l2.752 0.384h259.072V664.32a12.8 12.8 0 0 1 20.224-10.432l208.96 149.312a12.8 12.8 0 0 1 0 20.8l-208.96 149.248a12.8 12.8 0 0 1-20.224-10.368v-103.808H530.304c-59.712 0-106.496-49.856-110.08-110.72l-0.256-7.552-0.064-206.72H270.72a139.328 139.328 0 1 1 0-92.928h149.248v-206.72c0-61.568 44.416-113.92 103.04-118.016l7.36-0.256h259.008L789.44 12.8c0-7.04 5.76-12.8 12.8-12.8z"
            fill="#2c2c2c"
            p-id="2760"
          />
        </svg> -->
        <span class="text">逻辑处理器</span>
      </span>
      <el-row :gutter="20" class="context-logic-row">
        <el-col
          v-for="(item, index) in props.items"
          :key="index"
          :span="12"
          style="font-size: 13px; margin: 5px 0px"
          @click="addBusinessLogicNode(item)"
        >
          <div class="context-logic-main">
            <img :src="item.icon" alt="Icon" />
            <div class="text">
              {{ item.label }}
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="context-tool">
      <span class="context-tool-title">
        <!-- <svg
          t="1747879574788"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="5194"
          width="20"
          height="20"
        >
          <path
            d="M916.422378 713.465454l-200.653973-200.639646c12.921287-36.074613 19.461237-73.782424 19.461237-112.426559 0-184.603415-150.179394-334.799182-334.799182-334.799182-45.959751 0-90.675162 9.263991-132.8671 27.525913l-19.960611 8.640798c-0.031722 0.031722 0.124843 0.047072 0.062422 0.077771-1.898233 0.826831-3.674693 2.025123-5.231141 3.581572-6.602372 6.615675-6.602372 17.374716 0.032746 24.007787 0.093121 0.094144-0.094144 0.140193-0.032746 0.233314l187.452299 187.453323L317.133336 429.856142 133.447826 246.156306c-0.280386 0.031722-0.560772 0.047072-0.934279-0.326435-7.78429-7.78429-20.395516-7.78429-28.181853 0-2.116197 2.133593-3.674693 4.609994-4.63967 7.256264-0.093121 0.248663-0.217964 0.124843-0.341784 0.140193l-6.197142 14.323217c-18.24555 42.145889-27.52489 86.860277-27.52489 132.851751 0 184.588065 150.180418 334.799182 334.799182 334.799182 38.611389 0 76.3192-6.538927 112.438838-19.461237l200.654996 200.626343c27.092032 27.088962 63.119573 42.035372 101.450577 42.035372 0 0 0 0 0.032746 0 38.299281 0 74.358545-14.94641 101.449553-42.035372C972.347085 860.439854 972.347085 769.422907 916.422378 713.465454zM871.365182 871.275643c-15.07023 15.071254-35.093263 23.352871-56.360635 23.352871l-0.032746 0c-21.298072 0-41.320081-8.28264-56.359612-23.352871l-230.764757-230.735082-19.896142 8.594749c-34.158984 14.789844-70.342068 22.292726-107.521852 22.292726-149.432381 0-271.027763-121.562637-271.027763-271.02674 0-20.769023 2.335185-41.212634 6.974855-61.141522l168.582533 168.614256c7.068999 5.637394 16.782222 5.714142 23.976064 0.35918l177.239703-177.23868c0.062422-0.062422-0.063445-0.141216 0-0.202615 7.223518-7.224542 7.441483-18.497283 1.24434-26.328645L339.272565 136.330992c19.960611-4.623297 40.386826-6.958482 61.155848-6.958482 149.432381 0 271.028786 121.581056 271.028786 271.027763 0 37.226856-7.505951 73.392544-22.294772 107.491153l-8.594749 19.895119 230.765781 230.765781C902.410246 789.631158 902.410246 840.197834 871.365182 871.275643z"
            fill="#2c2c2c"
            p-id="5195"
          />
        </svg> -->
        <span class="text">应用列表</span>
      </span>
      <el-row :gutter="20" class="context-tool-row">
        <el-col
          v-for="item in allToolList"
          :key="item.id"
          :span="12"
          style="font-size: 13px; margin: 5px 0px"
          @click="addActionToolNode(item)"
        >
          <div class="context-tool-col">
            <el-image
              :src="`data:image/png;base64,${item.logo}`"
              fit="contain"
              style="width: 25px; height: 25px"
            />
            <div class="text">{{ item.description }}</div>
          </div>
        </el-col>
      </el-row>
    </div>
    <el-config-provider :locale="zhCn">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        size="small"
        :page-sizes="[20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-config-provider>
  </div>
</template>

<script lang="ts" setup>
import usePlaybookStore from "@/store/modules/playbook";
import LogicFlow from "@logicflow/core";
import {
  ElCol,
  ElImage,
  ElInput,
  ElRow,
  ElPagination,
  ElConfigProvider
} from "element-plus";
// 引入中文包
import zhCn from "element-plus/es/locale/lang/zh-cn"; // 或从 'element-plus/lib/locale/lang/zh-cn' 引入
import { Search } from "@element-plus/icons-vue";
import { onMounted, ref } from "vue";
import { apiGetToolList } from "@/api/playbook";
import { number } from "echarts";

//匹配任意类型，偷懒写法，正确的最好还是仔细写一下具体属性和类型有哪些
interface propsType {
  [key: string]: any;
}

const props = defineProps<{
  lf: LogicFlow;
  items: propsType;
  activeData: propsType;
  menuDOM: HTMLDivElement;
  addNode: Function;
  hideContextMenu: Function;
}>();

// const props = defineProps({
//   lf: {
//     type: LogicFlow,
//     required: true
//   },
//   items: {
//     type: Array,
//     required: true
//   },
//   activeData: {
//     type: Object
//   },
//   menuDOM: {
//     type: HTMLDivElement
//   },
//   addNode: {
//     type: Function
//   },
//   hideContextMenu: {
//     type: Function
//   }
// });

const currentPage = ref(0);
const pageSize = ref(20);
const searchKeyword = ref();
const playbookStore = usePlaybookStore();
const allToolList = ref();
const total = ref<number>(0);

onMounted(() => {
  getToolList({});
});
//业务逻辑节点
const addBusinessLogicNode = item => {
  const node = props.activeData;
  //判断是否有callback，如有，则是删除，否则创建用户点击的对应类型的节点
  if (item.type === "loop-body") {
    const nodeModel = props.lf.getNodeModelById(node.id);
    const newNode = props.lf.graphModel.addNode({
      type: item.type,
      x: node.x + 300,
      y: node.y - nodeModel.height / 2 + 50,
      resizable: true,
      collapsedWidth: 200,
      collapsedHeight: 100,
      properties: {
        resizable: true,
        transformWithContainer: true, // 启用联动变换
        isRestrict: true,
        autoResize: true,
        collapsedWidth: 200,
        collapsedHeight: 100,
        radius: 5
      }
    });
    (props.lf.extension.contextPad as any).hideContextMenu();
    //恢复保存按钮的使用
    playbookStore.isFlowChanged = true;
  } else {
    //如果是新增节点的话，1.确定旧节点的位置2.根据旧节点的位置生成新节点3.连接新旧节点
    const nodeModel = props.lf.getNodeModelById(node.id);
    const newNode = props.lf.graphModel.addNode({
      type: item.type,
      x: node.x + 300,
      y: node.y - nodeModel.height / 2 + 50,
      resizable: false,
      properties: {
        isOld: false
      }
    });
    (props.lf.extension.contextPad as any).hideContextMenu();
    let startPoint;
    let endPoint;
    if (node.type == "judge-node") {
      startPoint = {
        x: node.x + nodeModel.width / 2,
        y: node.y - nodeModel.height / 2 + 45
      };
    } else {
      startPoint = {
        x: node.x + nodeModel.width / 2,
        y: node.y - nodeModel.height / 2 + 50
      };
    }

    endPoint = {
      x: newNode.x - newNode.width / 2,
      y: newNode.y
    };
    //连接新旧节点
    props.lf.graphModel.addEdge({
      type: "vue-edge",
      sourceNodeId: node.id,
      targetNodeId: newNode.id,
      startPoint,
      endPoint
    });
    //恢复保存按钮的使用
    playbookStore.isFlowChanged = true;
  }
};

//工具节点
const addActionToolNode = item => {
  const node = props.activeData;
  //新增节点的话，1.确定旧节点的位置2.根据旧节点的位置生成新节点3.连接新旧节点
  const nodeModel = props.lf.getNodeModelById(node.id);
  const newNode = props.lf.graphModel.addNode({
    type: "action-node",
    x: node.x + 300,
    y: node.y - nodeModel.height / 2 + 50,
    resizable: false,
    properties: {
      resizable: false,
      main: item.main,
      name: item.description,
      app_name: item.name,
      node_name: item.description,
      retry_times: 0,
      timeout: 300,
      scale: 1,
      isOld: false
    }
  });
  //隐藏快捷菜单
  //来自ts的神秘判定，不加any找不到方法
  (props.lf.extension.contextPad as any).hideContextMenu();
  //确定新旧节点锚点位置
  let startPoint;
  let endPoint;
  if (node.type == "judge-node") {
    startPoint = {
      x: node.x + nodeModel.width / 2,
      y: node.y - nodeModel.height / 2 + 45
    };
  } else {
    startPoint = {
      x: node.x + nodeModel.width / 2,
      y: node.y - nodeModel.height / 2 + 50
    };
  }
  endPoint = {
    x: newNode.x - newNode.width / 2,
    y: newNode.y
  };
  //连接新旧节点
  props.lf.graphModel.addEdge({
    type: "vue-edge",
    sourceNodeId: node.id,
    targetNodeId: newNode.id,
    startPoint,
    endPoint
  });
  //恢复保存按钮的使用
  playbookStore.isFlowChanged = true;
};

//获取全部的工具
const getToolList = async ({ page = 1, size = 20, key = "" }) => {
  currentPage.value = page;
  pageSize.value = size;
  searchKeyword.value = key;
  let res: any = await apiGetToolList({
    page: currentPage.value,
    size: pageSize.value,
    key: searchKeyword.value
  });
  allToolList.value = res.data.tools;
  total.value = res.data.total;
};

const handleSizeChange = (val: number) => {
  getToolList({ size: val });
  console.log(`每页 ${val} 条`);
};
const handleCurrentChange = (val: number) => {
  getToolList({ page: val });
  console.log(`当前页: ${val}`);
};

const handleMouseEnter = () => {
  // 鼠标进入时禁止缩放
  props.lf.updateEditConfig({
    stopZoomGraph: true
  });
};

const handleMouseLeave = () => {
  // 鼠标离开时恢复缩放
  props.lf.updateEditConfig({
    stopZoomGraph: false
  });
};
</script>

<style lang="scss" scoped>
.custom-context-menu {
  position: absolute;
  background-color: white;
  border-radius: 10px;
  border: 1px solid #ccc;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  padding: 5px 0;
  z-index: 1000;
  width: 600px;
  max-height: 500px;
  overflow: auto;
  padding: 10px;

  .search {
    padding-bottom: 10px;
  }

  .context-logic {
    padding-bottom: 15px;

    .context-logic-title {
      display: flex;
      align-items: center;
      color: #2029459e;

      .text {
        padding-left: 10px;
        font-size: 15px;
      }
    }

    .context-logic-row {
      padding: 0px 10px;

      .context-logic-main {
        height: 30px;
        cursor: pointer;
        display: flex;
        align-items: center;

        img {
          width: 20px;
          height: 20px;
        }

        .text {
          padding-left: 10px;
        }
      }

      :hover {
        background: #eff0f8;
        border-radius: 10px;
        overflow: hidden;
      }
    }
  }

  .context-tool {
    padding-bottom: 15px;

    .context-tool-title {
      display: flex;
      align-items: center;
      color: #2029459e;

      .text {
        padding-left: 10px;
        font-size: 15px;
      }
    }

    .context-tool-row {
      padding: 0px 10px;

      .context-tool-col {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 5px 0px;

        .text {
          padding-left: 10px;
        }
      }

      :hover {
        background: #eff0f8;
        border-radius: 10px;
        overflow: hidden;
      }
    }
  }
}
</style>
