<template>
  <div>
    <el-dialog v-model="dialogVisible" title="克隆剧本" top="5vh" width="700px">
      <template #default>
        <div>
          <playbookDialogForm v-model:formData="formData" />
        </div>
      </template>
      <template #footer>
        <div>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="clonePlaybook()"> 确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { apiCreatePlaybook, apiGetPlaybookVersionsList } from "@/api/playbook";
import { ElMessage } from "element-plus";
import { ref } from "vue";
import playbookDialogForm from "@/views/playbook/components/playbookDialog/playbookDialogForm.vue";

const emit = defineEmits(["apiGet"]);

const dialogVisible = ref(false);
const playbookVersionsList = ref();
//表单数据
const formData = ref({
  id: "",
  name: "",
  remark: "",
  tags: [],
  scenes: []
});
//打开dialog并获取父组件传递的数据(并在defineExpose中对外暴露该方法)
const openDialog = data => {
  dialogVisible.value = true;
  //深拷贝对象
  formData.value = JSON.parse(JSON.stringify(data));
  formData.value.name += "_副本";
  console.log(formData.value);
};

//确认按钮-克隆剧本(相当于某种意义上的新建剧本)
const clonePlaybook = async () => {
  //根据剧本ID，获取剧本版本列表，并获取最新版本的flow_json
  let res1: any = await apiGetPlaybookVersionsList({
    playbook_id: formData.value.id
  });
  playbookVersionsList.value = res1.data[0];
  console.log(playbookVersionsList.value);
  //新建剧本(但是带有flow_json)
  let res2: any = await apiCreatePlaybook({
    name: formData.value.name,
    remark: formData.value.remark,
    tags: formData.value.tags,
    scenes: formData.value.scenes,
    flow_json: playbookVersionsList.value.flow_json
  });
  console.log(res2);
  if (res2.code == 0) {
    ElMessage({
      message: "克隆成功",
      type: "success"
    });
  } else {
    ElMessage({
      message: "克隆失败",
      type: "warning"
    });
  }
  dialogVisible.value = false;
  //新建剧本后，再次请求剧本列表数据
  emit("apiGet");
};

//对外暴露方法
defineExpose({
  openDialog
});
</script>

<style lang="scss" scoped></style>
