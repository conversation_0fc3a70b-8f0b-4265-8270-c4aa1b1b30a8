<template>
  <div>
    <el-button
      ref="buttonRef"
      v-click-outside="onClickOutside"
      size="large"
      type="primary"
      link
      :icon="Setting"
    />

    <el-popover
      ref="popoverRef"
      :virtual-ref="buttonRef"
      trigger="click"
      virtual-triggering
      placement="bottom-end"
      width="auto"
    >
      <el-cascader
        v-model="value"
        :options="options"
        :props="cascaderProps"
        @change="handleChange"
        @blur="handleBlur"
      />
      <!-- <div v-for="(item, index) in data" :key="index">
        <div
          v-if="item.type === 'start-node'"
          class="node-item"
          @click="copyNodeId(item.id)"
        >
          开始节点
        </div>
        <div class="node-item" @click="copyNodeId(item.id)">
          {{ item.properties.node_name }}
        </div>
      </div> -->
    </el-popover>
  </div>
</template>

<script setup lang="ts">
import { Setting } from "@element-plus/icons-vue";
import { ref, unref, watch, onMounted } from "vue";
import { ClickOutside as vClickOutside } from "element-plus";

const props = defineProps({
  upstreamNode: {
    type: Object
  }
});

const emit = defineEmits(["copyNodeId"]);

const buttonRef = ref();
const popoverRef = ref();
const data = ref();
const value = ref([]);

const cascaderProps = {
  expandTrigger: "hover" as const
};

const options = ref([
  {
    value: "node",
    label: "节点变量",
    children: []
  }
  // {
  //   value: "gobal",
  //   label: "全局变量",
  //   children: [
  //     {
  //       value: "${global.var1}",
  //       label: "全局变量1"
  //     },
  //     {
  //       value: "${global.var2}",
  //       label: "全局变量2"
  //     },
  //     {
  //       value: "${global.var3}",
  //       label: "全局变量3"
  //     }
  //   ]
  // },
]);

const handleChange = (value: any) => {
  console.log(value);
  if (value[0] === "node") {
    let params = `\${${value[1]}.output}`;
    emit("copyNodeId", params);
  } else {
    emit("copyNodeId", value[1]);
  }
};

const handleBlur = () => {
  value.value = [];
};

const onClickOutside = () => {
  value.value = [];
  unref(popoverRef).popperRef?.delayHide?.();
};

watch(
  () => props.upstreamNode,
  newVal => {
    // 空值校验
    if (!newVal || !Array.isArray(newVal)) return;

    // 深拷贝数据避免污染 props
    const processedData = newVal.map(item => {
      const newItem = { ...item }; // 浅拷贝（若嵌套属性需深拷贝）
      if (newItem.type === "start-node") {
        newItem.properties = {
          ...newItem.properties,
          node_name: "开始节点"
        };
      }
      return {
        value: newItem.id,
        label: newItem.properties.node_name
      };
    });

    data.value = processedData; // 单次赋值
    console.log("data:", data.value);
    options.value[0]["children"] = data.value;
  }
);

const copyNodeId = async (id: string) => {
  emit("copyNodeId", id);
};

/**
 * 获取指定节点的所有上游节点（递归追溯）
 * @param {LogicFlow} lf - LogicFlow 实例
 * @param {string|object} node - 节点 ID 或节点对象
 * @param {object} options - 配置项
 *   - full: boolean 是否返回完整节点对象（默认 false，返回 ID）
 *   - includeSelf: boolean 是否包含自身（默认 false）
 *   - dedupe: boolean 是否去重（默认 true）
 * @returns {Array<string|object>} 上游节点 ID 或节点对象列表
 */
// function getUpstreamNodes(
//   lf,
//   node,
//   options = { full: true, includeSelf: false, dedupe: true }
// ) {
//   const { full, includeSelf, dedupe } = options;

//   const graphData = lf.getGraphData();
//   const nodeMap = new Map(graphData.nodes.map(n => [n.id, n]));
//   console.log("nodeMap:", nodeMap);

//   // 构建上游映射：target -> [source, source, ...]
//   const upstreamMap = new Map();
//   graphData.edges.forEach(edge => {
//     const { sourceNodeId, targetNodeId } = edge;
//     if (!upstreamMap.has(targetNodeId)) {
//       upstreamMap.set(targetNodeId, []);
//     }
//     upstreamMap.get(targetNodeId).push(sourceNodeId);
//   });

//   const nodeId = typeof node === "string" ? node : node.id;
//   const visited = new Set();
//   const result = [];

//   function dfs(id) {
//     if (dedupe && visited.has(id)) return;
//     visited.add(id);

//     if (id !== nodeId || includeSelf) {
//       result.push(id);
//     }

//     const parents = upstreamMap.get(id) || [];
//     for (const parentId of parents) {
//       dfs(parentId);
//     }
//   }

//   dfs(nodeId);

//   return full ? result.map(id => nodeMap.get(id)) : result;
// }
</script>

<style lang="scss" scoped>
.node-item {
  cursor: pointer;
}
</style>
