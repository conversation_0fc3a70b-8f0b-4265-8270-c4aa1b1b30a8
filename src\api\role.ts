import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";
// 获取角色列表
export const getRoleList = (data: any) => {
  return http.post(baseUrlApi("role/all"), { data });
};
//查找具体角色信息
export const searchRole = (data: any) => {
  return http.post(baseUrlApi("role/details"), { data });
};
//新建角色
export const createRole = (data: any) => {
  return http.post(baseUrlApi("role/add"), { data });
};
//删除角色
export const deleteRole = (data: any) => {
  return http.post(baseUrlApi("role/delete"), { data });
};
//修改角色
export const updateRole = (data: any) => {
  return http.post(baseUrlApi("role/update"), { data });
};
//查找全部权限
export const allPermission = (data: any) => {
  return http.post(baseUrlApi("role/permission/all"), { data });
};

// 获取角色单纯名称列表
export const getRoleNameList = (data: any) => {
  return http.post(baseUrlApi("user/role-list"), { data });
};
