<template>
  <div>
    <el-dialog v-model="isShowFlowInputDialog" title="请确认用户输入">
      <el-form>
        <el-form-item v-for="(item, index) in startFlow_input" :key="index">
          <div>{{ item.description }}</div>
          <el-input v-model="flowInput[item.key]" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="isShowFlowInputDialog = false">取消</el-button>
          <el-button type="primary" @click="saveFlowInput()">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import usePlaybookStore from "@/store/modules/playbook";
import { computed, ref } from "vue";

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(["update:modelValue", "nodeEditConfirm"]);

const playbookStore = usePlaybookStore();
const isShowFlowInputDialog = computed({
  get: () => props.modelValue,
  set: val => emit("update:modelValue", val)
});
const startFlow_input = ref();
const flowInput = ref({});

const openPlaybookFlowInputDialog = (data: any) => {
  startFlow_input.value = data;
};

const saveFlowInput = () => {
  emit("nodeEditConfirm", flowInput.value);
};

defineExpose({
  openPlaybookFlowInputDialog
});
</script>

<style lang="scss" scoped></style>
