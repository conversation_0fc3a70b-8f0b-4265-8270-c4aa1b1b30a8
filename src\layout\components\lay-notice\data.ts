export interface ListItem {
  title: string;
  content: string;
  expire_time: string;
  created_at: string;
  approve_type: number;
}

export interface TabItem {
  key: string;
  name: string;
  list: ListItem[];
  emptyText: string;
}

export const noticesData: TabItem[] = [
  {
    key: "1",
    name: "消息",
    list: [
      // {
      //   avatar: "https://xiaoxian521.github.io/hyperlink/svg/smile1.svg",
      //   title: "小铭 评论了你",
      //   description: "诚在于心，信在于行，诚信在于心行合一。",
      //   datetime: "今天",
      //   type: "2"
      // }
    ],
    emptyText: "暂无消息"
  },
  {
    key: "2",
    name: "待办",
    list: [
      // {
      //   avatar: "",
      //   title: "第三方紧急代码变更",
      //   description:
      //     "小林提交于 2024-05-10，需在 2024-05-11 前完成代码变更任务",
      //   datetime: "",
      //   extra: "马上到期",
      //   status: "danger",
      //   type: "3"
      // },
      // {
      //   avatar: "",
      //   title: "版本发布",
      //   description: "指派小铭于 2024-06-18 前完成更新并发布",
      //   datetime: "",
      //   extra: "已耗时 8 天",
      //   status: "warning",
      //   type: "3"
      // },
      // {
      //   avatar: "",
      //   title: "新功能开发",
      //   description: "开发多租户管理",
      //   datetime: "",
      //   extra: "进行中",
      //   type: "3"
      // }
    ],
    emptyText: "暂无待办"
  }
];
