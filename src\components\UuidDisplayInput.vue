<template>
  <div class="uuid-display-container">
    <!-- 编辑模式 -->
    <el-input
      v-if="isEditing"
      ref="inputRef"
      v-model="inputUuid"
      type="textarea"
      autofocus
      autosize
      class="uuid-edit-input"
      style="width: 100%"
      @blur="handleBlur"
      @keydown.tab.prevent
    />

    <!-- 展示模式 -->
    <div
      v-else
      class="node-display"
      @click="enterEditMode"
      @keydown.tab="handleTabKey"
    >
      <span v-if="displayedValue == ''">
        <el-input />
      </span>
      <div v-else class="node-display-text">
        <span
          v-for="(part, index) in displayedValue.split(/(<[^>]+>)/g)"
          :key="index"
          :class="{ 'shadowed-text': isParamText(part) }"
          style="white-space: pre-wrap"
        >
          {{ part }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { ElInput } from "element-plus";
import usePlaybookStore from "@/store/modules/playbook";

// 获取流程图数据
const playbookStore = usePlaybookStore();

// 定义props
const props = defineProps({
  modelValue: {
    type: String,
    default: ""
  },
  readonly: {
    type: Boolean,
    default: false
  },
  selectedTool: {
    type: Object,
    default: null
  }
});
// 定义emits
const emit = defineEmits<{
  (e: "update:modelValue", value: string): void;
  (e: "select-node", value: string): void;
}>();

// 当前选中的UUID
const inputUuid = ref(props.modelValue);

// 添加对父组件数据的监听
watch(
  () => props.modelValue,
  newValue => {
    inputUuid.value = newValue;
  }
);

// 编辑状态
const isEditing = ref(false);

// 输入框引用
const inputRef = ref();

// 定义节点类型
interface NodeType {
  id: string;
  properties: {
    name: string;
    [key: string]: any;
  };
  [key: string]: any;
}

// 处理 Tab 键按下事件
const handleTabKey = event => {
  event.preventDefault(); // 阻止默认行为
  isEditing.value = true; // 切换到编辑模式

  // 焦点切换到编辑模式的输入框
  if (inputRef.value) {
    setTimeout(() => {
      inputRef.value.focus();
    }, 0);
  }
};

function getNodeTitle(text: string): string {
  const startNodeId = playbookStore.startNodeId;
  const flowData = playbookStore.flow_json;
  let result = "";

  // 1. 先处理节点变量（原有逻辑）
  const uuidMatches = text.matchAll(
    /\$\{([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})\.(output|input)(\.[^}]*)?\}/gi
  );
  let currentIndex = 0;
  for (const match of uuidMatches) {
    const matchIndex = match.index || 0;
    const matchText = match[0];
    const cleanUuid = match[1];
    const paramType = match[2];
    const parameter = (match[3] || "").replace(/^\./, "");
    result += text.slice(currentIndex, matchIndex);
    if (
      typeof flowData === "object" &&
      flowData !== null &&
      "nodes" in flowData
    ) {
      const nodes = (flowData as { nodes: NodeType[] }).nodes;
      const node = nodes.find(n => n.id === cleanUuid);
      const startNodeParams = nodes.find(item => item.type === "start-node");
      const nodeName = props.selectedTool?.description;
      if (startNodeParams?.properties.start_params?.length > 0) {
        const nameToDescMap = new Map(
          startNodeParams.properties.start_params.map(param => [
            param.name,
            param.desc
          ])
        );
        // 节点信息
        if (paramType === "output" || paramType === "input") {
          if (cleanUuid === startNodeId && paramType === "output") {
            const desc = nameToDescMap.get(parameter.replace(/^\./, ""));
            if (desc) {
              result += `<${nodeName || "剧本"}的输入参数：${desc}>`;
            } else {
              result += `<${nodeName || "剧本"}的输入参数：${parameter}>`;
            }
          } else {
            if (node?.properties?.node_name) {
              const desc = nameToDescMap.get(parameter.replace(/^\./, ""));
              result += `<${node.properties.node_name}的${paramType === "output" ? "输出参数：" : "输入参数："}${desc || parameter}>`;
            }
          }
        }
      } else {
        if (paramType === "output" || paramType === "input") {
          if (cleanUuid === startNodeId && paramType === "output") {
            result += `<${nodeName || "剧本"}的输入参数：${parameter}>`;
          } else {
            if (node?.properties?.node_name) {
              const cleanParam = parameter.replace(/^\./, "");
              result += `<${node.properties.node_name}的${paramType === "output" ? "输出参数：" : "输入参数："}${cleanParam}>`;
            }
          }
        }
      }
    } else {
      result += matchText;
    }
    currentIndex = matchIndex + matchText.length;
  }
  result += text.slice(currentIndex);

  // 2. 处理全局变量 ${global.xxx} -> <全局变量：xxx>
  result = result.replace(
    /\$\{global\.([a-zA-Z0-9_]+)\}/g,
    (match, varName) => `<全局变量：${varName}>`
  );

  return result;
}

// 计算属性处理显示值
const displayedValue = computed({
  get() {
    if (!inputUuid.value) return "";
    return getNodeTitle(inputUuid.value);
  },
  set(value: string) {
    // 处理用户手动输入的情况
    inputUuid.value = value;
    console.log("inputUuid.value:", inputUuid.value);
    emit("update:modelValue", value);
  }
});

// 新增计算属性：判断是否为参数文本
const isParamText = computed(() => {
  return (text: string): boolean => {
    return /^<[^>]+>$/.test(text);
  };
});

// 进入编辑模式
function enterEditMode() {
  if (props.readonly) return;

  isEditing.value = true;
  // 延迟聚焦
  setTimeout(() => {
    inputRef.value?.focus();
  }, 100);
}

// 失去焦点处理
function handleBlur() {
  isEditing.value = false;

  // 强制格式化并触发事件
  formatAndEmit();
}

// 强制格式化并触发事件
function formatAndEmit() {
  // 格式化输入内容
  const formattedValue = formatPath(inputUuid.value);
  inputUuid.value = formattedValue;

  // 触发update:modelValue事件
  emit("update:modelValue", formattedValue);

  // 触发select-node事件
  emit("select-node", formattedValue);
}

// 路径分隔符正则表达式（支持中英文斜杠和反斜杠，使用Unicode转义确保兼容性）
const PATH_SEPARATOR_REGEX = /[/\\｜＼]+/g;

// 修改空白字符处理正则表达式
const WHITESPACE_REGEX = /[\t\r\f\v]/g;

/**
 * 格式化路径，将路径转换为统一格式
 * @param path 要格式化的路径
 * @returns 格式化后的路径
 */
function formatPath(path: string): string {
  // 统一路径分隔符为正斜杠
  let formattedPath = path.replace(PATH_SEPARATOR_REGEX, "/");

  // 移除所有空白字符（不仅是末尾）
  formattedPath = formattedPath.replace(WHITESPACE_REGEX, "");

  // 修复Windows路径前缀（使用更健壮的正则表达式）
  const windowsPathRegex = /^([a-zA-Z]):(\/|\\)/;
  if (windowsPathRegex.test(formattedPath)) {
    formattedPath = formattedPath.replace(windowsPathRegex, "$1:/");
  }
  console.log("formattedPath:", formattedPath);
  return formattedPath;
}
</script>

<style lang="scss" scoped>
.uuid-display-container {
  position: relative;
  width: 100%;
  background: white;
}

.uuid-edit-input {
  width: auto;
}

.node-display {
  cursor: pointer;
  border-radius: 4px;
  width: 100%;
}

.node-display-text {
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 0px 5px;
}

/* 文字阴影样式 - 仅应用于参数文本 */
.shadowed-text {
  background: #ecf2ff;
  padding: 5px;
}
</style>
