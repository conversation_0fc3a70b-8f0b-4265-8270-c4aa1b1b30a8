import { getPluginsList } from "./build/plugins";
import { exclude, include } from "./build/optimize";
import { type ConfigEnv, loadEnv, type UserConfigExport } from "vite";
import {
  __APP_INFO__,
  alias,
  pathResolve,
  root,
  wrapperEnv
} from "./build/utils";

export default ({ mode }: ConfigEnv): UserConfigExport => {
  // 加载环境变量
  const env = loadEnv(mode, root);
  const { VITE_CDN, VITE_PORT, VITE_COMPRESSION, VITE_PUBLIC_PATH } =
    wrapperEnv(env);

  console.log("当前环境:", mode);

  // 获取API地址和路径的环境变量
  const VITE_LION_MGMT_HOST_PATH = env.VITE_LION_MGMT_HOST_PATH;

  let cfg = {
    base: VITE_PUBLIC_PATH,
    root,
    resolve: {
      alias
    },
    server: {
      // 端口号
      port: VITE_PORT,
      host: "0.0.0.0", // 主机地址，0.0.0.0 表示允许所有网络访问
      // 预热文件以提前转换和缓存结果，降低启动期间的初始页面加载时长并防止转换瀑布
      warmup: {
        clientFiles: ["./index.html", "./src/{views,components}/*"]
      }
    },
    plugins: getPluginsList(VITE_CDN, VITE_COMPRESSION),
    // https://cn.vitejs.dev/config/dep-optimization-options.html#dep-optimization-options
    optimizeDeps: {
      include,
      exclude
    },
    build: {
      // https://cn.vitejs.dev/guide/build.html#browser-compatibility
      target: "es2015",
      sourcemap: false,
      // 消除打包大小超过500kb警告
      chunkSizeWarningLimit: 4000,
      rollupOptions: {
        input: {
          index: pathResolve("./index.html", import.meta.url)
        },
        // 静态资源分类打包
        output: {
          chunkFileNames: "static/js/[name]-[hash].js",
          entryFileNames: "static/js/[name]-[hash].js",
          assetFileNames: "static/[ext]/[name]-[hash].[ext]"
        }
      }
    },
    define: {
      // 将API地址暴露给客户端代码，方便直接使用
      // "process.env.VITE_LION_MGMT_HOST": JSON.stringify(VITE_LION_MGMT_HOST),
      "process.env.VITE_LION_MGMT_HOST_PATH": JSON.stringify(
        VITE_LION_MGMT_HOST_PATH
      ),
      __INTLIFY_PROD_DEVTOOLS__: false,
      __APP_INFO__: JSON.stringify(__APP_INFO__)
    }
  };
  if (mode === "development") {
    cfg["server"]["proxy"] = {
      [VITE_LION_MGMT_HOST_PATH]: {
        target: env.VITE_LION_MGMT_HOST,
        changeOrigin: true,
        secure: false,
        ws: true,
        rewrite: path => path
      }
    };
  }
  return cfg;
};
