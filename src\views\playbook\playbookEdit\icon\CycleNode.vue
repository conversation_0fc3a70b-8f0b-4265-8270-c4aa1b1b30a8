<template>
  <div class="loop-body-node">
    <!-- 顶部栏 -->
    <div class="loop-header">
      <div class="loop-title-container">
        <span class="loop-icon">⟲</span>
        <span class="loop-title">循环体</span>
      </div>
      <div class="loop-container">
        <span>按住Ctrl键拖拽可移出循环体</span>
      </div>
      <div class="loop-delete" @click="handleDelete">
        <iconify-icon-offline
          icon="icon-park-outline:delete"
          width="24"
          height="24"
        />
      </div>
    </div>
    <!-- 内容区域 -->
    <div class="loop-content">
      <div class="loop-placeholder">拖拽节点到此处</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import iconifyIconOffline from "@/components/ReIcon/src/iconifyIconOffline";

const props = defineProps({
  id: {
    type: String
  },
  properties: {
    type: Object,
    required: true
  },
  model: {
    type: Object
  },
  graphModel: {
    type: Object
  },
  text: {
    type: String
  }
});

// 删除节点
const handleDelete = () => {
  props.graphModel.eventCenter.emit("custom:onBtnDelClick", {
    props: { model: props.model }
  });
};
</script>

<style lang="scss" scoped>
.loop-body-node {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
}

.loop-header {
  height: 40px;
  background-color: #20b2aa;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  font-size: 14px;
  font-weight: 600;
  position: relative;
  flex-shrink: 0;
}

.loop-icon {
  font-size: 16px;
  margin-right: 8px;
  color: #ffffff;
}

.loop-title {
  flex: 1;
  color: #ffffff;
  font-weight: 600;
}

.loop-delete {
  width: 24px;
  height: 16px;
  color: #ffffff;
  border-radius: 2px;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.loop-content {
  flex: 1;
  background-color: white;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-top: 1px solid #e0e0e0;
}

.loop-placeholder {
  color: #999999;
  font-size: 14px;
  text-align: center;
  user-select: none;
  height: auto;
  width: auto;
  min-height: 100vh;
  min-width: 100vw;
}
</style>
