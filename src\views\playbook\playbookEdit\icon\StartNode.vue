<template>
  <div class="vue-wrap">
    <div class="vue-html">
      <div class="vue-html-title">
        <div>开始</div>
      </div>
      <div class="vue-html-container">
        <svg
          class="icon"
          height="35"
          p-id="3519"
          t="1748432786368"
          version="1.1"
          viewBox="0 0 1024 1024"
          width="35"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M226.592 896C167.616 896 128 850.48 128 782.736V241.264C128 173.52 167.616 128 226.592 128c20.176 0 41.136 5.536 62.288 16.464l542.864 280.432C887.648 453.792 896 491.872 896 512s-8.352 58.208-64.272 87.088L288.864 879.536C267.712 890.464 246.768 896 226.592 896z m0-704.304c-31.008 0-34.368 34.656-34.368 49.568v541.472c0 14.896 3.344 49.568 34.368 49.568 9.6 0 20.88-3.2 32.608-9.248l542.864-280.432c21.904-11.328 29.712-23.232 29.712-30.608s-7.808-19.28-29.712-30.592L259.2 200.96c-11.728-6.048-23.008-9.264-32.608-9.264z"
            fill="rgba(18, 150, 219, 0.35)"
            p-id="3520"
          />
        </svg>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  id: {
    type: String
  },
  properties: {
    type: Object,
    required: true
  },
  graphModel: Object,
  model: Object,
  onBtnCopyClick: Function,
  onBtnDelClick: Function
});

// // 复制节点ID
// const $_copyNodeId = async () => {
//   try {
//     // 使用现代Clipboard API替代已废弃的execCommand
//     await navigator.clipboard.writeText(`\$\{${props.id}\}`);
//   } catch (err) {
//     console.error("复制失败:", err);
//     // 添加失败反馈（可根据项目需求替换为错误提示）
//   }
// };
</script>

<style lang="scss" scoped>
.vue-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  filter: drop-shadow(
    1px 5px 3px rgba(50, 50, 0, 0.5)
  ); //向右偏移、向下偏移、模糊半径
  .vue-html {
    width: 90px;
    height: 90px;
    border: 2px solid rgba(18, 150, 219, 0.35);
    border-radius: 50%;
    overflow: hidden;
    background: white;

    .vue-html-title {
      display: flex;
      justify-content: space-around;
      height: 25%;
      color: black;
      font-size: 14px;
      padding-top: 10px;
    }

    .vue-html-container {
      display: flex;
      justify-content: center;
      height: 75%;
      padding-top: 15px;
    }
  }
}
</style>
