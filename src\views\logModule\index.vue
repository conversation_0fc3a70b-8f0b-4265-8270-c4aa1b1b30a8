<!-- eslint-disable vue/no-unused-vars -->
<template>
  <div class="audit-container">
    <el-card>
      <!-- 搜索配置 -->
      <div class="search-bar">
        <!-- 字段选择下拉框 -->
        <el-select
          v-model="selectValue"
          :filterable="false"
          :no-data-text="keyword ? '未找到匹配的索引' : '暂无数据'"
          :popper-class="'custom-select-dropdown'"
          :reserve-keyword="true"
          clearable
          size="default"
          placeholder="请选择索引"
          class="search-bar__select"
        >
          <!-- 自定义关键字搜索框 -->
          <div class="dropdown-search-bar">
            <el-input
              v-model="keyword"
              :prefix-icon="Search"
              class="dropdown-search-input"
              clearable
              placeholder="输入关键字搜索"
              size="default"
              @click.stop
              @mousedown.stop
            />
          </div>
          <el-option
            v-for="item in filteredOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
            class="custom-option"
          />
          <!-- 当没有匹配选项时显示提示选项 -->
          <el-option
            v-if="keyword && filteredOptions.length === 0"
            key="no-data"
            class="no-data-option"
            disabled
            label="未找到匹配的索引"
            value=""
          />
        </el-select>
        <!-- 搜索过滤条件 -->
        <el-tooltip :hide-after="0" content="搜索过滤条件" placement="top">
          <div class="filter-bar">
            <el-button
              ref="addFilterBtn"
              size="default"
              class="filter-bar__item filter-bar__add"
              @click="filterDialogVisible = !filterDialogVisible"
            >
              <IconifyIconOffline
                icon="mingcute:filter-line"
                width="16"
                height="16"
              />
            </el-button>
            <el-popover
              ref="filterPopover"
              :virtual-ref="addFilterBtn"
              :visible="filterDialogVisible"
              placement="bottom-start"
              popper-class="custom-filter-popover"
              trigger="manual"
              virtual-triggering
              width="800px"
              @after-leave="filterDialogVisible = false"
            >
              <Filtration
                :fields="[...filteredShowFields, ...filteredOptionalFields]"
                @cancel="handleFilterCancel"
                @confirm="handleFilterConfirm"
              />
            </el-popover>
          </div>
        </el-tooltip>
        <el-input
          v-model="searchQuery"
          clearable
          placeholder="在此处输入您的搜索查询，然后按Enter"
          class="search-bar__input"
          size="default"
          @keyup.enter="executeSearch"
        />
        <div class="search-buttons">
          <el-button
            size="default"
            :icon="Search"
            type="primary"
            @click="executeSearch"
          >
            查询
          </el-button>
          <el-button
            size="default"
            :icon="Download"
            type="primary"
            @click="executeSearch"
          >
            导出
          </el-button>
        </div>
        <!-- 时间条件区域 -->
        <div class="time-controls">
          <TimeRangePicker v-model="dateRange" />
          <!-- 日志刷新时间选择 -->
          <div class="refresh-control">
            <el-select
              v-model="refreshInterval"
              :suffix-icon="Refresh"
              placeholder="off"
              size="default"
              class="refresh-select"
            >
              <el-option
                v-for="item in refreshOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
                <template #default>
                  <el-icon
                    v-if="item.value === 'off'"
                    style="margin-right: 4px"
                  >
                    <Refresh />
                  </el-icon>
                  {{ item.label }}
                </template>
              </el-option>
            </el-select>
            <!-- 倒计时提示 -->
            <div v-if="showCountdown" class="countdown-tip">
              <el-icon>
                <Refresh />
              </el-icon>
              <span>{{ countdown }}s</span>
            </div>
          </div>
        </div>
      </div>
      <!-- 搜索条件标签 -->
      <div v-if="filterConditions.length > 0" class="search-conditions">
        <el-tag
          v-for="condition in filterConditions"
          :key="condition.id"
          :type="condition.type"
          class="search-condition-tag"
          closable
          @close="removeFilterCondition(condition.id)"
        >
          {{ condition.text }}
        </el-tag>
        <el-button size="small" type="text" @click="clearAllFilterConditions">
          清除全部
        </el-button>
      </div>
      <!-- 直方图 -->
      <div class="histogram-container">
        <div class="histogram-header">
          <div class="histogram-title">
            <el-icon
              style="cursor: pointer; margin-left: 8px"
              @click="toggleHistogram"
            >
              <component :is="showHistogram ? CaretBottom : CaretRight" />
            </el-icon>
            查询直方图
          </div>
          <div class="histogram-info">
            <span>总查询数: {{ Count }}</span>
            <span>查询时间: {{ queryTime }}</span>
          </div>
        </div>
        <div v-show="showHistogram">
          <div ref="histogramChart" class="histogram-chart" />
        </div>
      </div>
      <!-- 表格 -->
      <div class="main-content">
        <!-- 左侧列表 -->
        <div class="quick-analysis" :class="{ collapsed: !showQuickAnalysis }">
          <div class="qa-title">
            <span v-if="showQuickAnalysis">日志字段</span>
            <el-button text @click="showQuickAnalysis = !showQuickAnalysis">
              <IconifyIconOffline
                :icon="
                  showQuickAnalysis
                    ? 'mingcute:arrow-to-left-fill'
                    : 'mingcute:arrow-to-right-fill'
                "
                width="20"
                height="20"
              />
            </el-button>
          </div>
          <div v-if="showQuickAnalysis" class="qa-content">
            <el-input
              v-model="fieldKeyword"
              :prefix-icon="Search"
              class="qa-search"
              clearable
              placeholder="搜索字段名"
              size="small"
            />
            <el-scrollbar class="qa-scrollbar">
              <div class="qa-group-title">表格展示字段</div>
              <el-collapse
                v-model="showFieldsActive"
                :expand-icon-position="'left'"
                class="qa-collapse"
                @change="handleShowFieldsChange"
              >
                <el-collapse-item
                  v-for="item in filteredShowFields"
                  :key="item.value"
                  :name="item.value"
                >
                  <template #title>
                    <div class="custom-collapse-title">
                      <el-tooltip
                        :content="item.label"
                        :show-after="500"
                        placement="top"
                      >
                        <span class="field-name qa-field-ellipsis">{{
                          item.label
                        }}</span>
                      </el-tooltip>
                      <span class="field-actions">
                        <span
                          class="remove-field-btn"
                          @click.stop="removeFieldFromTable(item)"
                        >
                          <el-icon><Minus /></el-icon>
                        </span>
                        <!-- <span
                          class="field-more-btn"
                          @click.stop="showFieldMore(item, $event)"
                        >
                          <el-icon><More /></el-icon>
                        </span> -->
                      </span>
                    </div>
                  </template>
                  <!-- 展开内容 -->
                  <div class="field-progress">
                    <span>占比</span>
                    <el-progress
                      v-if="fieldPercentages[item.value] !== undefined"
                      :percentage="fieldPercentages[item.value]"
                      :stroke-width="6"
                      :show-text="false"
                      color="#409EFF"
                    />
                    <span v-else class="loading-text">查询中...</span>
                    <span
                      v-if="fieldPercentages[item.value] !== undefined"
                      class="progress-text"
                    >
                      {{ fieldPercentages[item.value] }}%
                    </span>
                  </div>
                </el-collapse-item>
              </el-collapse>
              <div class="qa-group-title">可选字段</div>
              <el-collapse
                v-model="optionalFieldsActive"
                :expand-icon-position="'left'"
                class="qa-collapse"
                @change="handleOptionalFieldsChange"
              >
                <el-collapse-item
                  v-for="item in filteredOptionalFields"
                  :key="item.value"
                  :name="item.value"
                >
                  <template #title>
                    <div class="custom-collapse-title">
                      <el-tooltip
                        :content="item.label"
                        :show-after="500"
                        placement="top"
                      >
                        <span class="field-name qa-field-ellipsis">{{
                          item.label
                        }}</span>
                      </el-tooltip>
                      <span class="field-actions">
                        <span
                          class="add-field-btn"
                          @click.stop="addFieldToTable(item)"
                        >
                          <el-icon><Plus /></el-icon>
                        </span>
                        <!-- <span
                          class="field-more-btn"
                          @click.stop="showFieldMore(item, $event)"
                        >
                          <el-icon><More /></el-icon>
                        </span> -->
                      </span>
                    </div>
                  </template>
                  <div class="field-progress">
                    <span>占比</span>
                    <el-progress
                      v-if="fieldPercentages[item.value] !== undefined"
                      :percentage="fieldPercentages[item.value]"
                      :stroke-width="6"
                      :show-text="false"
                      color="#409EFF"
                    />
                    <span v-else class="loading-text">查询中...</span>
                    <span
                      v-if="fieldPercentages[item.value] !== undefined"
                      class="progress-text"
                    >
                      {{ fieldPercentages[item.value] }}%
                    </span>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </el-scrollbar>
          </div>
        </div>
        <!-- 右侧表格 -->
        <div class="log-table-wrapper">
          <el-table :data="tableData" border style="width: 100%">
            <!-- 可折叠行 -->
            <el-table-column type="expand">
              <template #default="props">
                <div class="expanded-content">
                  <json-viewer
                    :value="props.row"
                    :expand-depth="5"
                    copyable
                    sort
                    style="background-color: #f5f7fa"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column
              v-for="field in filteredShowFields"
              :key="field.value"
              :label="field.label"
              :prop="field.value"
            >
              <template #default="{ row }">
                <el-tooltip
                  :content="String(row[field.value] || '')"
                  :disabled="!row[field.value]"
                  :show-after="500"
                  placement="top"
                >
                  <span class="table-cell-content">{{
                    formatCellValue(row[field.value])
                  }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="Count"
            background
            layout="total, prev, pager, next"
            style="margin-top: 12px; display: flex; justify-content: flex-end"
          />
        </div>
      </div>
    </el-card>

    <!-- 字段More悬浮框 -->
    <el-popover
      ref="morePopoverRef"
      v-model:visible="fieldMoreVisible"
      :virtual-ref="moreBtnRef"
      placement="bottom-end"
      popper-class="field-more-popover"
      trigger="manual"
      virtual-triggering
      width="200"
    >
      <div class="popup-header">
        <span class="popup-title">{{ currentField?.label }}</span>
        <el-icon class="popup-close" @click="fieldMoreVisible = false">
          <Close />
        </el-icon>
      </div>
      <div class="popup-content">
        <div class="popup-item" @click="handleStatisticalAnalysis">
          <el-icon>
            <DataAnalysis />
          </el-icon>
          <span>统计分析</span>
        </div>
        <div class="popup-item" @click="handleAddToQuery">
          <el-icon>
            <Edit />
          </el-icon>
          <span>添加到查询语句</span>
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script setup>
import {
  CaretBottom,
  CaretRight,
  Close,
  DataAnalysis,
  Download,
  Edit,
  Filter,
  Minus,
  More,
  Plus,
  Refresh,
  Search
} from "@element-plus/icons-vue";
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from "vue";
import { initHistogramChart } from "./components/HistogramChart";
import { apiGetIndexList, apiGetLogs, apiGetLogsData } from "@/api/logs";
import { message } from "@/utils/message";
import Filtration from "./components/filtration.vue";
import TimeRangePicker from "./components/TimeRangePicker.vue";
import IconifyIconOffline from "@/components/ReIcon/src/iconifyIconOffline";
import JsonViewer from "vue-json-viewer";

const showQuickAnalysis = ref(true);
const dateRange = ref(null);
const selectValue = ref([]);
const keyword = ref("");
const options = ref([]);

// 搜索查询输入框
const searchQuery = ref("");

// 右侧表格分页
const pageSize = ref(15);
const currentPage = ref(1);
const Count = ref(0);

// 搜索条件标签
const filterConditions = ref([]);
// 索引列表
const filteredOptions = computed(() => {
  if (!options.value || options.value.length === 0) {
    return [];
  }
  return options.value.filter(
    item =>
      !keyword.value ||
      item.label.toLowerCase().includes(keyword.value.toLowerCase())
  );
});

const refreshInterval = ref("off");
const refreshOptions = [
  { label: "off", value: "off" },
  { label: "1s", value: "1s" },
  { label: "2s", value: "2s" },
  { label: "5s", value: "5s" },
  { label: "30s", value: "30s" },
  { label: "1m", value: "1m" }
];

// 定时器相关
let refreshTimer = null;
let countdownTimer = null;
const countdown = ref(0);
const showCountdown = ref(false);

const fieldKeyword = ref("");
const showFieldsActive = ref([]);
const optionalFieldsActive = ref([]);
// 存储字段占比数据的响应式对象
const fieldPercentages = ref({});

// 监听表格展示字段折叠面板展开事件
const handleShowFieldsChange = async activeNames => {
  // 获取最新展开的字段（数组中的最后一个）
  if (activeNames.length > 0) {
    const latestField = activeNames[activeNames.length - 1];
    const field = showFields.value.find(item => item.value === latestField);
    if (field) {
      console.log("展开字段:", field.label);
      // 只有当该字段还没有占比数据时才查询
      if (!fieldPercentages.value[field.value]) {
        const percentage = await getFieldPercentage(field.value);
        fieldPercentages.value[field.value] = percentage;
      }
    }
  }
};

// 监听可选字段折叠面板展开事件
const handleOptionalFieldsChange = async activeNames => {
  // 获取最新展开的字段（数组中的最后一个）
  if (activeNames.length > 0) {
    const latestField = activeNames[activeNames.length - 1];
    const field = optionalFields.value.find(item => item.value === latestField);
    if (field) {
      console.log("展开字段:", field.label);
      // 只有当该字段还没有占比数据时才查询
      if (!fieldPercentages.value[field.value]) {
        const percentage = await getFieldPercentage(field.value);
        fieldPercentages.value[field.value] = percentage;
      }
    }
  }
};

// 表格展示字段 - 默认显示前3个字段
const showFields = ref([]);
// 可选字段 - 从API获取的所有字段
const optionalFields = ref([]);

const filteredShowFields = computed(() =>
  showFields.value.filter(
    item =>
      !fieldKeyword.value ||
      item.label.toLowerCase().includes(fieldKeyword.value.toLowerCase())
  )
);
const filteredOptionalFields = computed(() =>
  optionalFields.value.filter(
    item =>
      !fieldKeyword.value ||
      item.label.toLowerCase().includes(fieldKeyword.value.toLowerCase())
  )
);

// 表格演示数据
const tableData = ref([{}]);

const addFieldToTable = field => {
  // 将可选字段添加到表格展示字段中
  showFields.value.push(field);
  // 从可选字段中移除
  const index = optionalFields.value.findIndex(
    item => item.value === field.value
  );
  if (index > -1) {
    optionalFields.value.splice(index, 1);
  }
};

const removeFieldFromTable = field => {
  // 从表格展示字段中移除
  const index = showFields.value.findIndex(item => item.value === field.value);
  if (index > -1) {
    showFields.value.splice(index, 1);
  }
  // 添加到可选字段中
  optionalFields.value.push(field);
};

const morePopoverRef = ref();
const moreBtnRef = ref();

// More图标点击处理函数
const showFieldMore = (field, event) => {
  currentField.value = field;
  moreBtnRef.value = event.target.closest(".field-more-btn");
  fieldMoreVisible.value = true;
};

// 处理统计分析
const handleStatisticalAnalysis = () => {
  console.log("统计分析:", currentField.value);
  // 这里可以添加统计分析的逻辑
  fieldMoreVisible.value = false;
};

// 处理添加到查询语句
const handleAddToQuery = () => {
  console.log("添加到查询语句:", currentField.value);
  // 构建查询语句
  const fieldName = currentField.value?.value;
  const currentIndex = selectValue.value;
  if (fieldName && currentIndex) {
    const query = `SELECT ${fieldName} FROM \"${currentIndex}\"`;
    // 如果当前输入框有内容，则追加；否则替换
    if (searchQuery.value.trim()) {
      searchQuery.value += `\n${query}`;
    } else {
      searchQuery.value = query;
    }
  }
  fieldMoreVisible.value = false;
};

// 执行搜索查询
const executeSearch = async () => {
  if (!searchQuery.value.trim()) {
    message("请输入查询语句", { type: "warning" });
    return;
  }

  // 检查是否有可用的索引选项
  if (options.value.length === 0) {
    message("暂无可用索引，无法执行查询", { type: "warning" });
    return;
  }

  try {
    const result = await getLogData(searchQuery.value);
    if (result && result.columns && result.rows) {
      // 更新字段列表
      const allFields = result.columns.map(column => ({
        label: column.name,
        value: column.name
      }));
      // 默认前三个给表格展示字段，剩下的给可选字段
      showFields.value = allFields.slice(0, 3);
      optionalFields.value = allFields.slice(3);
      // 将rows数据转换为表格数据格式
      tableData.value = result.rows.map(row => {
        const rowData = {};
        result.columns.forEach((column, index) => {
          rowData[column.name] = row[index];
        });
        return rowData;
      });
      // 更新总数
      Count.value = result.rows.length;
      message("查询执行成功", { type: "success" });
      // 更新直方图
      nextTick(() => {
        if (histogramChart.value) {
          initHistogramChart(histogramChart.value, result);
        }
      });
    } else {
      message("查询结果为空", { type: "warning" });
    }
  } catch (error) {
    console.error("查询执行失败:", error);
  }
};

// 监听点击外部事件
onMounted(() => {
  document.addEventListener("click", event => {
    // 点击 more-btn 自身时，showFieldMore 会处理显隐，这里不处理
    if (event.target.closest(".field-more-btn")) {
      return;
    }
    // 点击 popover 外部时，隐藏 popover
    if (fieldMoreVisible.value) {
      fieldMoreVisible.value = false;
    }
  });
});

// 悬浮框相关变量
const fieldMoreVisible = ref(false);
const currentField = ref(null);

// 直方图相关数据
const histogramChart = ref();
const totalCount = ref(90);
const queryTime = ref("2025-07-10 14:27:05");

// 控制直方图折叠
const showHistogram = ref(true);
const toggleHistogram = () => {
  showHistogram.value = !showHistogram.value;
};

// 弹窗相关变量
const addFilterBtn = ref();
const filterPopover = ref();
const filterDialogVisible = ref(false);

// 初始化
onMounted(async () => {
  // 延迟初始化直方图，确保DOM已经渲染
  nextTick(() => {
    if (histogramChart.value) {
      // 初始化时不传递数据，让图表显示空状态
      initHistogramChart(histogramChart.value, null);
    }
  });

  await getIndexList();
  // 默认选择第一个索引并搜索数据
  if (selectValue.value) {
    await searchLogData(selectValue.value);
  }
});

// 组件卸载时清理定时器
onUnmounted(() => {
  clearRefreshTimer();
});

// 监听 selectValue 变化
watch(selectValue, async newValue => {
  if (newValue) {
    // 清除过滤条件
    filterConditions.value = [];
    // 执行搜索
    await searchLogData(newValue);
  }
});

// 监听搜索查询输入框变化
watch(searchQuery, async newValue => {
  if (!newValue || newValue.trim() === "") {
    // 当输入框被清空时，执行默认查询
    if (selectValue.value) {
      // 清除过滤条件
      filterConditions.value = [];
      await searchLogData(selectValue.value);
    }
  }
});

// 监听刷新间隔变化
watch(refreshInterval, newValue => {
  if (newValue === "off") {
    clearRefreshTimer();
  } else {
    startRefreshTimer();
  }
});

// 监听时间范围变化
watch(
  dateRange,
  async (newValue, oldValue) => {
    console.log("dateRange changed:", { newValue, oldValue });

    // 有索引选择时，自动执行查询
    if (selectValue.value && options.value.length > 0) {
      // 清除过滤条件
      filterConditions.value = [];
      let sql = "";
      if (newValue && Array.isArray(newValue) && newValue.length === 2) {
        // 有时间范围，拼接时间条件
        const [start, end] = newValue;
        const startTime = start.toISOString();
        const endTime = end.toISOString();
        sql =
          'SELECT * FROM "' +
          selectValue.value +
          "\" WHERE timestamp >= '" +
          startTime +
          "' AND timestamp < '" +
          endTime +
          "' ORDER BY timestamp DESC LIMIT " +
          pageSize.value;
      } else {
        // dateRange为null或空，全部数据不加时间条件
        sql = `SELECT * FROM "${selectValue.value}" LIMIT ${pageSize.value}`;
      }
      console.log("自动执行时间范围查询:", sql);
      try {
        const result = await getLogData(sql);
        if (result && result.columns && result.rows) {
          // 更新字段列表
          const allFields = result.columns.map(column => ({
            label: column.name,
            value: column.name
          }));
          // 更新表格数据
          showFields.value = allFields.slice(0, 3);
          optionalFields.value = allFields.slice(3);
          tableData.value = result.rows.map(row => {
            const rowData = {};
            result.columns.forEach((column, index) => {
              rowData[column.name] = row[index];
            });
            return rowData;
          });
          Count.value = result.rows.length;
          message("时间范围查询执行成功", { type: "success" });
          // 更新直方图
          if (histogramChart.value) {
            initHistogramChart(histogramChart.value, result);
          }
        } else {
          message("时间范围内无数据", { type: "warning" });
        }
      } catch (error) {
        console.error("时间范围查询失败:", error);
        message("时间范围查询失败", { type: "error" });
      }
    }
  },
  { deep: true }
);

// 监听分页大小变化
watch(pageSize, async (newSize, oldSize) => {
  console.log("pageSize changed:", { newSize, oldSize });

  // 重置到第一页
  currentPage.value = 1;

  // 有索引选择时，重新查询数据
  if (selectValue.value && options.value.length > 0) {
    await searchLogData(selectValue.value);
  }
});

// 监听当前页码变化
watch(currentPage, async (newPage, oldPage) => {
  console.log("currentPage changed:", { newPage, oldPage });

  // 有索引选择时，重新查询数据
  if (selectValue.value && options.value.length > 0) {
    await searchLogData(selectValue.value);
  }
});

// 1、获取索引列表
const getIndexList = async () => {
  try {
    const res = await apiGetIndexList({});
    if (res.code === 0) {
      // 检查接口返回的数据是否为空
      if (!res.data || res.data.length === 0) {
        message("暂无可用索引，请检查系统配置", { type: "warning" });
        options.value = [];
        selectValue.value = "";
        return;
      }

      // 添加"全部"选项到开头
      const allOption = { label: "全部", value: "logs-*" };
      options.value = [
        allOption,
        ...res.data.map(item => ({
          label: item,
          value: item
        }))
      ];
      // 默认选择"全部"选项
      selectValue.value = allOption.value;
      // 清除过滤条件
      filterConditions.value = [];
    } else {
      message("获取索引列表失败", { type: "error" });
      options.value = [];
      selectValue.value = "";
    }
  } catch (error) {
    console.error("获取索引列表异常:", error);
    message("获取索引列表异常", { type: "error" });
    options.value = [];
    selectValue.value = "";
  }
};
// 2、获取索引字段列表
const getIndexFields = index => {
  apiGetLogs({ index }).then(res => {
    if (res.code === 0 && res.data) {
      const firstIndexKey = Object.keys(res.data)[0];
      const fieldsData = res.data[firstIndexKey];
      // 字段选项
      const allFields = Object.keys(fieldsData).map(fieldName => ({
        label: fieldName,
        value: fieldName
      }));
      // 默认前三个给表格展示字段，剩下的给可选字段
      showFields.value = allFields.slice(0, 3);
      optionalFields.value = allFields.slice(3);
    }
  });
};
// 3、获取日志数据
const getLogData = async sql => {
  // 获取当前时间并格式化
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");
  const seconds = String(now.getSeconds()).padStart(2, "0");
  queryTime.value = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

  const res = await apiGetLogsData({ sql });
  if (res.code === 0) {
    return res.data;
  }
  return null;
};

// 4、搜索日志数据
const searchLogData = async indexName => {
  // 检查是否有可用的索引选项
  if (!indexName || options.value.length === 0) {
    message("暂无可用索引，无法执行查询", { type: "warning" });
    return;
  }

  // 清除过滤条件
  filterConditions.value = [];

  // 获取总数
  const countData = await getLogData(`SELECT COUNT(*) FROM \"${indexName}\"`);
  if (countData && countData.rows && countData.rows.length > 0) {
    Count.value = countData.rows[0][0]; // 获取COUNT(*)的结果

    // 获取表格数据
    const tableDataResult = await getLogData(
      `SELECT * FROM \"${indexName}\" LIMIT ${pageSize.value}`
    );
    if (tableDataResult && tableDataResult.columns && tableDataResult.rows) {
      // 更新字段列表
      const allFields = tableDataResult.columns.map(column => ({
        label: column.name,
        value: column.name
      }));

      // 默认前三个给表格展示字段，剩下的给可选字段
      showFields.value = allFields.slice(0, 3);
      optionalFields.value = allFields.slice(3);

      // 将rows数据转换为表格数据格式
      tableData.value = tableDataResult.rows.map(row => {
        const rowData = {};
        tableDataResult.columns.forEach((column, index) => {
          rowData[column.name] = row[index];
        });
        return rowData;
      });

      // 更新直方图
      nextTick(() => {
        if (histogramChart.value) {
          initHistogramChart(histogramChart.value, tableDataResult);
        }
      });
    }
  }
};

// 处理过滤器确认事件
const handleFilterConfirm = async allGroups => {
  console.log("所有条件组：", allGroups);

  // 检查是否有可用的索引选项
  if (options.value.length === 0) {
    message("暂无可用索引，无法执行查询", { type: "warning" });
    filterDialogVisible.value = false;
    return;
  }

  // 将条件转换为SQL查询
  const sql = buildSqlFromConditions(allGroups.conditions, selectValue.value);
  console.log("生成的SQL查询:", sql);

  // 将条件转换为标签显示
  filterConditions.value = convertConditionsToTags(allGroups.conditions);

  try {
    const result = await getLogData(sql);
    if (result && result.columns && result.rows) {
      // 更新字段列表
      const allFields = result.columns.map(column => ({
        label: column.name,
        value: column.name
      }));

      // 更新表格数据
      showFields.value = allFields.slice(0, 3);
      optionalFields.value = allFields.slice(3);

      tableData.value = result.rows.map(row => {
        const rowData = {};
        result.columns.forEach((column, index) => {
          rowData[column.name] = row[index];
        });
        return rowData;
      });

      Count.value = result.rows.length;
      message("条件过滤查询执行成功", { type: "success" });
      // 更新直方图
      nextTick(() => {
        if (histogramChart.value) {
          initHistogramChart(histogramChart.value, result);
        }
      });
    } else {
      message("条件过滤查询结果为空", { type: "warning" });
    }
  } catch (error) {
    console.error("条件过滤查询失败:", error);
    message("条件过滤查询失败", { type: "error" });
  }

  filterDialogVisible.value = false;
};

// 将条件转换为SQL查询的函数（支持"下一组绑定上一组"的方式）
const buildSqlFromConditions = (conditions, indexName) => {
  if (!conditions || conditions.length === 0) {
    return `SELECT *
            FROM \"${indexName}\" LIMIT ${pageSize.value}`;
  }

  console.log("处理条件数组:", conditions);
  let whereClause = "";

  conditions.forEach((condition, index) => {
    const { type, name, value, connect_type } = condition;
    console.log(`处理第${index + 1}个条件:`, {
      type,
      name,
      value,
      connect_type
    });

    // 从第二个条件开始，添加连接符
    if (index > 0 && connect_type) {
      whereClause += ` ${connect_type} `;
      console.log(`添加连接符: ${connect_type}`);
    }

    // 根据条件类型构建SQL条件
    switch (type) {
      case "exists":
        whereClause += `${name} IS NOT NULL`;
        break;
      case "!exists":
        whereClause += `${name} IS NULL`;
        break;
      case "==":
        // 判断是否为数字
        if (!isNaN(value) && value !== "") {
          whereClause += `${name} = ${value}`;
        } else {
          whereClause += `${name} = '${value}'`;
        }
        break;
      case "!=":
        // 判断是否为数字
        if (!isNaN(value) && value !== "") {
          whereClause += `${name} != ${value}`;
        } else {
          whereClause += `${name} != '${value}'`;
        }
        break;
      default:
        break;
    }
    console.log(`当前whereClause: ${whereClause}`);
  });

  const sql = `SELECT *
               FROM \"${indexName}\"${whereClause ? ` WHERE ${whereClause}` : ""} LIMIT ${pageSize.value}`;
  console.log("最终生成的SQL:", sql);
  return sql;
};

// 将条件转换为标签的函数
const convertConditionsToTags = conditions => {
  if (!conditions || conditions.length === 0) {
    return [];
  }

  return conditions.map((condition, index) => {
    const { type, name, value, connect_type } = condition;
    let tagText = "";
    let tagType = "info";

    // 根据条件类型生成标签文本
    switch (type) {
      case "exists":
        tagText = `${name} 存在`;
        tagType = "success";
        break;
      case "!exists":
        tagText = `${name} 不存在`;
        tagType = "warning";
        break;
      case "==":
        tagText = `${name} = ${value}`;
        tagType = "primary";
        break;
      case "!=":
        tagText = `${name} ≠ ${value}`;
        tagType = "danger";
        break;
      default:
        tagText = `${name} ${type} ${value}`;
        tagType = "info";
    }

    return {
      id: `condition_${index}`,
      text: tagText,
      type: tagType,
      condition: condition
    };
  });
};

// 删除单个条件标签
const removeFilterCondition = conditionId => {
  const index = filterConditions.value.findIndex(
    condition => condition.id === conditionId
  );
  if (index > -1) {
    filterConditions.value.splice(index, 1);
    // 如果没有条件了，清除所有条件并重新查询
    if (filterConditions.value.length === 0) {
      clearAllFilterConditions();
    } else {
      // 重新构建条件数组并查询
      const conditions = filterConditions.value.map(tag => tag.condition);
      const sql = buildSqlFromConditions(conditions, selectValue.value);
      executeFilterQuery(sql);
    }
  }
};

// 清除所有条件标签
const clearAllFilterConditions = () => {
  filterConditions.value = [];
  // 重新查询所有数据
  if (selectValue.value && options.value.length > 0) {
    searchLogData(selectValue.value);
  }
};

// 执行过滤查询
const executeFilterQuery = async sql => {
  // 检查是否有可用的索引选项
  if (options.value.length === 0) {
    message("暂无可用索引，无法执行查询", { type: "warning" });
    return;
  }

  try {
    const result = await getLogData(sql);
    if (result && result.columns && result.rows) {
      // 更新字段列表
      const allFields = result.columns.map(column => ({
        label: column.name,
        value: column.name
      }));

      // 更新表格数据
      showFields.value = allFields.slice(0, 3);
      optionalFields.value = allFields.slice(3);

      tableData.value = result.rows.map(row => {
        const rowData = {};
        result.columns.forEach((column, index) => {
          rowData[column.name] = row[index];
        });
        return rowData;
      });

      Count.value = result.rows.length;
      message("条件过滤查询执行成功", { type: "success" });
      // 更新直方图
      nextTick(() => {
        if (histogramChart.value) {
          initHistogramChart(histogramChart.value, result);
        }
      });
    } else {
      message("条件过滤查询结果为空", { type: "warning" });
    }
  } catch (error) {
    console.error("条件过滤查询失败:", error);
    message("条件过滤查询失败", { type: "error" });
  }
};

// 处理过滤器取消事件
const handleFilterCancel = () => {
  filterDialogVisible.value = false;
};

// 清除定时器
const clearRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
  // 清除倒计时
  if (countdownTimer) {
    clearInterval(countdownTimer);
    countdownTimer = null;
  }
  showCountdown.value = false;
  countdown.value = 0;
};

// 启动定时器
const startRefreshTimer = () => {
  // 先清除现有定时器
  clearRefreshTimer();
  if (refreshInterval.value === "off") {
    return;
  }

  // 将时间字符串转换为毫秒
  const getIntervalMs = interval => {
    switch (interval) {
      case "1s":
        return 1000;
      case "2s":
        return 2000;
      case "5s":
        return 5000;
      case "30s":
        return 30000;
      case "1m":
        return 60000;
      default:
        return 0;
    }
  };

  const intervalMs = getIntervalMs(refreshInterval.value);
  if (intervalMs > 0) {
    // 启动倒计时
    const totalSeconds = Math.floor(intervalMs / 1000);
    countdown.value = totalSeconds;
    showCountdown.value = true;
    // 倒计时定时器
    countdownTimer = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        countdown.value = totalSeconds; // 重置倒计时
      }
    }, 1000);

    refreshTimer = setInterval(async () => {
      console.log(`定时刷新数据，间隔: ${refreshInterval.value}`);

      // 如果有选中的索引，执行数据刷新
      if (selectValue.value) {
        try {
          await searchLogData(selectValue.value);
        } catch (error) {
          console.error("定时刷新数据失败:", error);
          message("定时刷新数据失败", { type: "error" });
        }
      }
    }, intervalMs);
  }
};

// 获取字段占比的函数
const getFieldPercentage = async fieldName => {
  try {
    // 构建查询SQL来计算字段占比
    const currentIndex = selectValue.value;
    if (!currentIndex || !fieldName) return 75;

    // 查询字段的非空值占比
    const sql = `
      SELECT 
        COUNT(*) as total_count,
        COUNT(${fieldName}) as non_null_count,
        ROUND(COUNT(${fieldName}) * 100.0 / COUNT(*), 2) as percentage
      FROM "${currentIndex}"
    `;
    // 查询字段占比
    const result = await getLogData(sql);
    if (result && result.rows && result.rows.length > 0) {
      const percentage = Math.round(result.rows[0][2]); // 获取百分比值
      return percentage;
    }
  } catch (error) {
    console.error("获取字段占比失败:", error);
  }
  // 如果查询失败，返回默认值
  return 0;
};

const formatCellValue = value => {
  if (
    value === null ||
    value === undefined ||
    value === "" ||
    value === "none"
  ) {
    return "--";
  }
  return value;
};
</script>

<style scoped>
/* 下拉面板整体圆角和阴影 */
:deep(.custom-select-dropdown) {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  padding-top: 0;
}

/* 无数据选项样式 */
.no-data-option {
  color: #909399 !important;
  font-style: italic;
}

/* 搜索框区域 */
.dropdown-search-bar {
  padding: 10px 12px 4px 12px;
  border-bottom: 1px solid #f0f0f0;
}

.dropdown-search-input :deep(.el-input__wrapper) {
  height: 40px;
  display: flex;
  align-items: center;
  border-radius: 6px;
  background: #f7f8fa;
  box-shadow: none;
  border: none;
}

/* 移除旧的header样式 */
.header {
  display: none;
}

.header-actions {
  display: none;
}

.title {
  font-size: 16px;
  font-weight: bold;
}

/* 时间控制区域 */
.time-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
  margin-left: auto;
  min-width: 0;
  max-width: 400px;
}

.refresh-control {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  position: relative;
}

.refresh-select {
  width: 80px;
}

.countdown-tip {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 4px;
  font-size: 12px;
  color: #0369a1;
  white-space: nowrap;
  min-width: 50px;
  height: 32px;
  justify-content: center;
  flex-shrink: 0;
  position: absolute;
  right: -60px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}

.countdown-tip .el-icon {
  margin-right: 4px;
  font-size: 12px;
}

.search-bar {
  display: flex;
  align-items: center;
  margin: 16px 0;
  gap: 12px;
  flex-wrap: nowrap;
  overflow: hidden;
  height: 40px; /* 统一高度 */
}

/* 索引选择下拉框 */
.search-bar__select {
  width: 200px;
  flex-shrink: 0;
}

/* 搜索输入框 */
.search-bar__input {
  flex: 1;
  min-width: 300px;
}

/* 操作按钮组 */
.search-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

/* 过滤条件按钮 */
.filter-bar {
  display: flex;
  align-items: center;
  width: 40px;
  height: 40px;
  background: #f6f8fa;
  border: 1px solid #e0e3e8;
  overflow: hidden;
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  flex-shrink: 0;
}

.filter-bar__item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 16px;
  color: #1976d2;
  cursor: pointer;
  background: transparent;
  transition: background 0.2s;
}

.filter-bar__item.filter-bar__add {
  width: 40px;
  height: 40px;
  background: #1976d2;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  padding: 0;
  margin-left: 0;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.08);
  transition: background 0.2s;
}

.filter-bar__item.filter-bar__add:hover {
  background: #1565c0;
}

/* 响应式布局 */
@media (max-width: 1400px) {
  .time-controls {
    margin-left: 0;
    margin-top: 12px;
    width: 100%;
    justify-content: flex-end;
    max-width: none;
  }
  .countdown-tip {
    position: static;
    transform: none;
    right: auto;
    top: auto;
  }
}

@media (max-width: 1200px) {
  .search-bar {
    flex-wrap: wrap;
    height: auto;
    gap: 8px;
  }
  .search-bar__select {
    width: 180px;
  }
  .search-bar__input {
    min-width: 250px;
  }
  .time-controls {
    margin-left: 0;
    margin-top: 8px;
    width: 100%;
    justify-content: flex-end;
    max-width: none;
  }
  .countdown-tip {
    position: static;
    transform: none;
    right: auto;
    top: auto;
  }
}

@media (max-width: 768px) {
  .search-bar {
    flex-direction: column;
    align-items: stretch;
    flex-wrap: wrap;
    height: auto;
    gap: 12px;
  }
  .search-bar__select {
    width: 100%;
  }
  .search-bar__input {
    width: 100%;
    min-width: auto;
  }
  .search-buttons {
    justify-content: center;
  }
  .time-controls {
    justify-content: center;
    margin-top: 8px;
    flex-direction: column;
    gap: 8px;
  }
  .countdown-tip {
    position: static;
    transform: none;
    right: auto;
    top: auto;
  }
  .filter-bar {
    align-self: center;
  }
}

/* 只影响当前页面的 el-input 后缀内边距 */
:deep(.el-input__wrapper) {
  padding-right: 0 !important;
  margin-right: 0 !important;
}

.quick-analysis {
  width: 260px;
  padding: 16px 8px 8px 8px;
  background: #fff;
  border-right: 1px solid #f7f5f5;
}

.qa-title {
  display: flex;
  justify-content: space-between;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 12px;
}

.qa-search {
  margin-bottom: 16px;
}

.qa-group-title {
  font-size: 14px;
  color: #888;
  margin: 8px 0 8px 0;
}

.qa-collapse {
  background: none;
  border: none;
}

.qa-field-ellipsis {
  display: inline-block;
  max-width: 140px;
  vertical-align: middle;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.qa-field-actions {
  float: right;
  margin-left: 8px;
}

/* 自定义折叠项标题，实现内容两端对齐 */
.custom-collapse-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-grow: 1; /* 让标题容器占据所有剩余空间 */
  padding-right: 16px; /* 与默认箭头保持一些间距 */
}

/* 操作按钮容器 */
.field-actions {
  display: flex;
  align-items: center;
}

.main-content {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
}

.el-card {
  width: 100%;
}

.table-cell-content {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.log-table-wrapper {
  flex: 1;
  min-width: 0;
  padding: 8px 0px 0 8px;
}

.log-tabs {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 0;
  margin-bottom: 8px;
}

.log-tabs-title {
  font-size: 13px;
  margin-right: 10px;
  color: #1277ff;
}

.add-field-btn {
  margin-right: 8px;
  color: #67c23a;
  cursor: pointer;
  transition: color 0.2s;
}

.add-field-btn:hover {
  color: #529b2e;
}

.remove-field-btn {
  margin-right: 8px;
  color: #f56c6c;
  cursor: pointer;
  transition: color 0.2s;
}

.remove-field-btn:hover {
  color: #dd4949;
}

.field-name {
  vertical-align: middle;
}

.field-more-btn {
  margin-left: 8px;
  color: #909399;
  cursor: pointer;
  transition: color 0.2s;
  opacity: 0.6;
}

.field-more-btn:hover {
  color: #606266;
  opacity: 1;
}

/* 直方图 */
.histogram-container {
  width: 100%;
  padding: 5px 16px;
  background-color: #f9f9f9;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  /* margin-bottom: 5px; */
}

.histogram-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.histogram-title {
  font-size: 13px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 4px;
}

.histogram-info {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #666;
}

.histogram-chart {
  width: 100%;
  height: 120px;
  /* background: #fff; */
  border-radius: 4px;
}

/* 字段More悬浮框 */
:deep(.field-more-popover) {
  padding: 8px 0 !important;
  box-sizing: border-box !important;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px 10px 10px;
  border-bottom: 1px solid #ebeef5;
}

.popup-title {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

.popup-close {
  cursor: pointer;
  color: #909399;
  transition: color 0.2s;
}

.popup-close:hover {
  color: #606266;
}

.popup-content {
  padding: 10px 10px 0 10px;
}

.popup-item {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.popup-item:hover {
  background-color: #f5f7fa;
}

.popup-item .el-icon {
  margin-right: 8px;
  color: #409eff;
  font-size: 16px;
}

.popup-item span {
  font-size: 14px;
  color: #606266;
}

.filter-bar {
  display: flex;
  align-items: center;
  width: 40px;
  height: 35px;
  background: #f6f8fa;
  border: 1px solid #e0e3e8;
  overflow: hidden;
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  flex-shrink: 0;
}

.filter-bar__item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 16px;
  color: #1976d2;
  cursor: pointer;
  background: transparent;
  transition: background 0.2s;
}

.filter-bar__item.filter-bar__add {
  width: 40px;
  height: 40px;
  background: #1976d2;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  padding: 0;
  margin-left: 0;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.08);
  transition: background 0.2s;
}

.filter-bar__item.filter-bar__add:hover {
  background: #1565c0;
}

:deep(.custom-filter-popover) {
  padding: 0;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
  min-width: 700px;
  max-width: 900px;
}

/* 搜索条件标签样式 */
.search-conditions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  margin: 12px 0;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.search-condition-tag {
  margin-right: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.search-condition-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 展开内容样式 */
.expanded-content {
  padding: 0px;
  background-color: #f8f8f88a;
}

.field-item {
  display: flex;
  gap: 10px;
  font-size: 14px;
  margin-bottom: 10px;
}

.field-key {
  width: 180px;
}

.field-value {
  flex: 1;
}

/* 折叠状态样式 */
.quick-analysis.collapsed {
  width: 60px;
  min-width: 60px;
  overflow: hidden;
}

.quick-analysis.collapsed .qa-title {
  justify-content: center;
  padding: 10px 0;
}

.quick-analysis.collapsed .qa-title span {
  display: none;
}

/* 左侧列表内容区域 */
.qa-content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 200px);
}

.qa-scrollbar {
  flex: 1;
  overflow: hidden;
}

.qa-scrollbar :deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
}

/* 字段进度条样式 */
.field-progress {
  display: flex;
  align-items: center;
  gap: 3px;
}

.field-progress .el-progress {
  flex: 1;
  margin-right: 8px;
}

.progress-text {
  font-size: 12px;
  color: #409eff;
  font-weight: 500;
  min-width: 35px;
  text-align: right;
}

.loading-text {
  font-size: 12px;
  color: #909399;
  font-style: italic;
}
</style>
