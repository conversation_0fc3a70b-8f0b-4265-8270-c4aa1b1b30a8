<template>
  <div>
    <svg
      t="1753085625365"
      class="icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="7257"
      :width="size"
      :height="size"
    >
      <path
        d="M0 512a512 512 0 0 1 512-512 512 512 0 0 1 512 512 512 512 0 0 1-512 512 512 512 0 0 1-512-512z m73.142857 0a439.369143 439.369143 0 0 0 438.857143 438.857143 439.369143 439.369143 0 0 0 438.857143-438.857143 439.369143 439.369143 0 0 0-438.857143-438.857143 439.369143 439.369143 0 0 0-438.857143 438.857143z m292.571429 219.428571a73.142857 73.142857 0 0 1-73.142857-73.142857V365.714286a73.142857 73.142857 0 0 1 73.142857-73.142857h292.571428a73.142857 73.142857 0 0 1 73.142857 73.142857v292.571428a73.142857 73.142857 0 0 1-73.142857 73.142857z m0-73.142857h292.571428V365.714286H365.714286z"
        fill="#ffffff"
        p-id="7258"
      />
    </svg>
  </div>
</template>

<script lang="ts" setup>
defineProps({
  size: {
    type: String,
    default: "25"
  }
});
</script>
