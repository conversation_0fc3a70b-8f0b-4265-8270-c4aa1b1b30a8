import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

//事件event接口
export const EventList = (data: any) => {
  return http.post(baseUrlApi("event/list"), { data });
};
// 事件创建
export const EventAdd = (data: any) => {
  return http.post(baseUrlApi("event/create"), { data });
};
// 获取事件详情
export const EventDetails = (data: any) => {
  return http.post(baseUrlApi("event/detail"), { data });
};

//事件接入 event_ingestion接口
// 事件接入列表
export const eventIngestionList = (data: any) => {
  return http.post(baseUrlApi("event-ingestion/list"), { data });
};
// 事件接入更新
export const eventIngestionUpdate = (data: any) => {
  return http.post(baseUrlApi("event-ingestion/update"), { data });
};
// 事件接入创建
export const eventIngestionCreate = (data: any) => {
  return http.post(baseUrlApi("event-ingestion/create"), { data });
};
// 事件接入删除
export const eventIngestionDelete = (data: any) => {
  return http.post(baseUrlApi("event-ingestion/delete"), { data });
};
// 事件接入详情
export const eventIngestionDetail = (data: any) => {
  return http.post(baseUrlApi("event-ingestion/detail"), { data });
};

//获取事件接入配置列表
export const eventIngestionEditDetail = (data: any) => {
  return http.post(baseUrlApi("event-ingestion/config/list"), { data });
};

// 事件接入添加
export const eventIngestionAdd = (data: any) => {
  return http.post(baseUrlApi("event-ingestion/new"), { data });
};

//事件接入原始日志
export const eventIngestionOriginalLog = (data: any) => {
  return http.post(baseUrlApi("event-ingestion/sample"), { data });
};

//解析日志
export const eventIngestionParseLog = (data: any) => {
  return http.post(baseUrlApi("event-ingestion/parse"), { data });
};

// 事件生成与绑定剧本
export const eventIngestionBindScript = (data: any) => {
  return http.post(baseUrlApi("event-ingestion/binding"), { data });
};

// 解绑事件接入和剧本
export const eventIngestionUnbindScript = (data: any) => {
  return http.post(baseUrlApi("event-ingestion/unbundled"), { data });
};

// 导入事件接入脚本
export const eventIngestionImport = (formData: FormData) => {
  return http.post(baseUrlApi("event-ingestion/import"), undefined, {
    headers: {
      "Content-Type": "multipart/form-data"
    },
    data: formData
  });
};

// 导出事件接入脚本
export const eventIngestionExport = (data: any) => {
  return http.post(
    baseUrlApi("event-ingestion/export"),
    { data },
    {
      responseType: "blob"
    }
  );
};

// 更新事件
export const eventUpdate = (data: any) => {
  return http.post(baseUrlApi("event/update"), { data });
};

// 获取transforms列表
export const eventIngestionTransformsList = (data: any) => {
  return http.post(baseUrlApi("event-ingestion/transforms-list"), { data });
};
