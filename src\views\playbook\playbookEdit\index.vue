<!-- eslint-disable prettier/prettier -->
<template>
  <div class="logicflow-chart">
    <div class="logicflow-card shadow-effect">
      <!-- 底部工具栏 -->
      <logicflowToolbar
        v-if="showLf"
        ref="logicflowToolbarRef"
        :lf="lf"
        :playbookId="playbookId"
        class="logicflow-toolbar"
        @openWebSocket="openWebSocket()"
      />
      <!-- 右上菜单栏 -->
      <logicflowMenu
        v-if="showLf"
        ref="logicflowMenuRef"
        :lf="lf"
        class="logicflow-menu"
        @openReleaseDialog="openReleaseDialog()"
        @openUpdateDialog="openUpdateDialog()"
        @switchPlaybookVersion="switchPlaybookVersion"
      />
      <!-- 流程图主体 -->
      <!-- 画布 -->
      <div ref="containerRef" class="logicflow-lf" />
    </div>

    <!-- 各个节点的drawer -->
    <startNodeDrawer ref="startNodeDrawerRef" :lf="lf" />
    <actionNodeDrawer ref="actionNodeDrawerRef" :lf="lf" />
    <judgeNodeDrawer ref="judgeNodeDrawerRef" :lf="lf" />
    <approvalNodeDrawer ref="approvalNodeDrawerRef" :lf="lf" />
    <aggregationNodeDrawer ref="aggregationNodeDrawerRef" :lf="lf" />
    <subscriptNodeDrawer ref="subscriptNodeDrawerRef" :lf="lf" />
    <loopBodyNodeDrawer ref="loopBodyNodeDrawerRef" :lf="lf" />

    <!-- 执行剧本前，确认用户输入 -->
    <el-dialog
      v-model="playbookStore.isShowFlowInputDialog"
      title="请确认用户输入"
    >
      <el-form>
        <el-form-item
          v-for="(item, index) in playbookStore.startFlow_input"
          :key="index"
        >
          <div>{{ item.description }}</div>
          <el-input v-model="playbookStore.flowInput[item.key]" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="playbookStore.isShowFlowInputDialog = false"
            >取消
          </el-button>
          <el-button type="primary" @click="openWebSocket()">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看节点运行结果 -->
    <SSEResultDialog v-model="isShowWebSocketDialog" :SSEResult="SSEResult" />

    <!-- <el-dialog v-model="isShowWebSocketDialog" title="查看结果">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="开始时间">{{SSEResult.start_time}}</el-descriptions-item>
        <el-descriptions-item label="结束时间">{{SSEResult.end_time}}</el-descriptions-item>
        <el-descriptions-item label="应用名称">{{SSEResult.tool_display_name}}</el-descriptions-item>
        <el-descriptions-item label="应用版本">{{SSEResult.tool_version}}</el-descriptions-item>
        <el-descriptions-item label="动作名称">{{SSEResult.action_display_name}}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <div class="info-value">
            <span
              :class="getStatusClass(SSEResult.status)"
              class="status-tag"
              >{{ getStatusText(SSEResult.status) }}
            </span>
          </div></el-descriptions-item>
        <el-descriptions-item :span="2" label="输入参数">{{ SSEResult.args || "无输入参数"}}</el-descriptions-item>
        <el-descriptions-item :span="2" label="结果">{{SSEResult.result}}</el-descriptions-item>
      </el-descriptions>
    </el-dialog> -->

    <!-- 发布剧本版本变更说明填写dialog -->
    <el-dialog
      v-model="isShowReleaseDialog"
      :modal="false"
      style="margin-right: 30px; margin-top: 120px"
      width="20%"
    >
      <template #header>
        <div>请输入版本变更说明</div>
      </template>
      <el-form
        ref="changeDescRef"
        :model="changeDescForm"
        :rules="changeDescFormRules"
      >
        <el-form-item prop="changeDesc">
          <el-input v-model="changeDescForm.changeDesc" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div>
          <el-button @click="closeReleaseDialog()">取消</el-button>
          <Perms :value="['workflow:u']">
            <el-button type="primary" @click="releasePlaybook(changeDescRef)"
              >发布
            </el-button>
          </Perms>
        </div>
      </template>
    </el-dialog>

    <!-- 剧本基本信息编辑dialog -->
    <updatePlaybookDialog ref="updatePlaybookDialogRef" />
  </div>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, reactive, ref } from "vue";
import logicflowToolbar from "./components/logicflow/logicflowToolbar.vue";
import logicflowMenu from "./components/logicflow/logicflowMenu.vue";
import LogicFlow from "@logicflow/core";
import { DynamicGroup, MiniMap, SelectionSelect } from "@logicflow/extension";
import "@logicflow/core/lib/style/index.css";
import "@logicflow/extension/lib/style/index.css";
import { registerCustomElement } from "./node/index";
import { ContextPad } from "./components/contextPad/contextPad";
import setContextPad from "./components/contextPad/setContextPad";
import startNodeDrawer from "./components/drawer/startNodeDrawer.vue";
import actionNodeDrawer from "./components/drawer/actionNodeDrawer.vue";
import judgeNodeDrawer from "./components/drawer/judgeNodeDrawer.vue";
import approvalNodeDrawer from "./components/drawer/approvalNodeDrawer.vue";
import aggregationNodeDrawer from "./components/drawer/aggregationNodeDrawer.vue";
import subscriptNodeDrawer from "./components/drawer/subscriptNodeDrawer.vue";
import loopBodyNodeDrawer from "./components/drawer/loopBodyNodeDrawer.vue";
import { useDetail } from "@/views/playbook/hooks";
import {
  apiGetPlaybookVersionsList,
  apiPlaybookEditFlow,
  apiPlaybookPublish
} from "@/api/playbook";
import usePlaybookStore from "@/store/modules/playbook";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { useRouter } from "vue-router";
import updatePlaybookDialog from "@/views/playbook/components/playbookDialog/updatePlaybookDialog.vue";
import { baseUrlApi } from "@/api/utils";
import { fetchEventSource } from "@microsoft/fetch-event-source";
import SSEResultDialog from "./components/SSEResultDialog.vue";

interface changeDescFormType {
  changeDesc: string;
}

const lf = ref<LogicFlow>();
const containerRef = ref();
const logicflowToolbarRef = ref();
const logicflowMenuRef = ref();
const logicflowSidebarRef = ref();
const startNodeDrawerRef = ref();
const actionNodeDrawerRef = ref();
const judgeNodeDrawerRef = ref();
const approvalNodeDrawerRef = ref();
const aggregationNodeDrawerRef = ref();
const subscriptNodeDrawerRef = ref();
const loopBodyNodeDrawerRef = ref();
const showLf = ref(false);
const isShowSidebar = ref(true);
const leftPosition = ref(12);
const isShowWebSocketDialog = ref(false);
const playbookStore = usePlaybookStore();
const graphData = ref();
const id = JSON.parse(localStorage.getItem("user-info")).id;
const router = useRouter();
const isShowPlaybookVersionList = ref(false);
const playbookDetail = ref();
const playbookId = ref();
const playbookVersionId = ref();
const playbookStatus = ref();
const playbookList = ref([]);
const playbookData = ref();
const playbookVersionRef = ref();
const isShowReleaseDialog = ref();
const changeDescForm = ref<changeDescFormType>({
  changeDesc: ""
});
const changeDescRef = ref();
const flowInput = ref<any>({});
const updatePlaybookDialogRef = ref();
const activeNames = ref(["1", "2"]);
const SSEResult = ref();
const hasMore = ref(true);
const page = ref(1);
const playbookPermissions = JSON.parse(
  localStorage.getItem("playbookPermissions")
);

//初始数据
let data = {
  nodes: [
    {
      type: "start-node",
      resizable: false,
      x: 100,
      y: 100
    },
    {
      type: "end-node",
      resizable: false,
      x: 1000,
      y: 100
    }
  ]
};

//获取剧本ID、剧本版本ID和剧本状态
const { initToDetail, getParameter, toDetail } = useDetail();
initToDetail("params");
playbookId.value = getParameter.flow_id;
playbookVersionId.value = getParameter.version_id;
playbookStatus.value = getParameter.status;

const SilentConfig = {
  stopZoomGraph: false, // 禁止缩放
  stopScrollGraph: true // 禁止鼠标滚动移动画布
};

// const getPlaybookDetail = async () => {
//   let res: any = await apiGetPlaybookDetail({ playbook_id: playbookId.value });
//   console.log(res);
//   playbookDetail.value = res.data;
//   console.log(playbookDetail.value);
// };
// getPlaybookDetail();
//小地图配置
const miniMapOptions: MiniMap.MiniMapOption = {
  isShowHeader: false,
  isShowCloseIcon: false,
  headerTitle: "小地图",
  showEdge: true,
  width: 200,
  height: 120
};

// const ws = ref<WebSocket | null>(null);

onMounted(() => {
  initLogicFlow(data);
});
//初始化流程图
const initLogicFlow = async (data: any) => {
  //获取剧本的详细信息
  let res: any = await apiGetPlaybookVersionsList({
    playbook_id: playbookId.value
  });
  const result = res.data.find(
    item => item.version_id === playbookVersionId.value
  );
  playbookDetail.value = result;
  console.log(playbookDetail.value);
  LogicFlow.use(SelectionSelect); //选区
  LogicFlow.use(MiniMap); //小地图
  if (playbookPermissions[playbookId.value].includes("u")) {
    lf.value = new LogicFlow({
      container: containerRef.value,
      background: {
        backgroundColor: "white"
      },
      plugins: [DynamicGroup, ContextPad],
      allowResize: false, //全局的节点缩放配置
      allowRotate: false, //全局的节点旋转配置
      //键盘快捷键
      keyboard: {
        enabled: false,
        shortcuts: [
          {
            keys: ["backspace", "delete", "ctrl+c", "ctrl+v"], // 禁用Backspace、Delete等快捷键
            callback: () => {
              // 空操作：直接返回，不执行删除
            }
          }
        ]
      },
      grid: {
        visible: false,
        size: 15
      },
      pluginsOptions: {
        miniMap: miniMapOptions
      },
      ...SilentConfig
    });
  } else {
    lf.value = new LogicFlow({
      container: containerRef.value,
      background: {
        backgroundColor: "white"
      },
      plugins: [DynamicGroup, ContextPad],
      //键盘快捷键
      keyboard: {
        enabled: false,
        shortcuts: [
          {
            keys: ["backspace", "delete", "ctrl+c", "ctrl+v"], // 禁用Backspace、Delete等快捷键
            callback: () => {
              // 空操作：直接返回，不执行删除
            }
          }
        ]
      },
      grid: {
        visible: false,
        size: 15
      },
      pluginsOptions: {
        miniMap: miniMapOptions
      },
      isSilentMode: true
    });
  }

  showLf.value = true;

  // 为 LogicFlow 实例添加安全的 dynamicGroup 访问方法
  if (lf.value) {
    (lf.value as any).safeGetGroupByNodeId = (nodeId: string) => {
      if (!lf.value?.graphModel?.dynamicGroup) {
        console.warn(
          "DynamicGroup 插件未初始化，无法调用 getGroupByNodeId 方法"
        );
        return null;
      }
      return lf.value.graphModel.dynamicGroup.getGroupByNodeId(nodeId);
    };

    // 重写 DynamicGroup 的 getGroupByNodeId 方法以添加安全检查
    if (lf.value.graphModel?.dynamicGroup) {
      const originalGetGroupByNodeId =
        lf.value.graphModel.dynamicGroup.getGroupByNodeId;
      lf.value.graphModel.dynamicGroup.getGroupByNodeId = function (
        nodeId: string
      ) {
        try {
          return originalGetGroupByNodeId.call(this, nodeId);
        } catch (error) {
          console.warn(`获取节点 ${nodeId} 的分组信息时发生错误:`, error);
          return null;
        }
      };
    }
  }

  registerCustomElement(lf.value); //注册自定义节点和边
  setContextPad(lf.value); //注册快捷菜单栏
  lf.value.setDefaultEdgeType("vue-edge"); //边的类型
  //如果是已有剧本，则渲染已有剧本的流程图信息，否则默认为初始数据
  if (Object.entries(playbookDetail.value.flow_json).length > 0) {
    // playbookDetail.value.flow_json.nodes.forEach(node => {
    //   // //基于锚点的位置更新边的路径
    //   // lf.value.getNodeModelById(node.id).updateField();
    //   //在渲染流程图之前，将除开始节点和结束节点之外的节点初始化
    //   if (node.type !== "start-node" && node.type !== "end-node") {
    //     node.properties.isWebSocket = false;
    //     node.properties.scale = 1;
    //     node.properties.height = 100;
    //   }
    // });
    lf.value.render(playbookDetail.value.flow_json);
    //将流程图数据缓存到localStorage中
    localStorage.setItem(
      "flow_json",
      JSON.stringify(playbookDetail.value.flow_json)
    );
    playbookStore.flow_json = playbookDetail.value.flow_json;
    console.log(playbookDetail.value);
  } else {
    lf.value.render(data);
    //将流程图数据缓存到localStorage中
    localStorage.setItem("flow_json", JSON.stringify(data));
    playbookStore.flow_json = data;
  }
  //在流程图数据渲染完成后，将开始节点的ID和剧本名称保存到playbookStore
  graphData.value = lf.value.getGraphData();
  graphData.value.nodes.forEach(node => {
    if (node.type === "start-node") {
      playbookStore.startNodeId = node.id;
    }
  });
  playbookStore.playbookName = playbookDetail.value.name;
  lf.value.translateCenter(); // 将图形移动到画布中央
  (lf.value.extension.miniMap as any).show(); //bug，不知道为什么会报错，但是不影响正常使用小地图，断言为any之后不报错了
  console.log("lf:", lf.value);
  //右键打开el-drawer抽屉
  if (playbookPermissions[playbookId.value].includes("u")) {
    lf.value.on("node:contextmenu", props => {
      switch (props.data.type) {
        case "start-node":
          startNodeDrawerRef.value.openNodeEditDrawer(props.data);
          break;
        case "action-node":
          actionNodeDrawerRef.value.openNodeEditDrawer(props.data);
          break;
        case "judge-node":
          judgeNodeDrawerRef.value.openNodeEditDrawer(props.data);
          break;
        case "approval-node":
          approvalNodeDrawerRef.value.openNodeEditDrawer(props.data);
          break;
        case "aggregation-node":
          aggregationNodeDrawerRef.value.openNodeEditDrawer(props.data);
          break;
        case "subscript-node":
          subscriptNodeDrawerRef.value.openNodeEditDrawer(props.data);
          break;
        case "loop-body":
          loopBodyNodeDrawerRef.value.openNodeEditDrawer(props.data);
          break;
        default:
          console.log("该节点没有drawer");
      }
      //恢复保存按钮的使用并保存流程图数据至localStorage中
      playbookStore.isFlowChanged = true;
      playbookStore.flow_json = lf.value.getGraphData();
      localStorage.setItem(
        "flow_json",
        JSON.stringify(playbookStore.flow_json)
      );
    });
  }

  //复制节点
  lf.value.on("custom:onBtnCopyClick", ({ props }) => {
    lf.value.cloneNode(props.model.id);
    //恢复保存按钮的使用并保存流程图数据至localStorage中
    playbookStore.isFlowChanged = true;
    playbookStore.flow_json = lf.value.getGraphData();
    localStorage.setItem("flow_json", JSON.stringify(playbookStore.flow_json));
  });
  //删除节点
  lf.value.on("custom:onBtnDelClick", ({ props }) => {
    console.log(props);
    if (props.model.type === "cycle-node") {
      lf.value.deleteNode(props.model.id);
      const { nodes } = lf.value.getGraphData() as LogicFlow.GraphData;
      nodes.forEach(item => {
        if (item.properties.cycleId === props.model.id) {
          lf.value.deleteNode(item.id);
        }
      });
    } else {
      lf.value.deleteNode(props.model.id);
    }
    //恢复保存按钮的使用并保存流程图数据至localStorage中
    playbookStore.isFlowChanged = true;
    playbookStore.flow_json = lf.value.getGraphData();
    localStorage.setItem("flow_json", JSON.stringify(playbookStore.flow_json));
    // //来自ts的神秘判定，不加any找不到方法
    // (lf.value.extension.contextPad as any).hideContextMenu();
  });
  //监听用户点击清空执行结果按钮
  lf.value.on("custom:onBtnCloseClick", ({ props }) => {
    console.log("props:", props);
    // 获取流程绘图数据
    graphData.value = lf.value.getGraphData();
    //过滤开始节点和结束节点
    const nodesToUpdate = graphData.value.nodes.filter(
      (item: any) => item.type !== "start-node" && item.type !== "end-node"
    );
    nodesToUpdate.forEach((item: any) => {
      if (item.properties.isWebSocket) {
        // 清空运行结果并恢复节点的高度
        lf.value.setProperties(item.id, {
          isWebSocket: false,
          scale: 1
        });
        // 调用自定义方法向上移动节点，防止因缩小节点高度导致锚点和边错位
        const nodeModel = lf.value.getNodeModelById(item.id);
        if (typeof nodeModel?.resetField === "function") {
          nodeModel.resetField();
        } else {
          console.log(`节点ID${item.id}无updateField方法`);
        }
      }
    });
  });
  //监听用户点击websocket结果展示dialog
  lf.value.on("custom:onBtnWebSocket", ({ props }) => {
    isShowWebSocketDialog.value = true;
    console.log("isShowWebSocketDialog:", isShowWebSocketDialog.value);
    playbookStore.SSEResult.forEach(item => {
      if (item.node_id === props.model.id) {
        SSEResult.value = item;
        console.log("SSEResult.value:", SSEResult.value);
      }
    });
  });

  //监听循环体节点收缩/展开
  lf.value.on("custom:onToggleCollapse", ({ props }) => {
    const nodeModel = lf.value.getNodeModelById(props.model.id);
    if (nodeModel && typeof nodeModel.toggleCollapse === "function") {
      nodeModel.toggleCollapse();
    }
    //恢复保存按钮的使用并保存流程图数据至localStorage中
    playbookStore.isFlowChanged = true;
    playbookStore.flow_json = lf.value.getGraphData();
    localStorage.setItem("flow_json", JSON.stringify(playbookStore.flow_json));
  });
  // 监听多个事件(在用户执行这些操作后，恢复保存按钮的使用)
  lf.value.on("node:contextmenu,edge:click,node:drag,edge:add", () => {
    //恢复保存按钮的使用并保存流程图数据至localStorage中
    playbookStore.isFlowChanged = true;
    playbookStore.flow_json = lf.value.getGraphData();
    localStorage.setItem("flow_json", JSON.stringify(playbookStore.flow_json));
  });
  // 监听节点加入分组事件
  lf.value.on("group:add-node", ({ data, childId }) => {
    console.log(`节点 ${childId} 已加入分组 ${data.id}`);
  });

  // 监听节点移出分组事件
  lf.value.on("group:remove-node", ({ data, childId }) => {
    console.log(`节点 ${childId} 已从分组 ${data.id} 中移除`);
  });

  // 监听图形渲染完成事件，处理 DynamicGroup 相关错误
  lf.value.on("graph:rendered", () => {
    // 确保 DynamicGroup 插件正确初始化
    if (lf.value?.graphModel?.dynamicGroup) {
      // 添加错误处理
      const originalGetGroupByNodeId =
        lf.value.graphModel.dynamicGroup.getGroupByNodeId;
      if (originalGetGroupByNodeId) {
        lf.value.graphModel.dynamicGroup.getGroupByNodeId = function (
          nodeId: string
        ) {
          try {
            return originalGetGroupByNodeId.call(this, nodeId);
          } catch (error) {
            console.warn(`获取节点 ${nodeId} 的分组信息时发生错误:`, error);
            return null;
          }
        };
      }
    }
  });

  // 统一开关函数
  function toggleRestrict(enable: boolean) {
    lf.value.graphModel.nodes.forEach(node => {
      const model = lf.value.getNodeModelById(node.id);
      console.log("model:", model);
      // 只对分组节点操作
      if (model && model.isGroup) {
        model.isRestrict = enable;
      }
    });
  }

  // 键盘按下
  window.addEventListener("keydown", e => {
    if (e.ctrlKey && e.repeat === false) {
      toggleRestrict(false); // 放开限制
    }
  });

  // 键盘抬起
  window.addEventListener("keyup", e => {
    if (!e.ctrlKey) {
      toggleRestrict(true); // 恢复限制
    }
  });

  // 添加全局错误处理，捕获 DynamicGroup 相关错误
  const originalErrorHandler = window.onerror;
  window.onerror = function (message, source, lineno, colno, error) {
    if (
      message &&
      typeof message === "string" &&
      message.includes("getGroupByNodeId")
    ) {
      console.warn("捕获到 DynamicGroup 相关错误，已忽略:", message);
      return true; // 阻止错误继续传播
    }
    // 调用原始错误处理器
    if (originalErrorHandler) {
      return originalErrorHandler.call(
        this,
        message,
        source,
        lineno,
        colno,
        error
      );
    }
    return false;
  };
};

/**
 * 更新所有节点的前置节点列表（排除自身）
 * @param lf - LogicFlow 实例
 */
const updateNodeUpstreamList = (lf: LogicFlow) => {
  const graphData: LogicFlow.GraphConfigData = lf.getGraphData();
  const nodes = graphData.nodes as LogicFlow.NodeData[];
  const edges = graphData.edges as LogicFlow.EdgeData[];

  // 构建邻接表（target -> sources）
  const adjacencyMap: Record<string, string[]> = {};
  edges.forEach(edge => {
    const source = edge.sourceNodeId;
    const target = edge.targetNodeId;
    if (!adjacencyMap[target]) {
      adjacencyMap[target] = [];
    }
    adjacencyMap[target].push(source);
  });

  // 深度优先搜索找出所有上游节点
  const visited = new Set<string>();
  const upstreamMap: Record<string, string[]> = {};

  const dfs = (nodeId: string): string[] => {
    if (visited.has(nodeId)) return [];
    visited.add(nodeId);

    const result: string[] = [];

    const parents = adjacencyMap[nodeId] || [];
    parents.forEach(parent => {
      result.push(parent);
      const grandParents = dfs(parent);
      grandParents.forEach(gp => {
        if (!result.includes(gp)) result.push(gp);
      });
    });

    return result;
  };

  // 遍历所有节点并更新 prevNodes（不含自己）
  nodes.forEach(node => {
    if (node.type === "start-node" || node.type === "end-node") return;

    visited.clear(); // 清空 visited 集合
    const upstreamList = dfs(node.id).reverse(); // 反转以保持 A → B → C 的顺序

    lf.setProperties(node.id, {
      ...node.properties,
      prevNodes: upstreamList
    });
  });

  console.log("前置节点列表已更新");
};

// //自定义节点拖拽
// const $_dragInNode = (type: string) => {
//   lf.value.dnd.startDrag({ type });
// };

// //获取全部的工具
// const getToolList = async () => {
//   while (hasMore.value) {
//     let res: any = await apiGetToolList({
//       page: page.value,
//       size: 50
//     });
//     if (res.data.total_pages < page.value) {
//       hasMore.value = false;
//       break;
//     }
//     playbookStore.addAllToolList(res.data.tools); // 收集所有工具
//     page.value++;
//   }

//   // 所有数据收集完成后，一次性设置到 store 中
//   console.log("playbookStore.allToolList:", playbookStore.allToolList);
// };

// //返回剧本管理模块
// const goBack = () => {
//   router.back();
// };

//打开发布剧本版本变更说明dialog
const openReleaseDialog = () => {
  changeDescForm.value.changeDesc = "";
  isShowReleaseDialog.value = true;
  //清空残留的表单校验
  nextTick(() => {
    changeDescRef.value.clearValidate(); // 只清除清除验证
  });
};

//关闭发布剧本版本变更说明dialog
const closeReleaseDialog = () => {
  isShowReleaseDialog.value = false;
};

//发布剧本
const releasePlaybook = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      let res: any = await apiPlaybookPublish({
        flow_id: playbookVersionId.value,
        change_desc: changeDescForm.value.changeDesc
      });
      console.log(res);
      if (res.code == 0) {
        ElMessage({
          type: "success",
          message: "发布成功"
        });
        router.push({ name: "playbookIndex" });
      } else {
        ElMessage.error("发布失败");
      }
    } else {
      console.log("error submit!", fields);
    }
  });
};

//版本变更说明表单校验规则
const changeDescFormRules = reactive<FormRules<changeDescFormType>>({
  changeDesc: [
    {
      required: true,
      message: "版本变更说明不能为空",
      trigger: "change"
    }
  ]
});

//切换剧本版本并重新渲染流程图数据
const switchPlaybookVersion = item => {
  console.log(item);
  //切换版本前先清空之前的用户输入
  playbookStore.flow_inputName = [];
  playbookStore.flow_input = {};
  //切换版本前将保存按钮设置为禁用状态
  playbookStore.isFlowChanged = false;
  toDetail(
    {
      flow_id: item.flow_id,
      version_id: item.version_id,
      status: item.status
    },
    "params"
  );
};

//打开剧本基本信息编辑dialog
const openUpdateDialog = () => {
  //判断是否是当前的流程图的剧本基本信息
  if (updatePlaybookDialogRef.value) {
    updatePlaybookDialogRef.value.openDialog(playbookStore.playbookData);
  }
};

//无论是否有用户输入，点击执行按钮后都必须处理的逻辑
//1.保存数据 2.更新节点大小 3.向webSocket服务端发送信息
const openWebSocket = async () => {
  //关闭dialog
  playbookStore.isShowFlowInputDialog = false;
  // =========================================================================================
  //1.保存数据
  //在保存数据之前，将有执行结果的节点先恢复原本的状态
  // 获取流程绘图数据
  graphData.value = lf.value.getGraphData();
  //过滤开始节点和结束节点
  const nodesToUpdate = graphData.value.nodes.filter(
    (item: any) => item.type !== "start-node" && item.type !== "end-node"
  );
  nodesToUpdate.forEach((item: any) => {
    if (item.properties.isWebSocket == true) {
      // 清空运行结果并恢复节点的高度
      lf.value.setProperties(item.id, {
        isWebSocket: false,
        scale: 1,
        height: 100
      });
      // 调用自定义方法向上移动节点，防止因缩小节点高度导致锚点和边错位
      const nodeModel = lf.value.getNodeModelById(item.id);
      if (typeof nodeModel?.resetField === "function") {
        nodeModel.resetField();
      } else {
        console.log(`节点ID${item.id}无updateField方法`);
      }
    }
  });
  console.log(playbookStatus.value);
  //如果是草稿剧本，则保存流程图数据(已发布剧本则不保存数据)
  if (playbookStatus.value == 0) {
    //保存并上传剧本数据
    let res: any = await apiPlaybookEditFlow({
      flow_id: playbookVersionId.value,
      flow_json: lf.value.getGraphData()
    });
  } else if (playbookStatus.value == 1 && playbookStore.isFlowChanged == true) {
    //保存并上传剧本数据
    let res: any = await apiPlaybookEditFlow({
      flow_id: playbookVersionId.value,
      flow_json: lf.value.getGraphData()
    });
    if (res.code === 0) {
      ElMessage.success("保存成功,已为你自动生成新的版本,正在跳转中");
    }
    //切换版本前将保存按钮设置为禁用状态
    playbookStore.isFlowChanged = false;
    try {
      toDetail(
        {
          flow_id: res.data.flow_id,
          version_id: res.data.version_id,
          status: 0
        },
        "params"
      );
      // // 等待下一个 tick，确保路由变化和组件更新完成
      // await nextTick();
      // setTimeout(() => {
      //   handleRunlog();
      //   console.log("playbookVersionId.value:", playbookVersionId.value);
      // }, 5000);

      // // 确保在跳转路由后触发子组件的执行按钮方法
      // if (
      //   logicflowToolbarRef.value &&
      //   typeof logicflowToolbarRef.value.$_start === "function"
      // ) {
      //   logicflowToolbarRef.value.$_start();
      //   return;
      // } else {
      //   console.error("logicflowToolbarRef或其方法$_start不存在");
      // }
    } catch (error) {
      console.error("路由跳转过程中发生错误:", error);
    }
    return;
  }
  // =========================================================================================
  //2.向webSocket服务端发送信息并接受服务端返回的信息并更新节点的领域大小，并显示运行结果框
  handleRunlog();
  //启用清空剧本执行结果按钮
  playbookStore.isPlaybookStart = true;
  //剧本正在执行中
  if (playbookStore.startFlow_input.length !== 0) {
    ElMessage.success("开始执行");
  }
  playbookStore.isPlaybookStarting = true;
};

// 更新单个节点的领域大小，并显示运行结果框
const updateNodeField = (
  lf: any,
  nodeId: string,
  nodeType: string
): Promise<void> => {
  return new Promise((resolve, reject) => {
    // 跳过开始节点和结束节点
    if (nodeType === "start-node" || nodeType === "end-node") {
      resolve();
      return;
    }

    // 获取节点模型
    const nodeModel = lf.getNodeModelById(nodeId);
    if (!nodeModel) {
      reject(new Error(`节点ID ${nodeId} 不存在`));
      return;
    }

    // 显示运行结果并扩大节点的高度
    lf.setProperties(nodeId, {
      isWebSocket: true,
      scale: 1.3
    });

    // 调用自定义方法向下移动节点
    if (typeof nodeModel.updateField === "function") {
      try {
        nodeModel.updateField();
        resolve();
      } catch (error) {
        reject(error);
      }
    } else {
      reject(new Error(`节点ID ${nodeId} 无 updateField 方法`));
    }
  });
};

// SSE通信连接
const handleRunlog = () => {
  const controller = new AbortController();
  playbookStore.setController(controller);
  const SSEData = [];
  let isTimeout = false;

  // 创建一个20秒的超时定时器
  const timeoutId = setTimeout(() => {
    isTimeout = true;
    console.log("SSE连接超时，准备中止连接");
    if (controller && !controller.signal.aborted) {
      controller.abort();
    }
  }, 20000);

  fetchEventSource(baseUrlApi("playbook/debug"), {
    method: "POST",
    signal: controller.signal, // 只使用controller的信号
    body: JSON.stringify({
      type: "flow",
      flow_id: playbookStore.version_id,
      flow_input: playbookStore.flowInput
    }),
    onmessage({ data }) {
      console.log("原始数据:", data); // 调试输出
      try {
        const parsed = JSON.parse(data);
        console.log("解析后的数据:", parsed);
        SSEData.push(parsed);

        playbookStore.SSEResult = SSEData;
        if (
          parsed.node_type !== "start-node" &&
          parsed.node_type !== "end-node"
        ) {
          updateNodeField(lf.value, parsed.node_id, parsed.node_type);
        }
        if (parsed.node_type === "end-node") {
          clearTimeout(timeoutId); // 清除超时定时器
          playbookStore.isPlaybookStarting = false;
          ElMessage.success("剧本执行完成。");
        }
      } catch (e) {
        console.error("JSON 解析失败:", e.message, "数据内容:", data);
        ElMessage.error("接收到无法解析的数据格式。");
      }
    },
    onclose() {
      clearTimeout(timeoutId); // 清除超时定时器
      // 连接正常关闭时（非错误），给出提示
      if (playbookStore.isPlaybookStarting) {
        ElMessage.info("剧本执行连接已关闭。");
        playbookStore.isPlaybookStarting = false;
      }
    },
    onerror(err) {
      clearTimeout(timeoutId); // 清除超时定时器

      // 如果是用户手动中止，store里已经提示过了，直接抛出错误停止即可
      if (controller.signal.aborted && !isTimeout) {
        playbookStore.isPlaybookStarting = false;
        throw err;
      }

      // 检查是否是超时错误
      if (err.name === "AbortError" && isTimeout) {
        ElMessage.error("剧本执行超时（20秒），已自动取消执行状态。");
        playbookStore.handleTimeout(); // 使用专门的超时处理方法
      } else if (err.name === "AbortError") {
        ElMessage.error("剧本执行连接超时，请检查网络或服务状态。");
        playbookStore.isPlaybookStarting = false;
      } else {
        // 其他类型的错误
        ElMessage.error("剧本执行连接失败，请检查网络或服务器状态。");
        playbookStore.isPlaybookStarting = false;
      }

      console.error("SSE 连接发生错误:", err);
      // 必须抛出错误才会停止库的自动重连
      throw err;
    }
  });
  playbookStore.SSEResult = SSEData;
  console.log(playbookStore.SSEResult);
};

// const getStatusClass = status => {
//   return status === 0 ? "success" : "fail";
// };

// const getStatusText = status => {
//   return status === 0 ? "成功" : "失败";
// };
</script>

<style lang="scss" scoped>
.logicflow-chart {
  //width: 100%;
  //position: absolute;
  //display: flex;
  //height: 96%;
  height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  background-color: #ffffff;

  .logicflow-card {
    position: relative;
    height: 100%;

    .logicflow-toolbar {
      position: absolute;
      z-index: 999;
      left: 40%;
      bottom: 0%;
    }

    .logicflow-menu {
      position: absolute;
      z-index: 999;
      top: 0px;
      width: 100%;
    }

    .logicflow-sidebar {
      position: absolute;
      z-index: 999;
      left: 0px;
      top: 0px;
    }

    .logicflow-goback {
      position: absolute;
      z-index: 999;
      left: 10px;
      top: 10px;
      cursor: pointer;
      width: 50px;
      height: 50px;
    }

    .logicflow-lf {
      height: 100%;
    }
  }
}

.info-value {
  font-size: 16px;
  color: #333;
}

.status-tag {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  color: #fff;
}

.success {
  background: #52c41a;
}

.fail {
  background: #f5222d;
}

// /* 覆盖默认的变量以设置内边距为 0 */
// :deep(.el-card__body) {
//   --el-card-padding: 0;
//   padding: var(--el-card-padding);
// }

/* 提取阴影效果 */
.shadow-effect {
  box-shadow: var(--el-box-shadow-light);
}
</style>
