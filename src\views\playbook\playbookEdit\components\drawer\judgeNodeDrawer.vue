<template>
  <div>
    <!-- 判断节点drawer -->
    <el-drawer v-model="isShowDrawer" size="50%">
      <template #header>
        <div class="action-drawer-title">节点信息</div>
      </template>
      <template #default>
        <div class="judge-node-drawer-body">
          <el-form
            ref="nodeDataFormRef"
            :model="nodeData.properties"
            :rules="NodeDataFormRules"
          >
            <el-form-item label="节点ID:">
              <el-button link type="primary" @click="$_copyNodeId(nodeData.id)"
                >{{ nodeData.id }}
              </el-button>
            </el-form-item>
            <el-form-item
              label="节点标题:"
              label-position="right"
              label-width="100px"
              prop="node_name"
            >
              <el-input v-model="nodeData.properties.node_name" />
            </el-form-item>
          </el-form>

          <div class="dialog-content-multi">
            <div
              v-for="(formData, idx) in formDataList"
              :key="idx"
              class="row-group"
            >
              <!-- 左侧表单 -->
              <div class="form-col">
                <el-form
                  :model="formData"
                  class="form-left"
                  label-position="right"
                  label-width="100px"
                >
                  <!-- 判断条件 -->
                  <el-form-item label="判断条件：" prop="judge">
                    <el-card
                      class="condition-builder"
                      shadow="never"
                      style="padding: 0px"
                    >
                      <div class="conditions-container">
                        <!-- 条件列表 -->
                        <div
                          v-if="formData.length > 0"
                          class="condition-section"
                        >
                          <div
                            v-for="(condition, index) in formData"
                            :key="index"
                            class="condition-item"
                          >
                            <div class="condition-row">
                              <!-- 原始布局的connect_type下拉 -->
                              <el-select
                                v-if="index > 0"
                                v-model="condition.connect_type"
                                class="condition-connect"
                                placeholder="选择判断条件"
                              >
                                <el-option
                                  v-for="option in getConnectTypes()"
                                  :key="option.value"
                                  :label="option.label"
                                  :value="option.value"
                                />
                              </el-select>
                              <div v-else style="width: 60px" />

                              <!-- 其他条件字段保持原有布局 -->
                              <div class="condition-name">
                                <UuidDisplayInput
                                  v-model="condition.name"
                                  :lf="props.lf"
                                />
                              </div>

                              <el-select
                                v-model="condition.type"
                                class="condition-type"
                                placeholder="选择条件类型"
                              >
                                <el-option
                                  v-for="option in getConditionTypes()"
                                  :key="option.value"
                                  :label="option.label"
                                  :value="option.value"
                                />
                              </el-select>
                              <template v-if="condition.type === 'is_empty'">
                                <el-input
                                  v-model="condition.value"
                                  :disabled="true"
                                  class="condition-value"
                                  placeholder="值"
                                />
                              </template>
                              <template v-else>
                                <div class="condition-name">
                                  <UuidDisplayInput
                                    v-model="condition.value"
                                    :lf="props.lf"
                                  />
                                </div>
                              </template>

                              <el-button
                                circle
                                type="danger"
                                @click="removeCondition(idx, index)"
                              >
                                <el-icon>
                                  <Delete />
                                </el-icon>
                              </el-button>
                            </div>
                          </div>
                        </div>
                        <!-- 添加条件按钮 -->
                        <div class="add-condition-wrapper">
                          <el-button
                            plain
                            type="primary"
                            @click="handleAddCondition(idx)"
                          >
                            <el-icon>
                              <Plus />
                            </el-icon>
                            添加判断条件
                          </el-button>
                        </div>
                      </div>
                    </el-card>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #footer>
        <div class="judge-node-drawer-footer">
          <el-button @click="closeNodeEditDrawer">取消</el-button>
          <el-button type="primary" @click="nodeEditConfirm()">保存</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
import LogicFlow from "@logicflow/core";
import { nextTick, reactive, ref } from "vue";
import { Delete, Plus } from "@element-plus/icons-vue";
import { ElMessage, type FormRules } from "element-plus";
import UuidDisplayInput from "@/components/UuidDisplayInput.vue";

interface NodeDataForm {
  node_name: string;
}

const props = defineProps({
  lf: LogicFlow
});
const isShowDrawer = ref(false);
const isShowNewRuleDrawer = ref(false);
const isShowParameterDrawer = ref(false);
const parameterKeyword = ref();
const isShowCompareDrawer = ref();
const nodeDataFormRef = ref();

const nodeData = ref<LogicFlow.NodeData>();
//表单数据
const formDataList = ref([
  [{ type: "", name: "", value: "", connect_type: "&&" }]
]);

//获取判断条件类型选项
const getConnectTypes = () => {
  return [
    { label: "且", value: "&&" },
    { label: "或", value: "||" }
  ];
};

//获取条件类型选项
const getConditionTypes = () => {
  return [
    { label: "等于", value: "==" },
    { label: "不等于", value: "!=" },
    { label: "大于", value: ">" },
    { label: "小于", value: "<" },
    { label: "大于等于", value: ">=" },
    { label: "小于等于", value: "<=" },
    { label: "包含", value: "contains" },
    { label: "不包含", value: "not contains" },
    { label: "包含于", value: "in" },
    { label: "不包含于", value: "not in" },
    { label: "为空", value: "is null" },
    { label: "非空", value: "is not null" },
    { label: "以...开始", value: "starts with" },
    { label: "以...结束", value: "ends with" },
    { label: "正则表达式", value: "matches" },
    { label: "在IP段内（CIDR格式）", value: "in cidr" },
    { label: "不在IP段内（CIDR格式）", value: "not in cidr" }
  ];
};

//移除条件
const removeCondition = (idx, index) => {
  const conditions = formDataList.value[idx];
  if (conditions.length > 1) {
    conditions.splice(index, 1);
    // 删除后只剩一条，确保第一条没有 connect_type
    if (conditions.length === 1 && "connect_type" in conditions[0]) {
      delete conditions[0].connect_type;
    }
  }
};

//添加条件
const handleAddCondition = idx => {
  formDataList.value[idx].push({
    type: "",
    name: "",
    value: "",
    connect_type: "&&"
  });
};

//添加绑定组
const addGroup = () => {
  formDataList.value.push([
    {
      type: "",
      name: "",
      value: "",
      connect_type: ""
    }
  ]);
};

//移除绑定组
const removeGroup = idx => {
  if (formDataList.value.length > 1) formDataList.value.splice(idx, 1);
};

//打开抽屉
const openNodeEditDrawer = async (data: LogicFlow.NodeData) => {
  nodeData.value = data;
  //判断该节点是新节点还是已有数据节点
  if (nodeData.value.properties.node_name) {
    //旧节点将properites中judge的值传给formDataList
    formDataList.value = [nodeData.value.properties.condition];
    console.log(nodeData.value);
  } else {
    //新节点需要清空formDataList
    formDataList.value = [
      [{ type: "", name: "", value: "", connect_type: "&&" }]
    ];
  }
  console.log(nodeData.value);
  isShowDrawer.value = true;
  //清空残留的表单校验
  nextTick(() => {
    nodeDataFormRef.value.clearValidate(); // 只清除清除验证
  });
};

//关闭抽屉
const closeNodeEditDrawer = () => {
  isShowDrawer.value = false;
};

//确认并保存数据到nodeData的properties上
const nodeEditConfirm = async () => {
  isShowDrawer.value = false;
  nodeData.value.properties.isOld = true;
  //保存数据
  nodeData.value.properties.condition = formDataList.value[0];
  nodeData.value.properties.condition.forEach(item => {
    Object.values(item).forEach(value => {
      if (value === "${367cedf9-5cbe-41e4-8653-6e21e45d0585.output.123}") {
        nodeData.value.properties.input = item;
      }
    });
  });
  console.log(nodeData.value.properties.input);
  //推送数据到节点的properties上并更新(很重要，没送上来你前面怎么改都没用)
  props.lf.setProperties(nodeData.value.id, nodeData.value.properties!);
  console.log(props.lf.getGraphData());
};

//表单校验规则
const NodeDataFormRules = reactive<FormRules<NodeDataForm>>({
  node_name: [
    { required: true, message: "节点标题不能为空", trigger: "change" }
  ]
});

// 复制节点ID
const $_copyNodeId = async (id: string) => {
  try {
    // 使用现代Clipboard API替代已废弃的execCommand
    await navigator.clipboard.writeText(`\$\{${id}\}`);
    ElMessage.success("已复制该节点ID");
  } catch (err) {
    console.error("复制失败:", err);
    // 添加失败反馈（可根据项目需求替换为错误提示）
  }
};

defineExpose({
  openNodeEditDrawer,
  closeNodeEditDrawer
});
</script>

<style lang="scss" scoped>
.judge-node-drawer-body {
  .dialog-content-multi {
    width: 100%;

    .row-group {
      width: 100%;

      .form-col {
        width: 100%;

        .form-left {
          width: 100%;

          .condition-builder {
            width: 100%;

            .conditions-container {
              width: 100%;

              .condition-section {
                width: 100%;
                margin-bottom: 20px;
                padding: 10px;
                border: 1px solid #ebeef5;
                border-radius: 4px;
                background-color: #fafafa;

                .condition-item {
                  width: 100%;
                  margin-bottom: 10px;

                  &:last-child {
                    margin-bottom: 0;
                  }

                  .condition-row {
                    display: flex;
                    gap: 10px;
                    margin-bottom: 10px;
                    align-items: center;

                    .condition-connect {
                      width: 60px;
                      flex-shrink: 0;
                    }

                    .condition-type {
                      width: 120px;
                      flex-shrink: 0;
                    }

                    .condition-name,
                    .condition-value {
                      flex: 1;
                      min-width: 0;
                    }
                  }
                }
              }

              .add-condition-wrapper {
                margin-bottom: 20px;
                text-align: center;
              }
            }
          }
        }
      }

      .action-col {
        width: 100px;
        display: flex;
        align-items: center;
        height: 100%;
      }
    }
  }
}
</style>
