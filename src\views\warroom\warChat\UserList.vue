<template>
  <el-card class="user-list" shadow="never">
    <div class="title">
      <IconifyIconOnline
        height="20px"
        icon="icon-park-outline:every-user"
        width="20px"
      />
      <el-text class="onlineTag">用户列表</el-text>
    </div>
    <!-- 按角色分组(可点击展开/折叠) -->
    <el-scrollbar class="user-scrollbar" height="calc(100% - 100px)">
      <div v-for="group in groupedUsers" :key="group.type" class="user-group">
        <div class="group-header" @click="toggleGroup(group.type)">
          <IconifyIconOffline
            class="icon"
            :icon="
              group.expanded
                ? 'icon-park-outline:down'
                : 'icon-park-outline:arrow-right'
            "
            height="16px"
            width="16px"
          />
          <span>{{ group.type }}</span>
        </div>
        <el-collapse-transition>
          <el-scrollbar
            v-show="group.expanded"
            class="group-scrollbar"
            max-height="300px"
          >
            <div
              v-for="user in group.users"
              :key="user.id"
              :class="{
                selected: user.id === selectedId,
                online: user.online_status,
                current: user.id === userStore.$state.id,
                // 禁止点击自己
                disabled: user.id === userStore.$state.id
              }"
              class="user-item"
              @click="user.id !== userStore.$state.id && selectUser(user)"
            >
              <!-- 当前/其他用户, 在线/离线样式区分 -->
              <div class="user-info">
                <el-text
                  :style="{
                    opacity: user.online_status ? 1 : 0.4,
                    color: user.online_status ? '#ffffff' : ''
                  }"
                  class="username"
                >
                  <span
                    :class="{
                      online: user.online_status,
                      offline: !user.online_status
                    }"
                    :style="{
                      opacity: user.online_status ? 1 : 0.4
                    }"
                    class="status-dot"
                  />
                  &nbsp;&nbsp;
                  {{ user.display_name }}
                  ({{ user.username }})
                </el-text>
                <IconifyIconOffline
                  class="icon"
                  :icon="
                    user.online_status
                      ? 'icon-park-outline:wifi'
                      : 'icon-park-outline:close-wifi'
                  "
                  :style="{
                    opacity: user.online_status ? 1 : 0.4,
                    filter: user.online_status ? 'brightness(1.3)' : 'none'
                  }"
                  height="19px"
                  width="19px"
                />
              </div>
            </div>
          </el-scrollbar>
        </el-collapse-transition>
      </div>
    </el-scrollbar>
  </el-card>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, watch } from "vue";
import { type User } from "@/directives/warroom/warChat";
import { useUserStore } from "@/store/modules/user";
import { useChatStore } from "@/store/warChat";
import IconifyIconOffline from "@/components/ReIcon/src/iconifyIconOffline";
const props = defineProps<{
  users: User[];
  selectedId?: string | null;
  roomId: string;
  chatType?: "public" | "private" | "ai";
}>();

const emit = defineEmits<{
  (e: "select-user", user: User): void;
}>();

const chatStore = useChatStore();
// const { rooms } = storeToRefs(chatStore);
// const isPrivateChat = computed(() => {
//   const currentRoom = rooms.value.find(room => room.id === props.roomId);
//   return currentRoom?.name?.startsWith("私聊_") || false;
// });

// 用户角色分组状态
const groupStates = ref<Record<string, boolean>>({});
const toggleGroup = (type: string) => {
  groupStates.value[type] = !groupStates.value[type];
};

const selectUser = (user: User) => {
  if (chatStore.ws?.readyState !== WebSocket.OPEN) {
    chatStore.connectWebSocket();
  }
  emit("select-user", user);
};

const userStore = useUserStore();

// 监听房间ID变化
watch(
  () => props.roomId,
  newRoomId => {
    if (newRoomId && chatStore.ws?.readyState === WebSocket.OPEN) {
      chatStore.requestOnlineUsers(newRoomId);
    }
  }
);

// 挂载时立即请求在线用户
onMounted(() => {
  if (props.roomId && chatStore.ws?.readyState === WebSocket.OPEN) {
    chatStore.requestOnlineUsers(props.roomId);
  }
});

// 排序逻辑(首位是当前用户, 其次优先在线, 最后按用户名)
const sortedUsers = computed(() => {
  const currentUser = props.users.find(u => u.id === userStore.$state.id);
  const otherUsers = props.users.filter(u => u.id !== userStore.$state.id);
  return [
    ...(currentUser ? [currentUser] : []),
    ...otherUsers.sort((a, b) => {
      if (a.online_status !== b.online_status) {
        return a.online_status ? -1 : 1;
      }
      return a.username.localeCompare(b.username);
    })
  ];
});

// 获取角色类型
const allRoleTypes = computed(() => {
  const roles = new Set<string>();
  props.users.forEach(user => {
    if (user.roles && Array.isArray(user.roles)) {
      user.roles.forEach(role => roles.add(role));
    }
  });
  return Array.from(roles);
});

watch(
  allRoleTypes,
  newRoleTypes => {
    newRoleTypes.forEach(role => {
      if (groupStates.value[role] === undefined) {
        groupStates.value[role] = true;
      }
    });
  },
  { immediate: true }
);

// 获取按角色分组后的用户列表
const groupedUsers = computed(() => {
  const groups: Record<string, User[]> = {};
  allRoleTypes.value.forEach(role => {
    groups[role] = [];
  });
  sortedUsers.value.forEach(user => {
    user.roles.forEach(role => {
      if (!groups[role]) {
        groups[role] = [];
      }
      groups[role].push(user);
    });
  });
  return Object.keys(groups).map(type => {
    return {
      type,
      users: groups[type],
      expanded: groupStates.value[type] !== false
    };
  });
});
</script>

<style scoped>
.group-count {
  margin-left: 8px;
  font-size: 12px;
  color: #bdc3c7;
}

.user-item.current {
  background-color: rgba(51, 182, 209, 0.1) !important;
  cursor: default;
}

.user-item.current:hover {
  transform: none !important;
  background-color: rgba(255, 193, 7, 0.1) !important;
}

.badge {
  margin-left: 8px;
}

.username {
  display: flex;
  align-items: center;
  white-space: nowrap;
  position: relative;
}

.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-left: 8px;
  position: relative;
}

.status-dot.online {
  background-color: #67c23a;
  box-shadow: 0 0 0 3px rgba(103, 194, 58, 0.4);
  animation: pulse 2s infinite;
}

.status-dot.offline {
  background-color: #f56c6c;
  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.6);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(103, 194, 58, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0);
  }
}

.user-list {
  height: 100%;
  border-radius: 0;
  background-color: #2c3e50;
  color: #ecf0f1;
  border: none;
  font-family: "Segoe UI", "PingFang SC", "Microsoft YaHei", sans-serif;
}

.title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: bold;
  padding: 12px 16px;
  color: #ecf0f1;
}

.onlineTag {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  margin: 4px 8px;
  border-radius: 6px;
}

.user-item:hover {
  background-color: rgba(52, 152, 219, 0.1) !important;
  transform: translateX(5px);
}

.user-item.selected {
  background-color: rgba(52, 152, 219, 0.2) !important;
  box-shadow: 0 4px 8px rgba(52, 152, 219, 0.2);
}

.user-info {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.username {
  font-weight: 600;
  color: #ffffff;
}

.el-tag--success {
  background-color: #64b5f6;
  color: white;
  font-weight: 600;
  border: 1px solid #64b5f6;
}

.el-tag--info {
  background-color: #7f8c8d;
  color: white;
  font-weight: 600;
  border: 1px solid #7f8c8d;
}

.user-scrollbar {
  height: calc(100vh - 150px);
  margin: 0;
  padding: 0;
  transition: all 0.3s ease;
}

.group-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  margin: 4px 8px;
  font-weight: 600;
  color: #ecf0f1;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.group-header:hover {
  background-color: rgba(52, 152, 219, 0.1);
}

.group-scrollbar {
  margin-left: 10px;
}

.user-item.disabled {
  opacity: 0.7;
  cursor: not-allowed !important;
  transform: none !important;
}

.user-item.disabled:hover {
  background-color: transparent !important;
  transform: none !important;
}

.user-item.online {
  background-color: rgba(52, 152, 219, 0.07);
  box-shadow: 0 2px 4px rgba(52, 152, 219, 0.1);
}

.online .username {
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
}
</style>
