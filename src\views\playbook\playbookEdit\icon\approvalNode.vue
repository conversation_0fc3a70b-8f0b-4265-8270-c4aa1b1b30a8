<template>
  <div class="vue-wrap">
    <div class="vue-html">
      <div class="vue-html-title">
        <div class="vue-html-title-left">
          <div class="icon" @click.stop="$_copyNodeId()">
            <svg
              class="icon"
              height="20"
              p-id="2625"
              t="1749107452113"
              version="1.1"
              viewBox="0 0 1024 1024"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M515.2 226.752m-226.752 0a226.752 226.752 0 1 0 453.504 0 226.752 226.752 0 1 0-453.504 0Z"
                fill="#2c2c2c"
                p-id="2626"
              />
              <path
                d="M906.496 1024H123.904c-42.24 0-76.8-34.56-76.8-76.8v-147.904C47.104 641.28 176.384 512 334.4 512h361.536c158.016 0 287.296 129.28 287.296 287.296V947.2c0.064 42.24-34.496 76.8-76.736 76.8z"
                fill="#2c2c2c"
                p-id="2627"
              />
            </svg>
          </div>

          <div class="text">人工</div>
        </div>
        <div class="vue-html-title-right">
          <div class="copy" @click.stop="$_copyNode()">
            <svg
              class="icon"
              height="20"
              p-id="2839"
              t="1748240616953"
              version="1.1"
              viewBox="0 0 1024 1024"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M720 192h-544A80.096 80.096 0 0 0 96 272v608C96 924.128 131.904 960 176 960h544c44.128 0 80-35.872 80-80v-608C800 227.904 764.128 192 720 192z m16 688c0 8.8-7.2 16-16 16h-544a16 16 0 0 1-16-16v-608a16 16 0 0 1 16-16h544a16 16 0 0 1 16 16v608z"
                fill="#2c2c2c"
                p-id="2840"
              />
              <path
                d="M848 64h-544a32 32 0 0 0 0 64h544a16 16 0 0 1 16 16v608a32 32 0 1 0 64 0v-608C928 99.904 892.128 64 848 64z"
                fill="#2c2c2c"
                p-id="2841"
              />
              <path
                d="M608 360H288a32 32 0 0 0 0 64h320a32 32 0 1 0 0-64zM608 520H288a32 32 0 1 0 0 64h320a32 32 0 1 0 0-64zM480 678.656H288a32 32 0 1 0 0 64h192a32 32 0 1 0 0-64z"
                fill="#2c2c2c"
                p-id="2842"
              />
            </svg>
          </div>
          <el-popconfirm
            v-if="!props.graphModel.editConfigModel.isSilentMode"
            :hide-after="0"
            cancel-button-text="取消"
            confirm-button-text="确认"
            title="确认删除该节点吗？"
            width="auto"
            @confirm.stop="$_deleteNode()"
          >
            <template #reference>
              <div class="delete" @click.stop>
                <svg
                  class="icon"
                  height="20"
                  p-id="3915"
                  t="1748240739833"
                  version="1.1"
                  viewBox="0 0 1024 1024"
                  width="20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M106.666667 213.333333h810.666666v42.666667H106.666667z"
                    fill="#d81e06"
                    p-id="3916"
                  />
                  <path
                    d="M640 128v42.666667h42.666667V128c0-23.573333-19.093333-42.666667-42.538667-42.666667H383.872A42.496 42.496 0 0 0 341.333333 128v42.666667h42.666667V128h256z"
                    fill="#d81e06"
                    p-id="3917"
                  />
                  <path
                    d="M213.333333 896V256H170.666667v639.957333C170.666667 919.552 189.653333 938.666667 213.376 938.666667h597.248C834.218667 938.666667 853.333333 919.68 853.333333 895.957333V256h-42.666666v640H213.333333z"
                    fill="#d81e06"
                    p-id="3918"
                  />
                  <path
                    d="M320 341.333333h42.666667v384h-42.666667zM490.666667 341.333333h42.666666v384h-42.666666zM661.333333 341.333333h42.666667v384h-42.666667z"
                    fill="#d81e06"
                    p-id="3919"
                  />
                </svg>
              </div>
            </template>
          </el-popconfirm>
          <div v-else class="delete" @click.stop>
            <svg
              class="icon"
              height="20"
              p-id="3915"
              t="1748240739833"
              version="1.1"
              viewBox="0 0 1024 1024"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M106.666667 213.333333h810.666666v42.666667H106.666667z"
                fill="#d81e06"
                p-id="3916"
              />
              <path
                d="M640 128v42.666667h42.666667V128c0-23.573333-19.093333-42.666667-42.538667-42.666667H383.872A42.496 42.496 0 0 0 341.333333 128v42.666667h42.666667V128h256z"
                fill="#d81e06"
                p-id="3917"
              />
              <path
                d="M213.333333 896V256H170.666667v639.957333C170.666667 919.552 189.653333 938.666667 213.376 938.666667h597.248C834.218667 938.666667 853.333333 919.68 853.333333 895.957333V256h-42.666666v640H213.333333z"
                fill="#d81e06"
                p-id="3918"
              />
              <path
                d="M320 341.333333h42.666667v384h-42.666667zM490.666667 341.333333h42.666666v384h-42.666666zM661.333333 341.333333h42.666667v384h-42.666667z"
                fill="#d81e06"
                p-id="3919"
              />
            </svg>
          </div>
        </div>
      </div>
      <div class="vue-html-container">
        <el-form>
          <el-form-item>
            <div
              :title="props.properties.node_name || '未命名节点'"
              class="container-text ellipsis"
            >
              {{
                (props.properties.node_name || "未命名节点").length > 11
                  ? (props.properties.node_name || "未命名节点").substring(
                      0,
                      11
                    ) + "..."
                  : props.properties.node_name || "未命名节点"
              }}
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>

  <div v-if="props.properties.isWebSocket" class="webSocket">
    <div class="webSocket-result">
      <div class="webSocket-result-true" @click.stop="openDialog()">
        <div class="webSocket-result-left">
          <el-icon class="webSocket-result-icon" :style="{ color: iconColor }">
            <component :is="iconComponent" />
          </el-icon>
          <div class="webSocket-result-text">
            {{ statusText }}
          </div>
          <div class="webSocket-result-time" :style="{ color: iconColor }">
            {{ statusTime }}
          </div>
        </div>
        <div
          class="webSocket-result-right"
          @click.stop="$_closePlaybookStart()"
        >
          <el-icon>
            <Close />
          </el-icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElIcon,
  ElPopconfirm
} from "element-plus";
import usePlaybookStore from "@/store/modules/playbook";
import { computed } from "vue";
import {
  CircleCheckFilled,
  CircleCloseFilled,
  Close
} from "@element-plus/icons-vue";
import { status } from "nprogress";

const props = defineProps({
  id: {
    type: String
  },
  properties: {
    type: Object,
    required: true
  },
  model: {
    type: Object
  },
  graphModel: {
    type: Object
  },
  text: {
    type: String
  },
  onBtnCopyClick: Function,
  onBtnDelClick: Function,
  onBtnWebSocket: Function,
  onBtnCloseClick: Function
});

const playbookStore = usePlaybookStore();

// 新增计算属性：获取当前节点状态信息
const currentNodeStatus = computed(() => {
  if (!playbookStore.SSEResult) return null;
  return playbookStore.SSEResult.find((item: any) => item.node_id === props.id);
});

// 获取状态文本（运行成功/运行失败）
const statusText = computed(() => {
  const statusInfo = currentNodeStatus.value;
  if (!statusInfo) return "未开始";
  return statusInfo.status === 0 ? "运行成功" : "运行失败";
});
const statusTime = computed(() => {
  if (!currentNodeStatus.value) return "0s";
  const duration = calculateDuration(
    currentNodeStatus.value.start_time,
    currentNodeStatus.value.end_time
  );
  return formatDuration(duration);
});
// 计算时间差（单位：毫秒）
const calculateDuration = (startTime: string, endTime: string): number => {
  const start = new Date(startTime).getTime();
  const end = new Date(endTime).getTime();

  if (isNaN(start) || isNaN(end)) {
    console.error("无效的时间格式");
    return 0;
  }

  return end - start;
};

// 格式化时间差为可读格式
const formatDuration = (ms: number): string => {
  if (ms < 0) return "0s"; // 避免负数
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);

  if (minutes > 0) {
    return `${minutes}分${seconds % 60}秒`;
  }

  return `${seconds}s`;
};

//打开webSocket通信结果dialog
const openDialog = () => {
  props.onBtnWebSocket();
};

//复制节点
const $_copyNode = () => {
  props.onBtnCopyClick();
  console.log(props.graphModel.nodes);
  console.log(props.model.properties);
};

//删除节点
const $_deleteNode = () => {
  props.onBtnDelClick();
  console.log(props.graphModel.nodes);
  console.log(props.model.properties);
};

//清空剧本执行结果
const $_closePlaybookStart = () => {
  props.onBtnCloseClick();
};

// 复制节点ID
const $_copyNodeId = async () => {
  try {
    // 使用现代Clipboard API替代已废弃的execCommand
    await navigator.clipboard.writeText(`\$\{${props.id}\}`);
  } catch (err) {
    console.error("复制失败:", err);
    // 添加失败反馈（可根据项目需求替换为错误提示）
  }
};

// 新增计算属性：获取图标组件
const iconComponent = computed(() => {
  return statusText.value === "运行失败"
    ? CircleCloseFilled
    : CircleCheckFilled;
});

// 新增计算属性：获取图标颜色
const iconColor = computed(() => {
  return statusText.value === "运行失败" ? "#d81e06" : "#009624";
});
</script>

<style lang="scss" scoped>
.vue-wrap {
  display: flex;
  align-items: center;
  justify-content: center;

  .vue-html {
    width: 190px;
    height: 90px;
    overflow: hidden;
    border: 1px solid rgba(223, 225, 229, 0.8);
    border-radius: 6px;
    box-shadow:
      0 4px 8px rgba(0, 0, 0, 0.1),
      0 2px 4px rgba(0, 0, 0, 0.1);

    .vue-html-title {
      display: flex;
      justify-content: space-between;
      height: 30px;
      color: black;
      padding: 3px 5px;
      background: linear-gradient(#f2f2ff 0%, rgba(252, 252, 255, 1) 100%);

      .vue-html-title-left {
        display: flex;

        .icon {
          cursor: pointer;
        }

        .text {
          padding-left: 5px;
          font-size: 13px;
        }
      }

      .vue-html-title-right {
        display: flex;

        .copy {
          cursor: pointer;
        }

        .delete {
          cursor: pointer;
          margin-left: 10px;
        }
      }
    }

    .vue-html-container {
      display: flex;
      justify-content: center;
      height: 70px;
      background: white;
      padding-top: 10px;

      .container-text {
        font-size: 13px;
        max-width: 100%;
      }

      .ellipsis {
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

.webSocket {
  width: 200px;
  height: auto;
  color: white;
  margin-top: 5px;

  .webSocket-result {
    display: flex;
    justify-content: center;

    .webSocket-result-true {
      border: 1px solid rgba(223, 225, 229, 0.8);
      border-radius: 6px;
      /* 核心阴影 */
      box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.1),
        0 2px 4px rgba(0, 0, 0, 0.1);
      width: 190px;
      height: 30px;
      background: rgba(241, 248, 244, 0.7);
      color: black;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;

      .webSocket-result-left {
        display: flex;
        align-items: center;
        padding-left: 5px;

        .webSocket-result-icon {
          width: 20px;
          height: 20px;
          background: linear-gradient(
            145deg,
            var(--primary-green) 0%,
            var(--success-dark) 100%
          );
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #009624;
          font-size: 24px;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .webSocket-result-text {
          padding-left: 5px;
          font-size: 13px;
          font-weight: 500;
          letter-spacing: -0.3px;
        }

        .webSocket-result-time {
          padding-left: 5px;
          font-size: 13px;
          font-weight: 500;
        }
      }

      .webSocket-result-right {
        padding-right: 5px;
        display: flex;
        align-items: center;
      }
    }

    .el-button {
      padding: 0px 0px;
      margin: 0px 0px;
      border: 1px solid rgba(223, 225, 229, 0.8);
      box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.08),
        0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .el-button:hover {
      box-shadow: 0 6px 16px rgba(0, 200, 83, 0.12);
      transform: translateY(-2px);
    }
  }
}
</style>
