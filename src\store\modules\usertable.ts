import { defineStore } from "pinia";
import { ref } from "vue";
import { getUserList } from "@/api/userList";

interface UserResponse {
  code: number;
  data: {
    data: Array<{
      id: string;
      username: string;
    }>;
    total: number;
    page: number;
    size: number;
    total_pages: number;
  };
}

export const useUserTableStore = defineStore("userTable", () => {
  const userOptions = ref<
    Array<{ label: string; value: string; username: string }>
  >([]);
  const loading = ref(false);
  const cachedTotalPages = ref<number | null>(null);

  // 获取用户列表（自动分页获取所有数据）
  const fetchUserList = async () => {
    try {
      // 先获取第一页数据，检查总页数是否变化
      const firstPageResponse = (await getUserList({
        page: 1,
        size: 500
      })) as UserResponse;

      if (firstPageResponse.code !== 0) {
        console.error("获取用户列表出错，错误码:", firstPageResponse.code);
        return;
      }

      // 如果缓存的总页数存在且与当前总页数相同，说明数据已经是最新的
      if (cachedTotalPages.value === firstPageResponse.data.total_pages) {
        console.log("数据已是最新，无需重新加载");
        return;
      }

      // 需要重新加载数据
      loading.value = true;
      userOptions.value = []; // 清空现有数据
      // 处理第一页数据
      const firstPageUsers = firstPageResponse.data.data.map(item => ({
        label: item.username,
        value: item.id,
        username: item.username
      }));
      userOptions.value = firstPageUsers;

      // 如果有更多页，继续加载
      let currentPage = 2; // 从第2页开始
      while (currentPage <= firstPageResponse.data.total_pages) {
        const response = (await getUserList({
          page: currentPage,
          size: 500
        })) as UserResponse;
        if (response.code !== 0) {
          console.error("获取用户列表出错，错误码:", response.code);
          break;
        }

        const pageUsers = response.data.data.map(item => ({
          label: item.username,
          value: item.id,
          username: item.username
        }));
        userOptions.value = [...userOptions.value, ...pageUsers];
        currentPage++;
      }

      // 更新缓存的总页数
      cachedTotalPages.value = firstPageResponse.data.total_pages;
      console.log(
        "用户列表加载完成，共获取",
        userOptions.value.length,
        "条数据"
      );
    } catch (error) {
      console.error("获取用户列表失败:", error);
    } finally {
      loading.value = false;
    }
  };

  return {
    userOptions,
    loading,
    fetchUserList
  };
});
