<template>
  <div class="timer-tasks event-module-container bg-white dark:bg-[#141414]">
    <div class="header-actions">
      <div class="search-area">
        <Perms :value="['scene:r']">
          <el-input
            v-model="keyword"
            clearable
            placeholder="搜索场景关键字"
            style="width: 300px"
            @change="handleSearch"
            @clear="handleClear"
          >
            <template #append>
              <el-button @click="handleSearch">
                <el-icon>
                  <Search />
                </el-icon>
              </el-button>
            </template>
          </el-input>
        </Perms>
      </div>
      <div class="right-actions">
        <Perms :value="['scene:c']">
          <el-button class="ml-20" type="primary" @click="handleAdd">
            新建
          </el-button>
        </Perms>
      </div>
    </div>
    <el-table :data="sceneList" border row-key="id" style="width: 100%">
      <el-table-column label="场景名称" prop="name" width="220" />
      <el-table-column label="场景描述" prop="remark" />
      <el-table-column label="场景状态" prop="status" width="150px">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            :active-text="'启用'"
            :inactive-text="'禁用'"
            inline-prompt
            :before-change="() => beforeSwitchChange(scope.row)"
            @change="handleSwitchChange(scope.row, scope.$index)"
          />
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="creator" width="200">
        <template #default="scope">
          {{ scope.row.creator.display_name }}({{ scope.row.creator.username }})
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="ctime" width="200">
        <template #default="scope">
          {{ formatTime(scope.row.ctime) }}
        </template>
      </el-table-column>
      <el-table-column
        label="更新人"
        prop="updator"
        show-overflow-tooltip
        width="200"
      >
        <template #default="scope">
          {{ scope.row.updator.display_name }}({{ scope.row.updator.username }})
        </template>
      </el-table-column>
      <el-table-column label="更新时间" prop="utime" width="200">
        <template #default="scope">
          {{ formatTime(scope.row.utime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template #default="{ row }">
          <Perms :value="['scene:u']">
            <el-tooltip :hide-after="0" content="编辑" placement="top">
              <el-button link type="primary" @click="handleEdit(row)">
                <IconifyIconOffline
                  height="20"
                  icon="mingcute:edit-line"
                  width="20"
                />
              </el-button>
            </el-tooltip>
          </Perms>
          <Perms :value="['scene:d']">
            <el-tooltip :hide-after="0" content="删除" placement="top">
              <span class="ml-3">
                <el-popconfirm
                  cancel-button-text="取消"
                  confirm-button-text="确认"
                  title="确认要删除该场景吗？"
                  @confirm="handleDelete(row)"
                >
                  <template #reference>
                    <el-button link type="danger">
                      <IconifyIconOffline
                        height="20"
                        icon="icon-park-outline:delete"
                        width="20"
                      />
                    </el-button>
                  </template>
                </el-popconfirm>
              </span>
            </el-tooltip>
          </Perms>
        </template>
      </el-table-column>
    </el-table>
    <!-- 添加分页区域 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="page"
        v-model:page-size="page_size"
        :background="true"
        :page-sizes="[15, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!-- 场景编辑弹窗 -->
    <scene-edit
      v-model:visible="dialogVisible"
      :edit-data="editData"
      :is-edit="isEdit"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, watch } from "vue";
import { editScene, getSceneList } from "@/api/scene";
import { debounce } from "lodash-es";
import { ElMessage } from "element-plus";
import SceneEdit from "./sceneEdit.vue";
import { Icon } from "@iconify/vue";
import { Search } from "@element-plus/icons-vue";
import { apiSceneCurrent, apiSceneRemove } from "@/api/playbook";
import usePlaybookStore from "@/store/modules/playbook";
import IconifyIconOffline from "@/components/ReIcon/src/iconifyIconOffline";
// 分页相关
const page = ref(1);
const page_size = ref(15);
const total = ref(0);
const keyword = ref("");
const sceneList = ref([]);
const hideUsed = ref(false);
const playbookStore = usePlaybookStore();

// 弹窗控制
const dialogVisible = ref(false);
const isEdit = ref(false);
const editData = ref({
  name: "",
  remark: "",
  priority: 1,
  status: 0
});

// 状态映射
// const directionOptions = [
//   { label: "禁用", value: 0 },
//   { label: "启用", value: 1 },
//   { label: "当前场景", value: 2 }
// ];

const beforeSwitchChange = (row): Promise<boolean> => {
  return new Promise(resolve => {
    if (row.status === 0) {
      // 如果是关闭状态，允许切换
      resolve(true);
    } else {
      // 如果是开启状态，阻止切换并提示用户
      ElMessage.error("至少需要激活一个场景");
      resolve(false);
    }
  });
};

//状态切换
const handleSwitchChange = async (row, index) => {
  if (sceneList.value[index].status === 1) {
    let res: any = await apiSceneCurrent({ id: row.id });
    if (res.code == 0) {
      ElMessage({
        message: "切换成功",
        type: "success"
      });
    } else {
      ElMessage.error("切换失败");
    }
    // 如果当前开关被切换为 on，将其他开关切换为 off
    sceneList.value.forEach((item, idx) => {
      if (idx !== index) {
        item.status = 0;
      }
    });
  }
  findSceneName();
};

//查找目前启用的场景的name
const findSceneName = () => {
  let found = false; // 用于标记是否找到了 status == 1 的项
  // 遍历整个数组
  sceneList.value.forEach(item => {
    if (item.status == 1) {
      // 保存选中的场景名称到 localStorage
      localStorage.setItem("selectedScene", item.name);
      playbookStore.selectedScene = item.name; // 保存该 item.name
      found = true; // 找到符合条件的项，将标记设置为 true
    }
  });
  // 如果没有找到符合条件的项，设置默认值
  if (!found) {
    playbookStore.selectedScene = "";
  }
};

// 处理新建
const handleAdd = () => {
  isEdit.value = false;
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = (row: any) => {
  isEdit.value = true;
  editData.value = { ...row };
  dialogVisible.value = true;
};

// 处理删除
const handleDelete = async (row: any) => {
  let res: any = await apiSceneRemove({ id: row.id });
  if (res.code == 0) {
    ElMessage({
      type: "success",
      message: "删除成功"
    });
  } else {
    ElMessage.error("删除失败");
  }
  //再次请求场景列表数据
  await sceneres(page.value, page_size.value, keyword.value);
};
// 处理状态改变
const handleStatusChange = async (row: any) => {
  const editEditScene = (await editScene({
    id: row.id,
    status: row.status
  })) as any;
  if (editEditScene.code === 0) {
    sceneres(page.value, page_size.value, keyword.value);
    ElMessage.success("修改成功");
  }
};
// 获取场景列表
const sceneres = async (currentPage: number, size: number, key: string) => {
  const res = (await getSceneList({
    page: currentPage,
    page_size: size,
    key
  })) as any;
  if (res.code === 0) {
    console.log(res.data.scenes);
    sceneList.value = res.data.scenes;
    total.value = res.data.total;
    page.value = currentPage;
    page_size.value = size;
  }
};

// 处理清除
const handleClear = () => {
  keyword.value = "";
  page.value = 1; // 重置到第一页
  sceneres(page.value, page_size.value, "");
};

// 处理搜索
const handleSearch = () => {
  page.value = 1; // 重置到第一页
  sceneres(page.value, page_size.value, keyword.value);
};

// 更新场景
const updateScene = async (id: string, status: number) => {
  const editEditScene = (await editScene({
    id: id,
    status: status
  })) as any;
  if (editEditScene.code === 0) {
    sceneres(page.value, page_size.value, keyword.value);
  } else {
    console.log("修改失败");
  }
};

// 创建防抖的搜索函数
const debouncedSearch = debounce(async (value: string) => {
  await sceneres(1, page_size.value, value);
}, 500);

// 监听搜索关键字变化
watch(keyword, async newVal => {
  debouncedSearch(newVal);
});

// 监听隐藏已禁用场景开关
watch(hideUsed, async newVal => {
  await sceneres(1, page_size.value, keyword.value);
});

// 组件挂载时触发
onMounted(async () => {
  // 获取场景列表
  await sceneres(page.value, page_size.value, keyword.value);
  //组件挂载时，获取当前启用的场景的name
  findSceneName();
});
// 处理分页大小变化
const handleSizeChange = async (val: number) => {
  page_size.value = val;
  await sceneres(page.value, val, keyword.value);
};

// 处理页码变化
const handleCurrentChange = async (val: number) => {
  page.value = val;
  await sceneres(val, page_size.value, keyword.value);
};

// 添加时间格式化函数
const formatTime = (timestamp: string | number) => {
  if (!timestamp) return "";
  const date = new Date(timestamp);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false
  });
};

// 过滤后的场景列表
const filteredSceneList = computed(() => {
  if (hideUsed.value) {
    return sceneList.value.filter(item => item.status !== 0);
  }
  return sceneList.value;
});

// 处理编辑成功
const handleEditSuccess = async newScene => {
  ElMessage.success(isEdit.value ? "编辑成功" : "新建成功");
  await sceneres(page.value, page_size.value, keyword.value);

  // 新建且需要自动设置为当前场景
  if (!isEdit.value && newScene && newScene.name) {
    const idx = sceneList.value.findIndex(item => item.name === newScene.name);
    if (idx !== -1) {
      await handleSwitchChange(sceneList.value[idx], idx);
    }
  }
};
</script>

<style scoped>
.timer-tasks {
  padding: 20px;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.right-actions {
  display: flex;
  align-items: center;
}

.search-input {
  width: 300px;
}

.ml-20 {
  margin-left: 20px;
}

.mr-2 {
  margin-right: 8px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.segmented-container {
  width: 100%;
}

:deep(.el-segmented) {
  width: 100%;
  min-width: 240px;
}

:deep(.el-segmented-item) {
  flex: 1;
  min-width: 0;
}

:deep(.el-segmented-item.is-active) {
  background-color: var(--el-color-primary);
  color: white;
}
</style>
