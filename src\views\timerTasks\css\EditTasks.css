.timer-task-dialog {
    display: flex;
    flex-direction: column;
}

.el-dialog__body {
    padding: 0;
}

.el-dialog__header {
    margin: 0;
    padding: 20px 20px 10px;
}

.el-dialog__footer {
    margin: 0;
    padding: 10px 20px 20px;
}

.task-form {
    padding: 20px;
}

.form-item {
    margin-bottom: 24px;
}

.form-label {
    font-size: 14px;
    margin-bottom: 10px;
    margin-top: 8px;
    display: flex;
    align-items: center;
    font-weight: 500;
    color: var(--el-text-color-primary);
}

.required {
    color: var(--el-color-danger);
    margin-right: 6px;
    font-weight: bold;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
}

.playbook-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.cron-input-container {
    margin-top: 16px;
    padding: 16px;
    background-color: #f5f7fa;
    border-radius: 4px;
}

.cron-header {
    display: flex;
    margin-bottom: 8px;
}

.cron-label {
    flex: 1;
    text-align: center;
}

.cron-visual-editor {
    display: flex;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
}

.cron-field {
    flex: 1;
    position: relative;
    border-right: 1px solid #dcdfe6;
}

.cron-field:last-child {
    border-right: none;
}

.cron-field-input {
    width: 100%;
    height: 36px;
    border: none;
    outline: none;
    text-align: center;
    background-color: #fff;
    color: #606266;
    font-size: 14px;
    padding: 0 5px;
}

.next-execution {
    text-align: center;
    margin-top: 12px;
    padding: 8px;
    border-radius: 4px;
    color: #606266;
    font-size: 14px;
}

.next-execution-detail {
    text-align: center;
    padding: 4px;
    border-radius: 4px;
    color: #606266;
    font-size: 12px;
}

.input-params {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.param-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.param-input {
    flex: 1;
}

.param-key {
    width: 150px;
    margin-right: 10px;
}

.retry-timeout-wrapper {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin: 16px 0;
    background-color: var(--el-fill-color-light);
    padding: 16px;
    border-radius: 8px;
}

.retry-timeout-row {
    display: flex;
    gap: 20px;
    align-items: center;
    width: 100%;
}

.retry-timeout-item {
    flex: 1;
    background-color: var(--el-bg-color);
    padding: 12px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.retry-timeout-label {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 8px;
    color: var(--el-text-color-secondary);
    font-size: 14px;
}

.retry-timeout-label .el-icon {
    font-size: 16px;
    color: var(--el-color-primary);
}

.custom-input-number {
    width: 100%;
}

.custom-input-number :deep(.el-input__wrapper),
.retry-timeout-item .el-input :deep(.el-input__wrapper) {
    box-shadow: 0 0 0 1px var(--el-border-color) inset;
}

.custom-input-number :deep(.el-input__wrapper:hover),
.retry-timeout-item .el-input :deep(.el-input__wrapper:hover) {
    box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}

.param-row {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    width: 100%;
    background-color: var(--el-bg-color);
    padding: 12px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 8px;
}

.param-row:last-child {
    margin-bottom: 0;
}

.param-input-wrapper {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    flex: 1;
    min-height: 32px;
}

.param-label {
    min-width: 160px;
    max-width: 160px;
    color: var(--el-text-color-regular);
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 40px;
    flex-shrink: 0;
    height: 40px;
}

.param-icon {
    color: var(--el-color-primary);
    font-size: 16px;
    flex-shrink: 0;
}

.param-input-wrapper .el-input {
    flex: 1;
    min-width: 0;
}

.param-input-wrapper .el-input :deep(.el-input__wrapper) {
    height: 40px;
}

.param-input-wrapper .el-switch {
    flex-shrink: 0;
    margin-top: 4px;
}

.param-input-wrapper .required {
    color: var(--el-color-danger);
    margin-left: 4px;
    font-weight: bold;
    flex-shrink: 0;
    line-height: 32px;
}

.delete-btn {
    flex-shrink: 0;
    height: 32px;
    padding: 0 12px;
}

.delete-param-btn {
    padding: 0;
    height: auto;
    font-size: 14px;
}

.param-input-wrapper .el-icon {
    color: var(--el-color-primary);
    font-size: 16px;
}