<template>
  <div class="search-component">
    <!-- 搜索输入框和按钮 -->
    <div class="search-area">
      <el-input
        v-model="searchKeyword"
        clearable
        placeholder="输入事件内容搜索"
      >
        <template #prefix>
          <el-icon>
            <Search />
          </el-icon>
        </template>
      </el-input>

      <!-- 处理状态选择 -->
      <el-select
        v-model="searchForm.event_process_status"
        clearable
        multiple
        placeholder="处理状态"
        style="width: 280px"
      >
        <el-option label="待处理" value="待处理" />
        <el-option label="处理中" value="处理中" />
        <el-option label="已处理" value="已处理" />
      </el-select>

      <!-- 事件来源输入框 -->
      <el-input
        v-model="searchForm.event_source"
        clearable
        placeholder="输入事件来源"
      />
      <el-button type="primary" @click="handleSearch">搜索</el-button>
      <!--      <el-button @click="handleReset">重置</el-button>-->
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { Search } from "@element-plus/icons-vue";

const emit = defineEmits(["search"]);

// 搜索关键字
const searchKeyword = ref("");

// 搜索条件表单
const searchForm = ref({
  event_process_status: ["待处理", "处理中"],
  event_source: ""
});

// 搜索方法
const handleSearch = () => {
  const searchParams = {
    event_name: searchKeyword.value,
    ...searchForm.value
  };
  // 移除空值
  Object.keys(searchParams).forEach(key => {
    if (
      !searchParams[key] ||
      (Array.isArray(searchParams[key]) && searchParams[key].length === 0)
    ) {
      delete searchParams[key];
    }
  });
  emit("search", searchParams);
};

// 重置方法
const handleReset = () => {
  searchKeyword.value = "";
  searchForm.value = {
    event_process_status: [],
    event_source: ""
  };
  emit("search", {});
};
</script>

<style lang="scss" scoped>
.search-component {
  .search-area {
    display: flex;
    gap: 10px;
    align-items: center;

    .el-input {
      width: 250px;
    }

    .el-select {
      width: 300px;
    }
  }
}
</style>
