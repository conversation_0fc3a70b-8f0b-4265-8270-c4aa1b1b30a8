<template>
  <div class="vue-wrap">
    <div class="vue-html">
      <div class="vue-html-title">
        <div>{{ properties.nodeName || "结束" }}</div>
      </div>
      <div class="vue-html-container">
        <svg
          class="icon"
          height="30"
          p-id="5432"
          t="1748432950360"
          version="1.1"
          viewBox="0 0 1024 1024"
          width="30"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M960.031235 159.921913v703.656418c0 52.974134-42.979014 95.953148-95.953148 95.953148h-703.656418c-52.974134 0-95.953148-42.979014-95.953148-95.953148v-703.656418c0-52.974134 42.979014-95.953148 95.953148-95.953148h703.656418c52.974134 0 95.953148 42.979014 95.953148 95.953148z m-831.593949-159.921913C57.771791 0 0.499756 57.272035 0.499756 127.937531v767.625183c0 70.665495 57.272035 127.937531 127.93753 127.93753h767.625183c70.665495 0 127.937531-57.272035 127.937531-127.93753v-767.625183c0-70.665495-57.272035-127.937531-127.937531-127.937531h-767.625183z"
            fill="rgba(216, 30, 6, 0.35)"
            p-id="5433"
          />
        </svg>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  properties: {
    type: Object,
    required: true
  },
  graphModel: Object,
  model: Object,
  onBtnCopyClick: Function,
  onBtnDelClick: Function
});
</script>

<style lang="scss" scoped>
.vue-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  filter: drop-shadow(
    1px 5px 3px rgba(50, 50, 0, 0.5)
  ); //向右偏移、向下偏移、模糊半径
  .vue-html {
    width: 90px;
    height: 90px;
    border: 2px solid rgba(216, 30, 6, 0.35);
    border-radius: 50%;
    overflow: hidden;
    background: white;

    .vue-html-title {
      display: flex;
      justify-content: space-around;
      height: 25%;
      color: black;
      font-size: 14px;
      padding-top: 10px;
    }

    .vue-html-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 75%;
    }
  }
}
</style>
