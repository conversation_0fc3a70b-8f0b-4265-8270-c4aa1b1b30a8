<template>
  <div>
    <el-drawer v-model="isShowDrawer" size="50%">
      <template #header>
        <div class="action-drawer-title">节点信息</div>
      </template>
      <template #default>
        <el-card shadow="never">
          <el-form
            :model="nodeData.properties"
            :rules="rules"
            label-width="100px"
          >
            <el-form-item label="节点ID:">
              <el-button link type="primary" @click="$_copyNodeId(nodeData.id)"
                >{{ nodeData.id }}
              </el-button>
            </el-form-item>
            <el-form-item label="节点标题:" prop="node_name">
              <el-input v-model="nodeData.properties.node_name" />
            </el-form-item>
            <el-form-item label="循环类型:" prop="loop_type">
              <el-select
                v-model="nodeData.properties.loop_type"
                placeholder="请选择循环类型"
                @change="handleLoopTypeChange"
              >
                <el-option label="次数循环" value="count" />
                <el-option label="数组循环" value="array" />
              </el-select>
            </el-form-item>
            <el-form-item
              :label="
                nodeData.properties.loop_type === 'count'
                  ? '循环次数:'
                  : '循环数组:'
              "
              :prop="
                nodeData.properties.loop_type === 'count'
                  ? 'loop_count'
                  : 'loop_array'
              "
            >
              <el-input-number
                v-if="nodeData.properties.loop_type === 'count'"
                v-model="nodeData.properties.loop_count"
                :min="1"
              />
              <el-input
                v-else
                v-model="nodeData.properties.loop_array"
                type="textarea"
                :rows="3"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </template>
      <template #footer>
        <div>
          <el-button @click="closeNodeEditDrawer">取消</el-button>
          <el-button type="primary" @click="nodeEditConfirm()">保存</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
import LogicFlow from "@logicflow/core";
import { reactive, ref } from "vue";
import { ElMessage, FormRules } from "element-plus";
// 节点数据类型
interface aggregationTableType {
  name: string;
  desc: string;
  type: any;
  from: any;
  symbol: string;
}
// 表单验证规则
interface RuleForm {
  node_name: string;
  loop_type: string;
  loop_count: number;
  loop_array: string;
}
// 接收LogicFlow实例
const props = defineProps({
  lf: LogicFlow
});
// 是否显示抽屉
const isShowDrawer = ref(false);
// 节点数据
const nodeData = ref<LogicFlow.NodeData>();

//打开抽屉
const openNodeEditDrawer = async (data: LogicFlow.NodeData) => {
  isShowDrawer.value = !isShowDrawer.value;
  nodeData.value = data;
  // 初始化默认值
  if (!nodeData.value.properties.loop_type) {
    nodeData.value.properties.loop_type = "count";
  }
  if (!nodeData.value.properties.loop_count) {
    nodeData.value.properties.loop_count = 1;
  }
  console.log(nodeData.value);
};

// 处理循环类型变化
const handleLoopTypeChange = (value: string) => {
  if (value === "count") {
    // 次数循环：清空数组，设置默认次数
    nodeData.value.properties.loop_array = "";
    if (!nodeData.value.properties.loop_count) {
      nodeData.value.properties.loop_count = 1;
    }
  } else if (value === "array") {
    // 数组循环：清空次数，设置默认数组
    nodeData.value.properties.loop_count = 0;
    if (!nodeData.value.properties.loop_array) {
      nodeData.value.properties.loop_array = "item1,item2,item3";
    }
  }
};

//关闭抽屉
const closeNodeEditDrawer = () => {
  isShowDrawer.value = false;
};

//确认并保存数据到nodeData的properties上
const nodeEditConfirm = () => {
  isShowDrawer.value = false;
  nodeData.value.properties.isOld = true;
  //推送数据到节点的properties上并更新(很重要，没送上来你前面怎么改都没用)
  props.lf.setProperties(nodeData.value.id, nodeData.value.properties!);
  console.log(props.lf.getGraphData());
};
// 表单验证规则
const rules = reactive<FormRules<RuleForm>>({
  node_name: [{ required: true, message: "请输入节点标题", trigger: "blur" }],
  loop_type: [{ required: true, message: "请选择循环类型", trigger: "change" }],
  loop_count: [
    {
      required: true,
      message: "请输入循环次数",
      trigger: "blur",
      validator: (rule, value, callback) => {
        if (
          nodeData.value?.properties?.loop_type === "count" &&
          (!value || value < 1)
        ) {
          callback(new Error("次数循环必须设置循环次数"));
        } else {
          callback();
        }
      }
    }
  ],
  loop_array: [
    {
      required: true,
      message: "请输入循环数组",
      trigger: "blur",
      validator: (rule, value, callback) => {
        if (nodeData.value?.properties?.loop_type === "array" && !value) {
          callback(new Error("数组循环必须设置循环数组"));
        } else {
          callback();
        }
      }
    }
  ]
});

// 复制节点ID
const $_copyNodeId = async (id: string) => {
  try {
    // 使用现代Clipboard API替代已废弃的execCommand
    await navigator.clipboard.writeText(`\$\{${id}\}`);
    ElMessage.success("已复制该节点ID");
  } catch (err) {
    console.error("复制失败:", err);
    // 添加失败反馈（可根据项目需求替换为错误提示）
  }
};

//对外暴露方法
defineExpose({
  openNodeEditDrawer
});
</script>

<style lang="scss" scoped>
.aggregation-addparameter {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
</style>
