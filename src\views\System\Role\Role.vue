<template>
  <div class="bg-white dark:bg-[#141414]">
    <el-card>
      <div class="header">
        <h2>角色管理</h2>
        <Perms :value="['role:c']">
          <el-button type="primary" @click="handleAdd">+ 新建</el-button>
        </Perms>
      </div>
      <el-table :data="roleList" border style="margin: 20px 0">
        <el-table-column label="角色" prop="name" />
        <el-table-column label="描述" prop="description" />
        <el-table-column fixed="right" label="操作" width="200">
          <template #default="{ row }">
            <Perms :value="['role:u']">
              <el-tooltip :hide-after="0" content="编辑" placement="top">
                <el-button link type="primary" @click="handleEdit(row)">
                  <IconifyIconOffline
                    height="20"
                    icon="mingcute:edit-line"
                    width="20"
                  />
                </el-button>
              </el-tooltip>
            </Perms>
            <Perms :value="['role:r']">
              <el-tooltip :hide-after="0" content="查看" placement="top">
                <el-button link type="primary" @click="handleCheck(row.id)">
                  <IconifyIconOffline
                    height="20"
                    icon="icon-park-outline:preview-open"
                    width="20"
                  />
                </el-button>
              </el-tooltip>
            </Perms>
            <Perms :value="['role:d']">
              <el-tooltip :hide-after="0" content="删除" placement="top">
                <span class="ml-3">
                  <el-popconfirm
                    cancel-button-text="取消"
                    confirm-button-text="确认"
                    title="确认要删除该角色吗？"
                    @confirm="handleDelete(row)"
                  >
                    <template #reference>
                      <el-button link type="danger">
                        <IconifyIconOffline
                          height="20"
                          icon="icon-park-outline:delete"
                          width="20"
                        />
                      </el-button>
                    </template>
                  </el-popconfirm>
                </span>
              </el-tooltip>
            </Perms>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="page"
          v-model:page-size="size"
          :page-sizes="[15, 50, 100]"
          :total="total"
          background
          layout="total, sizes, prev, pager, next, jumper"
        />
      </div>
    </el-card>
    <RoleNew ref="roleNewRef" @success="initData" />
    <RoleView
      v-if="selectedRoleId"
      :role-id="selectedRoleId"
      @close="selectedRoleId = null"
    />
    <RoleEdit ref="roleEditRef" @success="initData" />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from "vue";
import { deleteRole, getRoleList } from "@/api/role";
import { ElMessage } from "element-plus";
import RoleNew from "./components/roleNew.vue";
import RoleView from "./components/roleView.vue";
import RoleEdit from "./components/roleEdit.vue";
import { Icon } from "@iconify/vue";
import IconifyIconOffline from "@/components/ReIcon/src/iconifyIconOffline";
interface RoleItem {
  id: number;
  name: string;
  description: string;
}

const roleList = ref<RoleItem[]>([]); // 修改为空数组，数据将从 API 获取
const page = ref(1); // 当前页码
const size = ref(15); // 每页条数
const total = ref(0);
const selectedRoleId = ref<string | null>(null);

// 引入事件添加组件
const roleNewRef = ref();
const roleEditRef = ref();

// 添加角色
const handleAdd = () => {
  roleNewRef.value.open();
};

// 编辑角色
const handleEdit = (row: RoleItem) => {
  roleEditRef.value.open(row.id);
};

// 查看角色
const handleCheck = (id: string) => {
  selectedRoleId.value = id;
};

const handleDelete = (row: RoleItem) => {
  // 处理删除逻辑
  deleteRole({ role_id: row.id }).then((res: any) => {
    if (res.code === 0) {
      ElMessage.success("删除成功");
      initData();
    } else {
      ElMessage.error("删除失败");
    }
  });
};

const fetchRoleList = (params: { page: number; size: number }) => {
  getRoleList(params).then((res: any) => {
    console.log("API返回数据：", res.data.roles);
    roleList.value = res.data.roles || res.data;
    total.value = res.data.total || 0;
  });
};

const initData = () => {
  fetchRoleList({
    page: page.value,
    size: size.value
  });
};

watch([page, size], () => {
  fetchRoleList({
    page: page.value,
    size: size.value
  });
});

onMounted(() => {
  initData();
});
</script>

<style scoped>
.role-container {
  min-height: calc(100vh - 40px);
  padding: 20px;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.pagination {
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: flex-end;
  margin-top: 20px;
}

.total {
  font-size: 14px;
  color: #606266;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>
