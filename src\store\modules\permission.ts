import { defineStore } from "pinia";
import {
  ascending,
  type cacheType,
  constantMenus,
  debounce,
  filterNoPermissionTree,
  filterTree,
  formatFlatteningRoutes,
  getKeyList,
  store
} from "../utils";
import { useMultiTagsStoreHook } from "./multiTags";

export const usePermissionStore = defineStore({
  id: "pure-permission",
  state: () => ({
    // 静态路由生成的菜单
    constantMenus,
    // 整体路由生成的菜单（静态、动态）
    wholeMenus: [],
    // 整体路由（一维数组格式）
    flatteningRoutes: [],
    // 缓存页面keepAlive
    cachePageList: []
  }),
  actions: {
    /** 组装整体路由生成的菜单 */
    handleWholeMenus(routes: any[]) {
      // 如果路由为空，则不进行处理(暂时不处理，为空需要加载静态路由)
      // if (routes.length === 0) {
      //   return;
      // }
      // 如果整体路由生成的菜单不为空，则不进行处理
      if (this.flatteningRoutes.length > 0) {
        return;
      }
      // 合并静态路由和动态路由（即使动态路由为空也要处理静态路由）
      const allRoutes = this.constantMenus.concat(routes || []);
      this.wholeMenus = filterNoPermissionTree(
        filterTree(ascending(allRoutes))
      );
      this.flatteningRoutes = formatFlatteningRoutes(allRoutes);
    },
    cacheOperate({ mode, name }: cacheType) {
      const delIndex = this.cachePageList.findIndex(v => v === name);
      switch (mode) {
        case "refresh":
          this.cachePageList = this.cachePageList.filter(v => v !== name);
          break;
        case "add":
          this.cachePageList.push(name);
          break;
        case "delete":
          delIndex !== -1 && this.cachePageList.splice(delIndex, 1);
          break;
      }
      /** 监听缓存页面是否存在于标签页，不存在则删除 */
      debounce(() => {
        let cacheLength = this.cachePageList.length;
        const nameList = getKeyList(useMultiTagsStoreHook().multiTags, "name");
        while (cacheLength > 0) {
          nameList.findIndex(v => v === this.cachePageList[cacheLength - 1]) ===
            -1 &&
            this.cachePageList.splice(
              this.cachePageList.indexOf(this.cachePageList[cacheLength - 1]),
              1
            );
          cacheLength--;
        }
      })();
    },
    /** 清空缓存页面 */
    clearAllCachePage() {
      this.wholeMenus = [];
      this.cachePageList = [];
      this.flatteningRoutes = []; // 清空路由
    }
  }
});

export function usePermissionStoreHook() {
  return usePermissionStore(store);
}
