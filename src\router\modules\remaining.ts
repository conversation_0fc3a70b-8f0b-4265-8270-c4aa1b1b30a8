const Layout = () => import("@/layout/index.vue");

export default [
  {
    path: "/login",
    name: "<PERSON><PERSON>",
    component: () => import("@/views/login/index.vue"),
    meta: {
      title: "登录",
      showLink: false,
      rank: 101
    }
  },
  {
    path: "/redirect",
    component: Layout,
    meta: {
      title: "加载中...",
      showLink: false,
      rank: 102
    },
    children: [
      {
        path: "/redirect/:path(.*)",
        name: "Redirect",
        component: () => import("@/layout/redirect.vue")
      }
    ]
  },
  {
    path: "/account-settings",
    name: "AccountSettings",
    component: () => import("@/views/account-settings/index.vue"),
    meta: {
      title: "个人中心",
      showLink: false
    }
  },
  {
    path: "/loading",
    name: "Loading",
    component: () => import("@/views/loading/index.vue"),
    meta: {
      title: "OAuth登录加载中...",
      showLink: false
    }
  },
  {
    path: "/embed-warroom/:roomId?",
    name: "EmbedWarRoom",
    component: () => import("@/views/warroom/WarRoomEmbed.vue"),
    meta: {
      title: "第三方系统调用作战室",
      showLink: false
    }
  }
] satisfies Array<RouteConfigsTable>;
