<template>
  <div class="menu">
    <div class="goBack">
      <div class="logicflow-goback" @click="goBack()">
        <el-tooltip
          :hide-after="0"
          content="返回"
          effect="dark"
          placement="bottom"
        >
          <el-icon :size="24" color="#0D1733">
            <Back />
          </el-icon>
        </el-tooltip>
      </div>
    </div>
    <div class="playbookName">
      <div class="logicflow-playbookName">
        <div class="text">{{ playbookStore.playbookName }}</div>
      </div>
      <div class="logicflow-playbookName-edit">
        <el-tooltip :hide-after="0" content="编辑" placement="bottom">
          <el-button
            :disabled="hasPermission('u')"
            link
            @click="openUpdateDialog"
          >
            <IconifyIconOffline
              color="#0D1733"
              height="24"
              icon="mingcute:edit-line"
              width="24"
            />
          </el-button>
        </el-tooltip>
      </div>
    </div>
    <div class="button">
      <el-tooltip
        :hide-after="0"
        content="查看历史版本"
        effect="dark"
        f
        placement="bottom"
      >
        <div class="menu-item">
          <el-button ref="viewVersionRef" v-click-outside="onClickOutside" link>
            <IconifyIconOffline
              color="#0D1733"
              height="24"
              icon="mdi:history"
              width="24"
            />
          </el-button>
        </div>
      </el-tooltip>
      <el-popover
        ref="popoverRef"
        :virtual-ref="viewVersionRef"
        placement="left-start"
        trigger="click"
        virtual-triggering
        width="500px"
      >
        <div>
          <el-scrollbar max-height="700px">
            <el-row>
              <el-col v-for="(item, index) in playbookVersionList" :key="index">
                <div class="version-list">
                  <div class="change-desc">
                    <div class="change-desc-left">
                      <div class="version-index">#{{ index + 1 }}</div>
                      <div class="version-text">
                        {{ item.change_desc }}
                      </div>
                    </div>

                    <div
                      v-if="item.version_id == playbookVersionId"
                      class="select-version"
                    >
                      <svg
                        class="icon"
                        height="20"
                        p-id="3668"
                        t="1749122184556"
                        version="1.1"
                        viewBox="0 0 1024 1024"
                        width="20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M512 624a112 112 0 1 0 0-224 112 112 0 0 0 0 224z"
                          fill="#00AA62"
                          p-id="3669"
                        />
                      </svg>
                      <div>当前版本</div>
                    </div>
                  </div>
                  <div class="version-utime">
                    <div>{{ item.utime }}</div>
                    <el-button
                      v-if="item.version_id !== playbookVersionId"
                      link
                      size="small"
                      type="primary"
                      @click="switchPlaybookVersion(item)"
                    >
                      切换
                    </el-button>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-scrollbar>
        </div>
      </el-popover>
      <Perms :value="['workflow:u']">
        <el-tooltip
          v-if="playbookStatus == 0"
          :hide-after="0"
          content="发布"
          effect="dark"
          placement="bottom"
        >
          <div class="menu-item" @click="releasePlaybook()">
            <el-button
              :disabled="disabledRelease"
              :style="{
                color: disabledRelease == true ? '#ccc' : '#0d1733'
              }"
              link
            >
              <IconifyIconOffline height="24" icon="mdi:upload" width="24" />
            </el-button>
          </div>
        </el-tooltip>
        <el-tooltip
          :hide-after="0"
          content="保存"
          effect="dark"
          placement="bottom"
        >
          <div class="menu-item" @click="savePlaybookWrapper()">
            <el-button
              :disabled="disabledEdit"
              :style="{
                color: disabledEdit == false ? '#0d1733' : '#ccc'
              }"
              link
            >
              <IconifyIconOffline
                height="24"
                icon="icon-park-outline:save"
                width="24"
              />
            </el-button>
          </div>
        </el-tooltip>
      </Perms>
    </div>
  </div>
</template>

<script lang="ts" setup>
import LogicFlow from "@logicflow/core";
import { computed, onMounted, ref, unref } from "vue";
import { useDetail } from "@/views/playbook/hooks";
import {
  apiGetPlaybookVersionsList,
  apiPlaybookEditFlow
} from "@/api/playbook";
import { ClickOutside as vClickOutside, ElMessage } from "element-plus";
import usePlaybookStore from "@/store/modules/playbook";
import { useRouter } from "vue-router";
import { Back } from "@element-plus/icons-vue";
import IconifyIconOffline from "@/components/ReIcon/src/iconifyIconOffline";

const props = defineProps({
  lf: {
    type: LogicFlow
  }
});

const emit = defineEmits([
  "openReleaseDialog",
  "switchPlaybookVersion",
  "openUpdateDialog"
]);

onMounted(() => {
  getPlaybookVersionList();
  console.log(playbookStore.playbookPermissions);
});

const router = useRouter();
const playbookStore = usePlaybookStore();
const playbookName = ref();
const playbookId = ref();
const playbookVersionId = ref();
const playbookStatus = ref();
const isShowHistoryVersion = ref(false);
const base64Data = ref("");
const srcList = ref([]);
const viewVersionRef = ref();
const popoverRef = ref();
const playbookVersionList = ref();
const graphData = ref();
const playbookPermissions = JSON.parse(
  localStorage.getItem("playbookPermissions")
);
const disabledEdit = computed(
  () =>
    !playbookPermissions[playbookId.value].includes("u") ||
    !playbookStore.isFlowChanged
);

const disabledRelease = computed(
  () =>
    !playbookPermissions[playbookId.value].includes("u") ||
    playbookStore.isFlowChanged
);

//获取剧本信息
const { getParameter } = useDetail();
playbookId.value = getParameter.flow_id;
playbookVersionId.value = getParameter.version_id;
playbookStatus.value = getParameter.status;
console.log(playbookStore.playbookList);
// playbookName.value = playbookStore.playbookList.find(
//   item => (item.id = playbookId.value)
// );

//返回剧本管理模块
const goBack = () => {
  router.push({ name: "playbookIndex" });
};

//打开编辑剧本dialog
const openUpdateDialog = () => {
  emit("openUpdateDialog");
  // if (updatePlaybookDialogRef.value) {
  //   updatePlaybookDialogRef.value.openDialog(row);
  // }
};

//发布剧本
const releasePlaybook = async () => {
  emit("openReleaseDialog");
  // let res: any = await apiPlaybookPublish({
  //   id: playbookVersionId.value
  // });
  // if (res.code === 0) {
  //   ElMessage({
  //     type: "success",
  //     message: "发布成功"
  //   });
  //   router.push({ name: "playbookIndex" });
  // } else {
  //   ElMessage({
  //     type: "warning",
  //     message: "发布失败"
  //   });
  // }
};

// 自定义节流函数
function throttle(func, limit) {
  let lastFunc;
  let lastRan;
  return function () {
    const context = this;
    const args = arguments;
    if (!lastRan) {
      func.apply(context, args);
      lastRan = Date.now();
    } else {
      clearTimeout(lastFunc);
      lastFunc = setTimeout(
        function () {
          if (Date.now() - lastRan >= limit) {
            func.apply(context, args);
            lastRan = Date.now();
          }
        },
        limit - (Date.now() - lastRan)
      );
    }
  };
}

// 保存剧本的函数
const savePlaybook = async () => {
  playbookStore.isFlowChanged = false;
  graphData.value = props.lf.getGraphData();
  graphData.value.nodes.forEach(node => {
    // 基于锚点的位置更新边的路径
    // lf.value.getNodeModelById(node.id).updateField();
    // 在保存流程图数据之前，对流程图内的数据进行修改
    // 检查节点的 properties 字段
    if (node.properties) {
      // 删除 isWebSocket 字段
      if (node.properties.hasOwnProperty("isWebSocket")) {
        delete node.properties.isWebSocket;
      }
      // 设置 scale 为 1
      node.properties.scale = 1;
      node.properties.height = 100;
    }
  });
  let res: any = await apiPlaybookEditFlow({
    flow_id: playbookVersionId.value,
    flow_json: graphData.value
  });
  if (res.code === 0) {
    if (playbookStatus.value == 1) {
      ElMessage({
        type: "success",
        message: "保存成功,已为你自动生成新的版本,正在跳转中"
      });
      const item = res.data;
      //切换剧本版本
      emit("switchPlaybookVersion", item);
    } else {
      ElMessage({
        type: "success",
        message: "保存成功"
      });
    }
  } else {
    ElMessage.error("保存失败");
  }
};

// 使用自定义节流函数包装 savePlaybook
const throttledSavePlaybook = throttle(savePlaybook, 5000);

// 暴露给模板使用
const savePlaybookWrapper = () => {
  throttledSavePlaybook();
};

//点击按钮打开popover弹出框
const onClickOutside = () => {
  unref(popoverRef).popperRef?.delayHide?.();
};

//获取剧本的版本列表
const getPlaybookVersionList = async () => {
  let res: any = await apiGetPlaybookVersionsList({
    playbook_id: playbookId.value
  });
  playbookVersionList.value = res.data;
  console.log(playbookVersionList.value);
};

//切换剧本版本
const switchPlaybookVersion = item => {
  emit("switchPlaybookVersion", item);
  console.log(item);
};

//是否有按钮权限
const hasPermission = (permission: string) => {
  return !playbookPermissions[playbookId.value].includes(permission);
};

//清空剧本执行结果
const clearPlaybookStart = () => {
  //禁用清空剧本执行结果按钮
  playbookStore.isPlaybookStart = false;
  // 获取流程绘图数据
  graphData.value = props.lf.getGraphData();
  console.log("1");
  //过滤开始节点和结束节点
  const nodesToUpdate = graphData.value.nodes.filter(
    (item: any) => item.type !== "start-node" && item.type !== "end-node"
  );
  console.log("2");
  nodesToUpdate.forEach((item: any) => {
    // 显示运行结果并扩大节点的高度
    props.lf.setProperties(item.id, {
      isWebSocket: false,
      scale: 1
    });
  });
  console.log("3");
};

// 获取最新的流程图数据
// const getLatestFlowData = () => {
//   return props.lf.getGraphData();
// };

//保存剧本且未修改流程图时，禁用保存按钮
// 监听流程图变化
// watch(
//   () => {
//     // 使用 JSON.stringify 比较对象是否相同
//     return JSON.stringify(getLatestFlowData());
//   },
//   (latestData, originalData) => {
//     console.log("Latest Data:", latestData);
//     console.log("Original Data:", originalData);
//     // 启用保存按钮
//     if (latestData !== originalData) {
//       isFlowChanged.value = true;
//     } else {
//       isFlowChanged.value = false;
//     }
//   }
// );
</script>

<style lang="scss" scoped>
.menu {
  display: flex;
  justify-content: space-between;
  background: #f7f7fc;
  box-shadow: var(--el-box-shadow-light);
}

.goBack {
  display: flex;
  align-items: center;
  padding-left: 5px;

  .logicflow-goback {
    display: flex;
    align-items: center;
    cursor: pointer;
  }
}

.playbookName {
  display: flex;
}

.logicflow-playbookName {
  display: flex;
  align-items: center;

  .text {
    margin: 0px 10px;
  }
}

.logicflow-playbookName-edit {
  display: flex;
  align-items: center;
}

.button {
  display: flex;
  align-items: center;

  .menu-item {
    padding: 5px 5px;
    cursor: pointer;
  }
}

.version-list {
  margin-bottom: 5px;

  .change-desc {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
    border: 1px solid #ccc;
    border-radius: 10px;
    background: #f7f7fc;
    line-height: 50px;
    padding: 0px 15px;
    margin-bottom: 5px;
    cursor: pointer;

    .change-desc-left {
      display: flex;

      .version-index {
        margin-right: 10px;
      }

      .version-text {
        cursor: pointer;
      }
    }

    .select-version {
      display: flex;
      align-items: center;
    }
  }

  .change-desc:hover {
    background: #e8e8f7;
  }

  .version-utime {
    display: flex;
    justify-content: space-between;
  }
}
</style>
