import { deleteUser, editUser } from "@/api/userList";
import { message } from "@/utils/message";
// 修改状态(账号状态)
export const handleChangeStatus = (row: any) => {
  editUser({
    username: row.username,
    disabled: row.disabled
  }).then((res: any) => {
    console.log(res);
    if (res.data.disabled === true) {
      message("已禁用", { type: "success" }); // 显示禁用成功的消息
    } else {
      message("已启用", { type: "success" }); // 显示启用成功的消息
    }
  });
};

// 删除用户
export const handleDelete = (row: any, callback?: () => void) => {
  deleteUser({
    username: row.username
  }).then((res: any) => {
    console.log(res);
    if (res.code === 0) {
      message(res.data, { type: "success" });
      callback?.(); // 调用回调函数来刷新数据
    } else {
      message(res.data, { type: "error" });
    }
  });
};
