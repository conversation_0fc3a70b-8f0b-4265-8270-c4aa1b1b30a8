<template>
  <div class="event-module-container bg-white dark:bg-[#141414]">
    <el-card>
      <div class="top-actions">
        <!-- 搜索区域 -->
        <div class="search-area">
          <Perms :value="['ingestion:r']">
            <el-input
              v-model="searchName"
              clearable
              style="width: 300px"
              @clear="search"
            >
              <template #append>
                <el-button @click="search">
                  <el-icon>
                    <Search />
                  </el-icon>
                </el-button>
              </template>
            </el-input>
          </Perms>
        </div>
        <Perms :value="['ingestion:c']">
          <div class="operation-buttons">
            <el-button type="primary" @click="handleCreate">新建</el-button>
            <el-upload
              :auto-upload="false"
              :before-upload="beforeUpload"
              :multiple="true"
              :on-change="handleUpload"
              :show-file-list="false"
              accept=".zip"
              action="#"
              class="upload-demo"
            >
              <el-button type="primary">导入</el-button>
            </el-upload>
            <el-button type="primary" @click="exportData">导出</el-button>
          </div>
        </Perms>
      </div>
      <!-- 表格区域 -->
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="名称" prop="event_ingestion_name" />
        <el-table-column label="接入方式" prop="event_source_type">
          <template #default="scope">
            <el-tag effect="dark" type="primary">
              {{ scope.row.event_source_type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="数据解析方式" prop="transforms_type" />
        <Perms :value="['ingestion:c']">
          <el-table-column label="启用状态" prop="enabled_status" width="130">
            <template #default="scope">
              <el-switch
                v-model="scope.row.enabled_status"
                :active-text="'启用'"
                :active-value="1"
                :inactive-text="'禁用'"
                :inactive-value="0"
                inline-prompt
                @click="handleStatusChange(scope.row)"
              />
            </template>
          </el-table-column>
        </Perms>
        <el-table-column align="center" label="事件接入状态">
          <template #default="scope">
            <div class="health-status">
              <el-tooltip
                :content="
                  scope.row.enabled_health_status === 'healthy'
                    ? '数据采集正常'
                    : '数据采集异常'
                "
                :hide-after="0"
                placement="bottom"
              >
                <el-icon
                  :class="[
                    'status-icon',
                    scope.row.enabled_health_status === 'healthy'
                      ? 'success'
                      : 'danger'
                  ]"
                >
                  <CircleCheckFilled
                    v-if="scope.row.enabled_health_status === 'healthy'"
                  />
                  <CircleCloseFilled v-else />
                </el-icon>
              </el-tooltip>
              <span class="separator">-----</span>
              <el-tooltip
                :content="
                  scope.row.convert_health_status === 'healthy'
                    ? '数据解析正常'
                    : '数据解析未配置'
                "
                :hide-after="0"
                placement="bottom"
              >
                <el-icon
                  :class="[
                    'status-icon',
                    scope.row.convert_health_status === 'healthy'
                      ? 'success'
                      : 'danger'
                  ]"
                >
                  <CircleCheckFilled
                    v-if="scope.row.convert_health_status === 'healthy'"
                  />
                  <CircleCloseFilled v-else />
                </el-icon>
              </el-tooltip>
              <span class="separator">-----</span>
              <el-tooltip
                :content="
                  scope.row.execution_health_status === 'healthy'
                    ? '正常生成事件'
                    : '未配置事件规则'
                "
                :hide-after="0"
                placement="bottom"
              >
                <el-icon
                  :class="[
                    'status-icon',
                    scope.row.execution_health_status === 'healthy'
                      ? 'success'
                      : 'danger'
                  ]"
                >
                  <CircleCheckFilled
                    v-if="scope.row.execution_health_status === 'healthy'"
                  />
                  <CircleCloseFilled v-else />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="修改人" prop="updator" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.updator.display_name }}({{
              scope.row.updator.username
            }})
          </template>
        </el-table-column>
        <el-table-column label="修改时间" prop="utime">
          <template #default="scope">
            {{ formatTime(scope.row.utime) }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" max-width="330">
          <template #default="scope">
            <Perms :value="['ingestion:u']">
              <el-tooltip
                :hide-after="0"
                content="数据采集配置"
                placement="top"
              >
                <el-button link type="primary" @click="handleEdit(scope.row)">
                  <IconifyIconOffline
                    height="20"
                    icon="hugeicons:connect"
                    width="20"
                  />
                </el-button>
              </el-tooltip>
              <el-tooltip
                :hide-after="0"
                content="数据解析配置"
                placement="top"
              >
                <el-button link type="primary" @click="convert(scope.row)">
                  <IconifyIconOffline
                    height="20"
                    icon="icon-park-outline:file-conversion"
                    width="20"
                  />
                </el-button>
              </el-tooltip>
              <el-tooltip
                :hide-after="0"
                content="事件生成规则"
                placement="top"
              >
                <el-button link type="primary" @click="playBook(scope.row)">
                  <IconifyIconOffline
                    height="20"
                    icon="codicon:variable-group"
                    width="20"
                  />
                </el-button>
              </el-tooltip>
            </Perms>
            <Perms :value="['ingestion:r']">
              <el-tooltip :hide-after="0" content="查看日志" placement="top">
                <el-button link type="primary" @click="handleLogs(scope.row)">
                  <IconifyIconOffline height="20" icon="ix:log" width="20" />
                </el-button>
              </el-tooltip>
            </Perms>
            <Perms :value="['ingestion:d']">
              <el-tooltip :hide-after="0" content="删除" placement="top">
                <span class="ml-3">
                  <el-popconfirm
                    cancel-button-text="取消"
                    confirm-button-text="确认"
                    title="确认要删除该事件接入吗？"
                    @confirm="handleDelete(scope.row)"
                  >
                    <template #reference>
                      <el-button link type="danger">
                        <Icon height="20" icon="uiw:delete" width="20" />
                      </el-button>
                    </template>
                  </el-popconfirm>
                </span>
              </el-tooltip>
            </Perms>
          </template>
        </el-table-column>
      </el-table>

      <!-- 添加分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="size"
          :page-sizes="[15, 50, 100]"
          :total="total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      <!-- 新建事件接入组件 -->
      <EventIngestionAdd
        v-model:visible="drawerVisible"
        :is-edit="false"
        @refresh="loadData"
      />
      <EventIngestionEdit
        v-model:visible="editVisible"
        :is-edit="true"
        :row-data="currentEditRow"
        @refresh="loadData"
      />
      <convertConfig
        v-model:visible="convertVisible"
        :is-edit="true"
        :row-data="currentEditRow"
        @refresh="loadData"
      />
      <Binding
        v-model:visible="bindingVisible"
        :event-id="currentEditRow?.id"
        :event-name="currentEditRow?.event_ingestion_name"
        @update:visible="
          val => {
            bindingVisible = val;
            if (!val) loadData();
          }
        "
      />
      <IngestionLogs
        v-model:visible="logsVisible"
        :event-id="currentEditRow?.id"
      />
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import {
  CircleCheckFilled,
  CircleCloseFilled,
  Search
} from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import {
  eventIngestionDelete,
  eventIngestionExport,
  eventIngestionImport,
  eventIngestionList,
  eventIngestionUpdate
} from "@/api/event";
import EventIngestionAdd from "./components/eventIngestionAdd.vue";
import EventIngestionEdit from "./components/eventIngestionEdit.vue";
import convertConfig from "./components/convertConfig.vue";
import { message } from "@/utils/message";
import IngestionLogs from "./components/ingestion_logs.vue"; // 分页相关
import { Icon } from "@iconify/vue";
import Binding from "./components/binding.vue";

const currentPage = ref(1);
const size = ref(15);
const total = ref(0); // 总数据量
const searchName = ref(""); // 搜索关键词
const drawerVisible = ref(false); // 新建事件接入组件是否显示
const editVisible = ref(false); // 编辑事件接入组件是否显示
const convertVisible = ref(false); // 数据解析配置组件是否显示
const currentEditRow = ref(null); // 当前编辑行数据
const bindingVisible = ref(false);
const cronValue = ref(""); // 新增的 cronValue 引用
const logsVisible = ref(false); // 日志组件是否显示
// 模拟的表格数据
const tableData = ref([
  {
    id: "",
    event_ingestion_name: "",
    event_ingestion_describe: "",
    event_source_type: "",
    transforms_type: "",
    enabled_status: "",
    execution_health_status: "",
    release_status: "",
    utime: "",
    updator: ""
  }
]);

// 新建事件接入
const handleCreate = () => {
  drawerVisible.value = true;
};

// 搜索
const search = () => {
  console.log("搜索:", searchName.value);
  loadData();
};

// 接入配置
const handleEdit = (row: any) => {
  currentEditRow.value = row;
  editVisible.value = true;
};
// 剧本配置
const playBook = (row: any) => {
  currentEditRow.value = row;
  bindingVisible.value = true;
};
//数据解析配置
const convert = (row: any) => {
  console.log(row);
  currentEditRow.value = row;
  convertVisible.value = true;
};
// 日志
const handleLogs = (row: any) => {
  console.log(row);
  currentEditRow.value = row;
  logsVisible.value = true;
};
// 删除
const handleDelete = (row: any) => {
  eventIngestionDelete({
    id: row.id
  }).then((res: any) => {
    if (res.code === 0) {
      ElMessage.success(res.data);
      // 删除后重新加载数据
      loadData();
    } else {
      ElMessage.error("删除失败");
    }
  });
};

// 加载数据
const loadData = () => {
  eventIngestionList({
    page: currentPage.value,
    size: size.value,
    name: searchName.value
  }).then((res: any) => {
    console.log(res);
    tableData.value = res.data.events;
    total.value = res.data.total;
  });
};
// 页码改变
const handleCurrentChange = (val: number) => {
  console.log("当前页:", val);
  currentPage.value = val;
  loadData();
};

// 每页条数改变
const handleSizeChange = (val: number) => {
  console.log("每页条数:", val);
  size.value = val;
  loadData();
};

// 上游
const handleStatusChange = (row: any) => {
  eventIngestionUpdate({
    id: row.id,
    enabled_status: row.enabled_status
  }).then((res: any) => {
    message(res.data, { type: "success" });
    console.log("上游状态", res);
  });
};

// 处理联动执行状态改变
const handleLinkedExecutionChange = (row: any) => {
  eventIngestionUpdate({
    id: row.id,
    linked_execution: row.linked_execution
  }).then((res: any) => {
    if (res.code === 0) {
      message(res.data, { type: "success" });
      console.log("联动执行状态", res);
    }
  });
};

// 获取事件健康检查配置
const getEventHealthConfig = async (callback: (data: any) => void) => {
  ElMessage.info("事件健康检查配置");
};

// 保存事件健康检查配置
const handleEventHealthSave = (config: any) => {
  ElMessage.info("事件健康检查接口");
};
// 选中的行数据
const selectedRows = ref([]);
// 处理多选变化
const handleSelectionChange = val => {
  selectedRows.value = val.map((item: any) => item.id);
};

// 上传前校验 - 只允许JSON文件
const beforeUpload = (file: any) => {
  const isJson = file.type === "application/json";
  if (!isJson) {
    ElMessage.warning("只能上传 JSON 文件！");
    return false;
  }
  return true;
};
// 处理文件上传
const handleUpload = async (file: any) => {
  const formData = new FormData();
  formData.append("files", file.raw);
  try {
    const res = (await eventIngestionImport(formData)) as any;
    if (res.code === 0) {
      ElMessage.success("导入成功");
      loadData(); // 刷新列表
    } else {
      ElMessage.error(res.msg || "导入失败");
    }
  } catch (error) {
    console.error("导入失败:", error);
    ElMessage.error("导入失败");
  }
};

// 导出
const exportData = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning("请选择要导出的数据");
    return;
  }
  try {
    const IngestionExport = await eventIngestionExport({
      ids: selectedRows.value
    });
    // 创建 Blob 对象 - 后端返回的已经是 Blob 对象
    const blob = new Blob([IngestionExport as BlobPart], {
      type: "application/zip"
    });
    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    // 设置文件名称为"事件接入配置_时间戳.zip"格式
    const timestamp = Date.now();
    const fileName = `事件接入配置_${timestamp}.zip`;
    link.setAttribute("download", fileName);
    document.body.appendChild(link);
    link.click();
    // 清理
    window.URL.revokeObjectURL(url);
    document.body.removeChild(link);
    ElMessage.success("导出成功");
  } catch (error) {
    console.error("导出失败:", error);
    ElMessage.error("导出失败");
  }
};
// 时间格式化函数
const formatTime = timestamp => {
  if (!timestamp) return "";
  const date = new Date(timestamp);
  return date
    .toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false
    })
    .replace(/\//g, "-");
};
// 组件挂载时加载数据
onMounted(() => {
  loadData();
});
</script>

<style lang="scss" scoped>
.event-module-container {
  box-sizing: border-box;

  .top-actions {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    .search-area {
      display: flex;
      gap: 10px;

      .el-input {
        width: 300px;
      }
    }

    .operation-buttons {
      display: flex;
      gap: 10px;
    }
  }

  .el-table {
    margin-bottom: 16px;
  }

  // 添加分页容器样式
  .pagination-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 16px;
    .pagination-info {
      color: #606266;
    }
  }
}

.health-status {
  display: flex;
  align-items: center;
  justify-content: center;

  .status-icon {
    font-size: 24px;
    transition: all 0.3s ease;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;

    &:hover {
      transform: scale(1.1);
    }

    &.success {
      color: #67c23a;
      filter: drop-shadow(0 2px 4px rgba(103, 194, 58, 0.2));

      &:hover {
        color: #85ce61;
        filter: drop-shadow(0 4px 6px rgba(103, 194, 58, 0.3));
      }
    }

    &.danger {
      color: #f56c6c;
      filter: drop-shadow(0 2px 4px rgba(245, 108, 108, 0.2));

      &:hover {
        color: #f78989;
        filter: drop-shadow(0 4px 6px rgba(245, 108, 108, 0.3));
      }
    }

    &.info {
      color: #909399;
      filter: drop-shadow(0 2px 4px rgba(144, 147, 153, 0.2));

      &:hover {
        color: #a6a9ad;
        filter: drop-shadow(0 4px 6px rgba(144, 147, 153, 0.3));
      }
    }
  }

  .separator {
    margin: 0 8px;
    color: #dcdfe6;
    flex-shrink: 0;
    line-height: 1;
  }
}
</style>
