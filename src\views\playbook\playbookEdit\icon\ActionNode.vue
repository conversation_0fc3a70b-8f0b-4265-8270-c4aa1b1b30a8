<template>
  <div class="vue-wrap">
    <div class="vue-html">
      <div class="vue-html-title">
        <div class="vue-html-title-left">
          <div class="icon" @click.stop="$_copyNodeId()">
            <svg
              class="icon"
              height="20px"
              p-id="6449"
              t="1745398574021"
              version="1.1"
              viewBox="0 0 1024 1024"
              width="20px"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M853.333333 298.666667h-213.333333V170.666667c0-72.533333-55.466667-128-128-128H170.666667C98.133333 42.666667 42.666667 98.133333 42.666667 170.666667v341.333333c0 72.533333 55.466667 128 128 128h128v213.333333c0 72.533333 55.466667 128 128 128h426.666666c72.533333 0 128-55.466667 128-128V426.666667c0-72.533333-55.466667-128-128-128zM170.666667 554.666667c-25.6 0-42.666667-17.066667-42.666667-42.666667V170.666667c0-25.6 17.066667-42.666667 42.666667-42.666667h341.333333c25.6 0 42.666667 17.066667 42.666667 42.666667v128h-128c-72.533333 0-128 55.466667-128 128v128H170.666667z m384-170.666667v128c0 25.6-17.066667 42.666667-42.666667 42.666667H384v-128c0-25.6 17.066667-42.666667 42.666667-42.666667h128z m341.333333 469.333333c0 25.6-17.066667 42.666667-42.666667 42.666667H426.666667c-25.6 0-42.666667-17.066667-42.666667-42.666667v-213.333333h128c72.533333 0 128-55.466667 128-128V384h213.333333c25.6 0 42.666667 17.066667 42.666667 42.666667v426.666666z"
                fill="#2c2c2c"
                p-id="6450"
              />
            </svg>
          </div>

          <div class="text">动作</div>
        </div>
        <div class="vue-html-title-right">
          <div class="copy" @click.stop="$_copyNode()">
            <svg
              class="icon"
              height="20"
              p-id="2839"
              t="1748240616953"
              version="1.1"
              viewBox="0 0 1024 1024"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M720 192h-544A80.096 80.096 0 0 0 96 272v608C96 924.128 131.904 960 176 960h544c44.128 0 80-35.872 80-80v-608C800 227.904 764.128 192 720 192z m16 688c0 8.8-7.2 16-16 16h-544a16 16 0 0 1-16-16v-608a16 16 0 0 1 16-16h544a16 16 0 0 1 16 16v608z"
                fill="#2c2c2c"
                p-id="2840"
              />
              <path
                d="M848 64h-544a32 32 0 0 0 0 64h544a16 16 0 0 1 16 16v608a32 32 0 1 0 64 0v-608C928 99.904 892.128 64 848 64z"
                fill="#2c2c2c"
                p-id="2841"
              />
              <path
                d="M608 360H288a32 32 0 0 0 0 64h320a32 32 0 0 0 0-64zM608 520H288a32 32 0 1 0 0 64h320a32 32 0 1 0 0-64zM480 678.656H288a32 32 0 1 0 0 64h192a32 32 0 1 0 0-64z"
                fill="#2c2c2c"
                p-id="2842"
              />
            </svg>
          </div>
          <el-popconfirm
            v-if="!props.graphModel.editConfigModel.isSilentMode"
            :hide-after="0"
            cancel-button-text="取消"
            confirm-button-text="确认"
            title="确认删除该节点吗？"
            width="auto"
            @confirm.stop="$_deleteNode()"
          >
            <template #reference>
              <div class="delete" @click.stop>
                <svg
                  class="icon"
                  height="20"
                  p-id="3915"
                  t="1748240739833"
                  version="1.1"
                  viewBox="0 0 1024 1024"
                  width="20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M106.666667 213.333333h810.666666v42.666667H106.666667z"
                    fill="#d81e06"
                    p-id="3916"
                  />
                  <path
                    d="M640 128v42.666667h42.666667V128c0-23.573333-19.093333-42.666667-42.538667-42.666667H383.872A42.496 42.496 0 0 0 341.333333 128v42.666667h42.666667V128h256z"
                    fill="#d81e06"
                    p-id="3917"
                  />
                  <path
                    d="M213.333333 896V256H170.666667v639.957333C170.666667 919.552 189.653333 938.666667 213.376 938.666667h597.248C834.218667 938.666667 853.333333 919.68 853.333333 895.957333V256h-42.666666v640H213.333333z"
                    fill="#d81e06"
                    p-id="3918"
                  />
                  <path
                    d="M320 341.333333h42.666667v384h-42.666667zM490.666667 341.333333h42.666666v384h-42.666666zM661.333333 341.333333h42.666667v384h-42.666667z"
                    fill="#d81e06"
                    p-id="3919"
                  />
                </svg>
              </div>
            </template>
          </el-popconfirm>
          <div v-else class="delete" @click.stop>
            <svg
              class="icon"
              height="20"
              p-id="3915"
              t="1748240739833"
              version="1.1"
              viewBox="0 0 1024 1024"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M106.666667 213.333333h810.666666v42.666667H106.666667z"
                fill="#d81e06"
                p-id="3916"
              />
              <path
                d="M640 128v42.666667h42.666667V128c0-23.573333-19.093333-42.666667-42.538667-42.666667H383.872A42.496 42.496 0 0 0 341.333333 128v42.666667h42.666667V128h256z"
                fill="#d81e06"
                p-id="3917"
              />
              <path
                d="M213.333333 896V256H170.666667v639.957333C170.666667 919.552 189.653333 938.666667 213.376 938.666667h597.248C834.218667 938.666667 853.333333 919.68 853.333333 895.957333V256h-42.666666v640H213.333333z"
                fill="#d81e06"
                p-id="3918"
              />
              <path
                d="M320 341.333333h42.666667v384h-42.666667zM490.666667 341.333333h42.666666v384h-42.666666zM661.333333 341.333333h42.666667v384h-42.666667z"
                fill="#d81e06"
                p-id="3919"
              />
            </svg>
          </div>
        </div>
      </div>
      <div class="vue-html-container">
        <el-form>
          <el-form-item>
            <div
              :title="props.properties.node_name || '未命名节点'"
              class="container-text ellipsis"
            >
              {{
                (props.properties.node_name || "未命名节点").length > 11
                  ? (props.properties.node_name || "未命名节点").substring(
                      0,
                      11
                    ) + "..."
                  : props.properties.node_name || "未命名节点"
              }}
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>

  <div v-if="props.properties.isWebSocket" class="webSocket">
    <div class="webSocket-result">
      <div class="webSocket-result-true" @click.stop="openDialog()">
        <div class="webSocket-result-left">
          <el-icon class="webSocket-result-icon">
            <CircleCheckFilled />
          </el-icon>
          <div class="webSocket-result-text">运行成功</div>
          <div class="webSocket-result-time">1s</div>
        </div>
        <div
          class="webSocket-result-right"
          @click.stop="$_closePlaybookStart()"
        >
          <el-icon>
            <Close />
          </el-icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElIcon,
  ElPopconfirm
} from "element-plus";
import usePlaybookStore from "@/store/modules/playbook";
import { CircleCheckFilled, Close } from "@element-plus/icons-vue";
import { computed, ref, watchEffect } from "vue";

const props = defineProps({
  id: {
    type: String
  },
  properties: {
    type: Object,
    required: true
  },
  model: {
    type: Object
  },
  graphModel: {
    type: Object
  },
  editConfigModel: {
    type: Object
  },
  text: {
    type: String
  },
  onBtnCopyClick: Function,
  onBtnDelClick: Function,
  onBtnWebSocket: Function,
  onBtnCloseClick: Function
});

const playbookPermissions = (() => {
  try {
    const permissions = localStorage.getItem("playbookPermissions");
    return permissions ? JSON.parse(permissions) : {};
  } catch (error) {
    console.error("解析权限配置失败:", error);
    return {};
  }
})();
// 打开webSocket通信结果dialog
const openDialog = () => {
  props.onBtnWebSocket();
};

//复制节点
const $_copyNode = () => {
  // 添加安全检查
  if (!playbookPermissions || !playbookPermissions[props.id]) {
    console.warn(`权限配置缺失: ${props.id}`);
    // 如果没有权限配置，默认允许复制
    props.onBtnCopyClick();
    console.log(props.id);
    return;
  }

  if (playbookPermissions[props.id].includes("u")) {
    props.onBtnCopyClick();
    console.log(props.id);
  } else {
    console.warn(`节点 ${props.id} 没有复制权限`);
  }
};

//删除节点
const $_deleteNode = () => {
  // 添加安全检查
  if (!playbookPermissions || !playbookPermissions[props.id]) {
    console.warn(`权限配置缺失: ${props.id}`);
    // 如果没有权限配置，默认允许删除
    props.onBtnDelClick();
    return;
  }

  if (playbookPermissions[props.id].includes("d")) {
    props.onBtnDelClick();
  } else {
    console.warn(`节点 ${props.id} 没有删除权限`);
  }
};

//清空剧本执行结果
const $_closePlaybookStart = () => {
  props.onBtnCloseClick();
};

// 复制节点ID
const $_copyNodeId = async () => {
  try {
    // 使用现代Clipboard API替代已废弃的execCommand
    await navigator.clipboard.writeText(`\$\{${props.id}\}`);
  } catch (err) {
    console.error("复制失败:", err);
    // 添加失败反馈（可根据项目需求替换为错误提示）
  }
};
</script>

<style lang="scss" scoped>
.vue-wrap {
  display: flex;
  align-items: center;
  justify-content: center;

  .vue-html {
    width: 190px;
    height: 90px;
    overflow: hidden;
    border: 1px solid rgba(223, 225, 229, 0.8);
    border-radius: 6px;
    box-shadow:
      0 4px 8px rgba(0, 0, 0, 0.1),
      0 2px 4px rgba(0, 0, 0, 0.1);

    .vue-html-title {
      display: flex;
      justify-content: space-between;
      height: 30px;
      color: black;
      padding: 3px 5px;
      background: linear-gradient(#f2f2ff 0%, rgba(252, 252, 255, 1) 100%);

      .vue-html-title-left {
        display: flex;

        .icon {
          cursor: pointer;
        }

        .text {
          padding-left: 5px;
          font-size: 13px;
        }
      }

      .vue-html-title-right {
        display: flex;

        .copy {
          cursor: pointer;
        }

        .delete {
          cursor: pointer;
          margin-left: 10px;
        }
      }
    }

    .vue-html-container {
      display: flex;
      justify-content: center;
      height: 70px;
      background: white;
      padding-top: 10px;

      .container-text {
        font-size: 13px;
        max-width: 100%;
      }

      .ellipsis {
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

.webSocket {
  width: 200px;
  height: auto;
  color: white;
  margin-top: 5px;

  .webSocket-result {
    display: flex;
    justify-content: center;

    .webSocket-result-true {
      border: 1px solid rgba(223, 225, 229, 0.8);
      border-radius: 6px;
      /* 核心阴影 */
      box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.1),
        0 2px 4px rgba(0, 0, 0, 0.1);
      width: 190px;
      height: 30px;
      background: rgba(241, 248, 244, 0.7);
      color: black;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;

      .webSocket-result-left {
        display: flex;
        align-items: center;
        padding-left: 5px;

        .webSocket-result-icon {
          width: 20px;
          height: 20px;
          background: linear-gradient(
            145deg,
            var(--primary-green) 0%,
            var(--success-dark) 100%
          );
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #009624;
          font-size: 24px;
          box-shadow: 0 4px 8px rgba(0, 200, 83, 0.3);
          animation: pulse 2s infinite ease-in-out;
        }

        .webSocket-result-text {
          padding-left: 5px;
          font-size: 13px;
          font-weight: 500;
          color: #202124;
          letter-spacing: -0.3px;
        }

        .webSocket-result-time {
          padding-left: 5px;
          font-size: 13px;
          color: #00c853;
          font-weight: 500;
        }
      }

      .webSocket-result-right {
        padding-right: 5px;
        display: flex;
        align-items: center;
      }
    }

    .el-button {
      padding: 0px 0px;
      margin: 0px 0px;
      border: 1px solid rgba(223, 225, 229, 0.8);
      box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.08),
        0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .el-button:hover {
      box-shadow: 0 6px 16px rgba(0, 200, 83, 0.12);
      transform: translateY(-2px);
    }
  }
}

.webSocket-running {
  display: flex;
  justify-content: center;
}
</style>
