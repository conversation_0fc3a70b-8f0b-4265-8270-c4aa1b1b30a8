<script lang="ts" setup>
import { ref } from "vue";
import { message } from "@/utils/message";
import { ReQrcode } from "@/components/ReQrcode";
import { mfaAuth } from "@/api/userList";
import { useRouter } from "vue-router";
import { getTopMenu, initRouter } from "@/router/utils";
import { useUserStoreHook } from "@/store/modules/user";
import { setToken } from "@/utils/auth";
import { Lock } from "@element-plus/icons-vue";
import { useRefreshTokenStore } from "@/store/modules/RefreshToken"; // 导入刷新token的store

const router = useRouter(); // 导入路由
const visible = ref(false); // 控制弹窗的显示
const qrcodeText = ref(""); // 二维码文本
const currentUser = ref<any>(null); // 当前用户
const code = ref(""); // 验证码输入框
const showQrcode = ref(false); // 是否显示二维码
const mfa = ref(false); // 是否已经完成MFA认证
const loading = ref(false); // 加载状态
const countdown = ref(300); // 5分钟倒计时
let timer: ReturnType<typeof setInterval> | null = null;

// 打开弹窗
const openDialog = (row: any) => {
  currentUser.value = row;
  // 根据是否需要绑定MFA来决定是否显示二维码
  showQrcode.value = !row.mfa_bind;
  if (showQrcode.value) {
    qrcodeText.value = row.mfa_uri || "";
  }
  code.value = ""; // 清空验证码输入框
  visible.value = true;
  loading.value = false; // 重置加载状态
  // 开始倒计时
  countdown.value = 300;
  if (timer) clearInterval(timer);
  timer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer!);
      message("验证超时，请重新登录", { type: "warning" });
      visible.value = false;
      useUserStoreHook().logOut();
      router.push("/login");
    }
  }, 1000);
};

// 确认MFA验证
const handleConfirm = () => {
  if (loading.value) return; // 如果正在加载中，阻止重复提交
  if (!code.value) {
    message("请输入验证码", { type: "warning" });
    return;
  }
  loading.value = true; // 开始加载
  mfaAuth({
    code: code.value
  })
    .then((res: any) => {
      if (res.code === 0) {
        if (timer) clearInterval(timer);
        // 后端通过Set-Cookie自动设置token，无需前端手动设置
        // setToken({
        //   accessToken: res.data["aioe-token"],
        //   refreshToken: res.data["aioe-token"],
        //   expires: new Date(Date.now() + 30 * 60 * 1000)
        // });
        // 启动刷新token的定时器
        useRefreshTokenStore().startRefreshTimer();
        message(showQrcode.value ? "MFA绑定成功" : "多因素认证成功", {
          type: "success"
        });
        mfa.value = true;
        visible.value = false;
        // 清除多因素认证标记
        localStorage.removeItem("needMfaAuth");
        // 认证成功后，初始化路由并跳转到首页
        initRouter().then(() => {
          router.push(getTopMenu(true).path);
        });
      } else {
        message(res.message || "验证失败", { type: "error" });
      }
    })
    .catch(error => {
      message("验证请求发生错误", { type: "error" });
      console.error("MFA验证错误:", error);
    })
    .finally(() => {
      loading.value = false; // 结束加载
    });
};

// 取消验证
const handleCancel = () => {
  if (loading.value) return; // 如果正在加载中，阻止关闭
  if (timer) clearInterval(timer);
  code.value = "";
  visible.value = false;
  // 只有在未完成MFA认证时才执行登出操作
  if (mfa.value === false) {
    useUserStoreHook().logOut();
    router.push("/login");
  }
};

defineExpose({
  openDialog
});
</script>

<template>
  <el-dialog
    v-model="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :title="showQrcode ? 'MFA绑定' : '多因素认证'"
    width="400px"
    @close="handleCancel"
  >
    <div class="text-center">
      <!-- 首次绑定显示二维码 -->
      <template v-if="showQrcode">
        <p class="mb-4">请使用扫描以下二维码</p>
        <ReQrcode
          :options="{
            color: {
              dark: '#55D187',
              light: '#ffffff'
            }
          }"
          :text="qrcodeText"
          :width="200"
        />
        <p class="mt-4 text-gray-500">扫描完成后，请输入显示的6位验证码</p>
      </template>
      <!-- 已绑定只显示验证码输入框 -->
      <template v-else>
        <p class="mb-4">请输入验证码</p>
        <p class="mt-4 text-gray-500">请输入验证码，然后点击确定登录</p>
      </template>
      <!-- 剩余时间 -->
      <p class="mt-4 text-gray-500">
        剩余时间：<span style="color: #55d187">{{ countdown }}</span> 秒
      </p>
      <!-- 验证码输入框 -->
      <el-input
        v-model="code"
        :disabled="loading"
        class="mt-4 verification-input"
        maxlength="6"

        @keyup.enter.prevent="handleConfirm"
      >
        <template #prefix>
          <el-icon class="el-input__icon">
            <Lock />
          </el-icon>
        </template>
      </el-input>
    </div>
    <template #footer>
      <el-button :disabled="loading" @click="handleCancel">取消</el-button>
      <el-button :loading="loading" type="primary" @click="handleConfirm"
        >确定
      </el-button>
    </template>
  </el-dialog>
</template>

<style scoped>
.verification-input {
  width: 300px;
  margin: 0 auto;
  margin-top: 20px;
}

:deep(.el-input__wrapper) {
  padding-left: 11px;
}

:deep(.el-input__icon) {
  margin-right: 8px;
}
</style>
