<template>
  <div class="dialog">
    <el-form
      v-if="data"
      ref="playbookDialogFormRef"
      :model="data"
      :rules="rules"
      label-position="right"
      label-width="75px"
    >
      <el-form-item label="名称:" prop="name">
        <el-input v-model="data.name" />
      </el-form-item>
      <el-form-item label="描述:" prop="remark">
        <div style="width: 100%; border: 1px solid #ccc">
          <Toolbar :defaultConfig="toolbarConfig" :editor="editorRef" />
          <Editor
            v-model="data.remark"
            :defaultConfig="editorConfig"
            style="height: 330px; overflow-y: hidden"
            @onCreated="handleCreated"
          />
        </div>
      </el-form-item>
      <el-form-item label="场景:" prop="scenes">
        <el-select
          v-if="allScene"
          v-model="data.scenes"
          multiple
          placeholder="请选择场景"
          value-key="id"
        >
          <el-option
            v-for="item in allScene.scenes"
            :key="item.id"
            :label="item.name"
            :value="item.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="标签:" prop="tags">
        <el-select v-model="data.tags" multiple>
          <el-option
            v-for="(item, index) in allTagList"
            :key="index"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { computed, onBeforeUnmount, onMounted, ref, shallowRef } from "vue";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue"; //引入富文本编辑器
import "@wangeditor/editor/dist/css/style.css"; //引入富文本编辑器css
import { ElLoading, ElMessage } from "element-plus";
import { apiGetPlaybookTagList, apiGetSceneAll } from "@/api/playbook";

const props = defineProps({
  formData: {
    type: Object
  }
});

const emit = defineEmits(["update:formData"]);

onMounted(() => {
  getAllScene();
  getPlaybookTagList();
});

const imageContent = ref("");
const allScene = ref();
const allTagList = ref([]);
const playbookDialogFormRef = ref();

// 表单验证规则
const rules = {
  name: [
    { required: true, message: "剧本名称必填", trigger: "change" },
    { min: 0, max: 30, message: "剧本名称不能超过30个字", trigger: "change" }
  ]
};

// 暴露给父组件的验证方法
const validateForm = () => {
  return new Promise(resolve => {
    if (!playbookDialogFormRef.value) return resolve(false);
    playbookDialogFormRef.value.validate(valid => {
      resolve(valid); // true: 验证通过, false: 验证失败
    });
  });
};

//暴露给父组件的清空表单验证方法
const clearValidateForm = () => {
  playbookDialogFormRef.value.clearValidate();
};

//对该表单项进行重置，将其值重置为初始值并移除校验结果
const resetFieldForm = () => {
  playbookDialogFormRef.value.resetFields();
};

const data = computed({
  get() {
    return props.formData;
  },
  set(value) {
    emit("update:formData", value);
  }
});

//获取所有场景
const getAllScene = async () => {
  let res: any = await apiGetSceneAll({});
  allScene.value = res.data;
  console.log(allScene.value);
};

//获取所有标签
const getPlaybookTagList = async () => {
  let res: any = await apiGetPlaybookTagList({});
  allTagList.value = res.data;
  console.log(allTagList.value);
};
//富文本编辑器
//编辑器实例，必须用 shallowRef
const editorRef = shallowRef();
// 工具栏配置(隐藏全部菜单)
const toolbarConfig = {
  toolbarKeys: [],
  hideKeys: [
    "fullScreen",
    "insertTable",
    "codeBlock",
    "insertLink",
    "todo",
    "emotion",
    "fontFamily",
    "fontSize",
    "lineHeight",
    "bulletedList",
    "numberedList",
    "blockquote",
    "code",
    "clearStyle",
    "divider",
    "redo",
    "undo",
    "through",
    "italic",
    "bold",
    "color",
    "backgroundColor",
    "justifyCenter",
    "justifyLeft",
    "justifyRight",
    "indent",
    "delIndent",
    "header1",
    "header2",
    "header3",
    "uploadImage"
  ]
};
const editorConfig = {
  placeholder: "",
  MENU_CONF: {
    uploadImage: {
      // server: "/api/upload/image",
      fieldName: "file",
      maxFileSize: 10 * 1024 * 1024,
      maxNumberOfFiles: 10,
      allowedFileTypes: ["image/*"],
      metaWithUrl: true,
      customUpload: async (file, insertFn) => {
        const loading = ElLoading.service({
          lock: true,
          text: "图片处理中...",
          background: "rgba(0, 0, 0, 0.7)"
        });

        try {
          const compressedFile = await compressImage(file);
          const reader = new FileReader();
          reader.onload = () => {
            const base64 = reader.result as string;
            imageContent.value = base64;
            insertFn(base64, file.name, base64);
            // ElMessage.success({
            //   message: "图片上传成功",
            //   duration: 1000
            // });

            loading.close();
          };
          reader.onerror = () => {
            ElMessage.error("图片读取失败");
            loading.close();
          };
          reader.readAsDataURL(compressedFile);
        } catch (error) {
          ElMessage.error("图片处理失败");
          loading.close();
        }
      }
    }
  }
};
// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});
const handleCreated = editor => {
  editorRef.value = editor; // 记录 editor 实例，重要！
};

// 图片压缩方法
const compressImage = async (file: File): Promise<File> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = URL.createObjectURL(file);
    img.onload = () => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      const MAX_WIDTH = 1600;
      const MAX_HEIGHT = 1200;
      let { width, height } = img;

      if (width > height) {
        if (width > MAX_WIDTH) {
          height *= MAX_WIDTH / width;
          width = MAX_WIDTH;
        }
      } else {
        if (height > MAX_HEIGHT) {
          width *= MAX_HEIGHT / height;
          height = MAX_HEIGHT;
        }
      }

      canvas.width = width;
      canvas.height = height;
      ctx?.drawImage(img, 0, 0, width, height);

      const fileType = file.type || "image/jpeg";
      const mimeType = fileType === "image/png" ? "image/png" : "image/jpeg";

      canvas.toBlob(
        blob => {
          if (blob) {
            const newFile = new File([blob], file.name, {
              type: mimeType,
              lastModified: Date.now()
            });
            resolve(newFile);
          } else {
            reject(new Error("图片压缩失败"));
          }
        },
        mimeType,
        0.9
      );
    };
    img.onerror = () => reject(new Error("图片加载失败"));
  });
};

//对外暴露方法
defineExpose({ validateForm, clearValidateForm, resetFieldForm });
</script>

<style lang="scss" scoped>
.dialog {
  border: 1px solid #e4e7ed;
  padding: 20px;
}

.form-remark {
  width: 100%;
}
</style>
