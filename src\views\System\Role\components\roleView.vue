<template>
  <el-dialog v-model="visible" title="查看角色信息" width="800px" top="5vh">
    <el-scrollbar v-if="roleData" class="dialog-scroll" height="70vh">
      <div class="role-info">
        <el-descriptions :column="1" border size="large">
          <el-descriptions-item label="角色 ID">
            <span class="role-id">{{ roleData.id }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="角色名称">
            <el-tag type="primary" size="large">{{ roleData.name }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="角色描述">
            <span class="description">{{ roleData.description }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="权限信息">
            <div class="permission-content">
              <el-collapse v-model="activeNames">
                <el-collapse-item
                  v-for="(module, index) in formattedPermissions"
                  :key="index"
                  :title="module.title"
                  :name="index.toString()"
                >
                  <div class="permission-list">
                    <div
                      v-for="(perm, permIndex) in module.permissions"
                      :key="permIndex"
                      class="permission-item"
                    >
                      <el-icon class="permission-icon"><Check /></el-icon>
                      <span class="permission-text">{{ perm }}</span>
                    </div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            <span class="time">{{ formatTime(roleData.ctime) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            <span class="time">{{ formatTime(roleData.utime) }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="visible = false">关 闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { computed, defineProps, onMounted, ref } from "vue";
import { searchRole } from "@/api/role"; // 假设这是获取角色信息的 API
import { Check } from "@element-plus/icons-vue";

const props = defineProps({
  roleId: {
    type: String,
    required: true
  }
});

const visible = ref(true); // 控制弹出框的显示
const roleData = ref(null); // 存储角色数据
const activeNames = ref<string[]>([]); // 控制折叠面板展开状态

// 添加时间格式化函数
const formatTime = (timestamp: string | number) => {
  if (!timestamp) return "";
  const date = new Date(timestamp);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false
  });
};

// 格式化权限数据用于表单显示
const formattedPermissions = computed(() => {
  if (
    !roleData.value?.permissions ||
    !Array.isArray(roleData.value.permissions)
  ) {
    return [];
  }

  return roleData.value.permissions.map(module => ({
    title: module.model_description,
    permissions: module.sp.map(sp => {
      const key = Object.keys(sp)[0];
      const value = sp[key];
      return value;
    })
  }));
});

interface RoleResponse {
  data: {
    id: string;
    name: string;
    description: string;
    permissions: string;
    ctime: string;
    utime: string;
  };
}

// 在组件挂载时请求角色信息
onMounted(async () => {
  try {
    const response = (await searchRole({
      role_id: props.roleId
    })) as RoleResponse;
    roleData.value = response.data;

    // 设置所有权限模块默认展开
    if (
      roleData.value?.permissions &&
      Array.isArray(roleData.value.permissions)
    ) {
      activeNames.value = roleData.value.permissions.map((_, index) =>
        index.toString()
      );
    }

    console.log("角色信息:", roleData.value);
  } catch (error) {
    console.error("获取角色信息失败:", error);
  }
});
</script>

<style scoped>
.role-info {
  padding: 20px 0;
}

.role-id {
  font-family: "Courier New", monospace;
  color: #666;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.description {
  color: #333;
  line-height: 1.6;
}

.time {
  color: #666;
  font-size: 14px;
}

.permission-content {
  /* 移除固定高度，让 el-scrollbar 控制滚动 */
}

.permission-list {
  padding: 0;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4px 20px;
  width: 100%;
}

.permission-item {
  display: flex;
  align-items: center;
  padding: 6px 0;
}

.permission-icon {
  color: #52c41a;
  margin-right: 8px;
  font-size: 14px;
  flex-shrink: 0;
}

.permission-text {
  color: #333;
  font-size: 14px;
  line-height: 1.5;
  flex: 1;
}

:deep(.el-collapse-item__header) {
  background-color: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
  font-weight: 600;
  color: #333;
  padding-left: 16px;
}

:deep(.el-collapse-item__content) {
  background-color: #fff;
  padding: 16px;
}

:deep(.el-collapse) {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  width: 100%;
}

:deep(.el-collapse-item) {
  width: 100%;
}

/* 响应式布局：小屏幕时改为单列 */
@media (max-width: 768px) {
  .permission-list {
    grid-template-columns: 1fr;
  }
}

:deep(.el-descriptions) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

:deep(.el-descriptions__label) {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
  width: 120px;
}

:deep(.el-descriptions__content) {
  background-color: #fff;
  color: #333;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
}

:deep(.el-dialog__header) {
  margin-right: 0;
  border-bottom: 1px solid #ebeef5;
  padding: 20px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  border-top: 1px solid #ebeef5;
  padding: 20px;
}

.dialog-scroll {
  width: 100%;
}

:deep(.el-dialog__body) {
  padding: 0;
}

.role-info {
  padding: 20px;
}
</style>
