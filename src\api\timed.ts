import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

// 获取定时任务列表
export const getTimedList = (data: any) => {
  return http.post(baseUrlApi("timed/all"), { data });
};

// 更新定时任务
export const updateTimed = (data: any) => {
  return http.post(baseUrlApi("timed/update"), { data });
};

// 删除定时任务
export const deleteTimed = (data: any) => {
  return http.post(baseUrlApi("timed/delete"), { data });
};

// 添加定时任务
export const createTimed = (data: any) => {
  return http.post(baseUrlApi("timed/add"), { data });
};

// 获取定时任务执行日志
export const getTimedRunLog = (data: any) => {
  return http.post(baseUrlApi("timed/runlog"), { data });
};
