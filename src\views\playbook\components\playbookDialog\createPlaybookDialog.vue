<template>
  <div>
    <el-dialog v-model="dialogVisible" title="新建剧本" top="5vh" width="700px">
      <template #default>
        <div>
          <playbookDialogForm
            ref="playbookDialogFormRef"
            v-model:formData="formData"
          />
        </div>
      </template>
      <template #footer>
        <div>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirm()"> 确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { apiCreatePlaybook } from "@/api/playbook";
import { nextTick, ref } from "vue";
import playbookDialogForm from "@/views/playbook/components/playbookDialog/playbookDialogForm.vue";
import { ElMessage } from "element-plus";

interface formDataType {
  id?: string;
  name: string;
  remark: string;
  tags: any;
  scenes: any;
}

const emit = defineEmits(["apiGet"]);

const dialogVisible = ref(false);
const playbookDialogFormRef = ref();
//表单数据
const formData = ref<formDataType>({
  name: "",
  remark: "",
  tags: [],
  scenes: []
});

//初始数据
const data = [
  {
    nodes: [
      {
        type: "start-node",
        x: 100,
        y: 100,
        properties: {
          scale: 1
        }
      },
      {
        type: "end-node",
        x: 1000,
        y: 100,
        properties: {
          scale: 1
        }
      }
    ]
  }
];

//打开dialog(并在defineExpose中对外暴露该方法)
const openDialog = () => {
  dialogVisible.value = true;
  nextTick(() => {
    if (playbookDialogFormRef.value) {
      playbookDialogFormRef.value.resetFieldForm();
    }
  });
};

// 确认按钮处理函数
const handleConfirm = async () => {
  if (!playbookDialogFormRef.value) return;

  // 调用子组件验证方法 [1,2,6](@ref)
  const isValid = await playbookDialogFormRef.value.validateForm();

  if (isValid) {
    // 验证通过，执行业务逻辑
    await createPlaybook();
  } else {
    // 验证失败处理
    console.log("表单校验未通过");
  }
};

//新建剧本
const createPlaybook = async () => {
  let res: any = await apiCreatePlaybook({
    name: formData.value.name,
    remark: formData.value.remark,
    scenes: formData.value.scenes,
    tags: formData.value.tags,
    flow_json_list: data
  });
  if (res.code == 0) {
    ElMessage.success("新建剧本成功");
  } else {
    ElMessage.error("新建剧本失败");
  }
  dialogVisible.value = false;
  //新建剧本后，再次请求剧本列表数据
  emit("apiGet");
};

//对外暴露方法
defineExpose({
  openDialog
});
</script>

<style lang="scss" scoped></style>
