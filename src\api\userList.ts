import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";
// 获取用户列表
export const getUserList = (data: any) => {
  return http.post(baseUrlApi("user/list"), { data });
};
//获取所有API账号信息
export const getApiUserList = (data: any) => {
  return http.post(baseUrlApi("user/list-token"), { data });
};
//新建API账号
export const createApiUser = (data: any) => {
  return http.post(baseUrlApi("user/new-token"), { data });
};
//删除API账号
export const deleteApiUser = (data: any) => {
  return http.post(baseUrlApi("user/del-token"), { data });
};
// 新建用户
export const createUser = (data: any) => {
  return http.post(baseUrlApi("user/new"), { data });
};
// 搜索用户
export const SearchUser = (data: any) => {
  return http.post(baseUrlApi("user/search"), { data });
};

// 删除用户
export const deleteUser = (data: any) => {
  return http.post(baseUrlApi("user/del"), { data });
};

// 修改任意用户信息（管理员）
export const editUser = (data: any) => {
  return http.post(baseUrlApi("user/edit"), { data });
};
// 修改用户多因素认证
export const editUserMfa = (data: any) => {
  return http.post(baseUrlApi("user/reset-mfa-key"), { data });
};
// 修改用户信息(用户自己)
export const editUserInfo = (data: any) => {
  return http.post(baseUrlApi("user/update"), { data });
};
// 获取用户多因素认证的密钥
export const getUserMfa = (data: any) => {
  return http.post(baseUrlApi("user/get-mfa-key"), { data });
};

//重置MFA密钥（需要user:a权限）
export const resetUserMfa = (data: any) => {
  return http.post(baseUrlApi("user/reset-mfa-key"), { data });
};
// 多因素认证
export const mfaAuth = (data: any) => {
  return http.post(baseUrlApi("user/mfa-auth"), { data });
};

//给账号分配角色（需要user:a权限）
export const setUserRole = (data: any) => {
  return http.post(baseUrlApi("user/set-user-role"), { data });
};

//获取用户名称、id
export const apiuserAccountList = (data: any) => {
  return http.post(baseUrlApi("user/account-list"), { data });
};

//获取角色名称、id
export const apiUserRoleList = (data: any) => {
  return http.post(baseUrlApi("user/role-list"), { data });
};
