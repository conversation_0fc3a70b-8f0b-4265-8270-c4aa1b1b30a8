<template>
  <el-card>
    <div class="flex items-center gap-[10px] mb-[15px]">
      <label class="w-[100px] dark:text-white">登录背景:</label>
      <el-upload
        ref="backgroundUpload"
        :auto-upload="false"
        :limit="1"
        :on-change="(file, files) => handleChange(file, files, 'background')"
        :on-exceed="handleExceed"
        :on-preview="handlePictureCardPreview"
        :on-remove="file => handleRemove(file, 'background')"
        action="#"
        list-type="picture-card"
      >
        <el-icon>
          <Plus />
        </el-icon>
      </el-upload>
    </div>

    <div class="flex items-center gap-[10px] mb-[15px]">
      <label class="w-[100px] dark:text-white">系统Logo:</label>
      <el-upload
        ref="logoUpload"
        :auto-upload="false"
        :limit="1"
        :on-change="(file, files) => handleChange(file, files, 'logo')"
        :on-exceed="handleExceed"
        :on-preview="handlePictureCardPreview"
        :on-remove="file => handleRemove(file, 'logo')"
        action="#"
        list-type="picture-card"
      >
        <el-icon>
          <Plus />
        </el-icon>
      </el-upload>
    </div>

    <div class="flex items-center gap-[10px] mb-[15px]">
      <label class="w-[100px] dark:text-white">系统名称:</label>
      <el-input
        v-model="systemConfig.system_name"

      />
    </div>

    <div class="flex items-center gap-[10px] mb-[15px]">
      <label class="w-[100px] dark:text-white">系统版本:</label>
      <el-input
        v-model="systemConfig.system_version"

      />
    </div>

    <div class="flex items-center gap-[10px] mb-[15px]">
      <label class="w-[100px] dark:text-white">CopyRight:</label>
      <el-input
        v-model="systemConfig.copyright"

      />
    </div>

    <Perms :value="['system:c']">
      <div class="flex justify-center">
        <el-button
          class="w-[100px] mt-[10px]"
          type="primary"
          @click="saveSystemInfo"
          >保存
        </el-button>
      </div>
    </Perms>
    <el-dialog v-model="dialogVisible">
      <img :src="dialogImageUrl" alt="Preview Image" w-full />
    </el-dialog>
  </el-card>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { getConfig, setConfig } from "@/config";
import { systemUpload } from "@/api/system";
import type { UploadFile, UploadFiles, UploadInstance } from "element-plus";
import { ElMessage } from "element-plus";
import { Plus } from "@element-plus/icons-vue";

interface SystemConfigData {
  background: UploadFile | null;
  logo: UploadFile | null;
  copyright: string;
  system_name: string;
  system_version: string;
}

const backgroundUpload = ref<UploadInstance>();
const logoUpload = ref<UploadInstance>();

const dialogImageUrl = ref("");
const dialogVisible = ref(false);

const systemConfig = ref<SystemConfigData>({
  background: null,
  logo: null,
  copyright: String(getConfig("Content") || ""),
  system_name: String(getConfig("Title") || ""),
  system_version: String(getConfig("Version") || "")
});

// 处理文件超出限制
const handleExceed = () => {
  ElMessage.warning("只能上传一个文件");
};

// 处理文件改变 - 根据不同的上传类型保存到对应的字段
const handleChange = (
  file: UploadFile,
  files: UploadFiles,
  type: keyof Pick<SystemConfigData, "background" | "logo">
) => {
  systemConfig.value[type] = file;
};

// 处理文件移除 - 根据不同的上传类型清除对应的字段
const handleRemove = (
  file: UploadFile,
  type: keyof Pick<SystemConfigData, "background" | "logo">
) => {
  systemConfig.value[type] = null;
};

// 处理图片预览
const handlePictureCardPreview = (file: UploadFile) => {
  dialogImageUrl.value = file.url!;
  dialogVisible.value = true;
};

const saveSystemInfo = async () => {
  try {
    // 创建 FormData 对象
    const formData = new FormData();

    // 添加文件
    if (systemConfig.value.background?.raw) {
      formData.append("background", systemConfig.value.background.raw);
    }
    if (systemConfig.value.logo?.raw) {
      formData.append("logo", systemConfig.value.logo.raw);
    }

    // 添加其他数据
    formData.append("copyright", systemConfig.value.copyright);
    formData.append("system_name", systemConfig.value.system_name);
    formData.append("system_version", systemConfig.value.system_version);

    // 调用上传API
    const res = (await systemUpload(formData)) as any;
    if (res.code === 0) {
      // 更新本地配置
      const newConfig = {
        Content: systemConfig.value.copyright,
        Title: systemConfig.value.system_name,
        Version: systemConfig.value.system_version
      };
      setConfig(newConfig);
      ElMessage.success("保存成功");
      // 刷新页面
      window.location.reload();
    }
  } catch (error) {
    console.error(error);
  }
};
</script>
