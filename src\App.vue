<template>
  <el-config-provider :locale="currentLocale">
    <router-view />
    <ReDialog />
    <FloatButton v-if="showFloatButton" />
  </el-config-provider>
</template>

<script lang="ts">
import { defineComponent, onMounted } from "vue";
import { ElConfigProvider } from "element-plus";
import { ReDialog } from "@/components/ReDialog";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import { useRefreshTokenStore } from "@/store/modules/RefreshToken";
import { getToken } from "@/utils/auth";
import { initRouter } from "@/router/utils";

export default defineComponent({
  name: "app",
  components: {
    [ElConfigProvider.name]: ElConfigProvider,
    ReDialog
  },
  setup() {
    const refreshTokenStore = useRefreshTokenStore();
    onMounted(async () => {
      // 如果存在用户信息，启动刷新定时器并初始化路由
      const userInfo = getToken();
      if (userInfo?.id) {
        // 后端通过Set-Cookie自动管理token，前端只需要检查用户信息是否存在
        refreshTokenStore.startRefreshTimer();
        await initRouter();
      }
    });
  },
  computed: {
    currentLocale() {
      return zhCn;
    },
    // 作战室悬浮窗显示控制
    showFloatButton(): boolean {
      return (
        !this.$route.path.includes("/warroom") &&
        !this.$route.path.includes("/login") &&
        !this.$route.path.includes("/loading")
      );
    }
  }
});
</script>
