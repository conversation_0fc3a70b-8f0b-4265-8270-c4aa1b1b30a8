import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

// 创建作战室
export const createRoom = (data: any) => {
  return http.post(baseUrlApi("warroom/create"), { data });
};

// 加入作战室
export const joinRoom = (data: any) => {
  return http.post(baseUrlApi("warroom/join"), { data });
};

// 退出作战室
export const quitRoom = (data: any) => {
  return http.post(baseUrlApi("warroom/quit"), { data });
};

//取消关注作战室
export const unstarRoom = (data: any) => {
  return http.post(baseUrlApi("warroom/unstar_room"), { data });
};

// 获取作战室列表
export const getRoomsList = (data: any) => {
  return http.post(baseUrlApi("warroom/rooms_list"), { data });
};

// 获取用户已加入的作战室列表
export const getUserRooms = (data: any = {}) => {
  return http.post(baseUrlApi("warroom/user_rooms"), { data });
};

// 获取关注的作战室列表
export const getStarRooms = (data: any = {}) => {
  return http.post(baseUrlApi("warroom/get_star_rooms"), { data });
};

// 获取指定作战室的成员列表
export const getMembers = (data: any) => {
  return http.post(baseUrlApi("warroom/members"), { data });
};

// 获取或创建私聊房间
export const getPrivateRoom = (data: any) => {
  return http.post(baseUrlApi("warroom/get_private_room"), { data });
};

// 统一消息查询接口
export const queryMessages = (data: {
  query_type:
    | "public_history"
    | "private_history"
    | "search_messages"
    | "announcements";
  room_id?: string;
  partner_id?: string;
  keyword?: string;
  limit?: number;
  offset?: number;
}) => {
  return http.post(baseUrlApi("warroom/query_messages"), { data });
};

//运行剧本或动作
export const ActionScriptRun = (data: any) => {
  return http.post(baseUrlApi("warroom/run"), { data });
};

// 关注指定作战室
export const StarRooms = (data: any) => {
  return http.post(baseUrlApi("warroom/star_room"), { data });
};

// 邀请用户加入作战室
export const inviteUser = (data: any) => {
  return http.post(baseUrlApi("warroom/invite"), { data });
};
