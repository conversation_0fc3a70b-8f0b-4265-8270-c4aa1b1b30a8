import { RectResize } from "@logicflow/extension";
import {
  getShapeStyleFuction,
  getTextStyleFunction
} from "@/views/playbook/playbookEdit/node/getShapeStyleUtil";

// 矩形
class RectNewModel extends RectResize.model {
  // 设置矩形的形状属性：大小和圆角
  setAttributes() {
    this.width = 200;
    this.height = 100;
    this.radius = 15;
  }

  setToBottom() {
    this.zIndex = 0;
  }

  getNodeStyle() {
    const style = super.getNodeStyle();
    const properties = this.getProperties();
    return getShapeStyleFuction(style, properties);
  }

  getTextStyle() {
    const style = super.getTextStyle();
    const properties = this.getProperties();
    return getTextStyleFunction(style, properties);
  }
}

export default {
  type: "pro-rect",
  view: RectResize.view,
  model: RectNewModel
};
