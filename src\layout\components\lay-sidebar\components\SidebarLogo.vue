<script lang="ts" setup>
import { getTopMenu } from "@/router/utils";
import { useNav } from "@/layout/hooks/useNav";
import { onMounted, onUnmounted, ref } from "vue";
import { getPicture } from "@/api/system";

defineProps({
  collapse: Boolean
});

const { title } = useNav();

const logo = ref<string>("");

onMounted(async () => {
  logo.value = await getPicture("logo");
});

onUnmounted(() => {
  if (logo.value) URL.revokeObjectURL(logo.value);
});
</script>

<template>
  <div :class="{ collapses: collapse }" class="sidebar-logo-container">
    <transition name="sidebarLogoFade">
      <router-link
        v-if="collapse"
        key="collapse"
        :title="title"
        :to="getTopMenu()?.path ?? '/'"
        class="sidebar-logo-link"
      >
        <img v-if="logo" :src="logo" alt="加载失败" />
        <img v-else alt="加载失败" src="/logo.svg" />
        <span class="sidebar-title">{{ title }}</span>
      </router-link>
      <router-link
        v-else
        key="expand"
        :title="title"
        :to="getTopMenu()?.path ?? '/'"
        class="sidebar-logo-link"
      >
        <img v-if="logo" :src="logo" alt="logo" />
        <span class="sidebar-title">{{ title }}</span>
      </router-link>
    </transition>
  </div>
</template>

<style lang="scss" scoped>
.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 48px;
  overflow: hidden;

  .sidebar-logo-link {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    height: 100%;
    padding-left: 10px;

    img {
      display: inline-block;
      height: 32px;
    }

    .sidebar-title {
      display: inline-block;
      height: 32px;
      margin: 2px 0 0 12px;
      overflow: hidden;
      font-size: 18px;
      font-weight: 600;
      line-height: 32px;
      color: var(--pure-theme-sub-menu-active-text);
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
