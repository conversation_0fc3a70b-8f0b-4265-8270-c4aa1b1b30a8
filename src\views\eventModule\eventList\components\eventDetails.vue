<template>
  <el-dialog
    v-model="visible"
    title="事件详情"
    width="1200px"
    top="5vh"
    @open="onOpen"
  >
    <el-skeleton v-if="loading" :rows="6" animated />
    <el-scrollbar
      v-else
      always
      style="height: 70vh; min-height: 350px; max-height: 80vh"
    >
      <el-descriptions :column="1" label-width="240px" border>
        <el-descriptions-item label="事件ID" label-align="center">
          {{ detail.id }}
        </el-descriptions-item>
        <el-descriptions-item label="事件名称" label-align="center">
          {{ detail.event_name }}
        </el-descriptions-item>
        <el-descriptions-item label="处理状态" label-align="center">
          <el-tag
            :type="
              detail.event_process_status === '待处理'
                ? 'danger'
                : detail.event_process_status === '处理中'
                  ? 'primary'
                  : 'success'
            "
            effect="plain"
          >
            {{ detail.event_process_status }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="事件来源" label-align="center">
          {{ detail.event_source }}
        </el-descriptions-item>
        <el-descriptions-item label="责任人" label-align="center">
          <template
            v-if="Array.isArray(detail.owner?.user) && detail.owner.user.length"
          >
            <span v-for="(userObj, idx) in detail.owner.user" :key="idx">
              {{ (Object.values(userObj)[0] as any).display_name }}
              <span v-if="idx < detail.owner.user.length - 1">, </span>
            </span>
          </template>
          <template v-else> -</template>
        </el-descriptions-item>
        <el-descriptions-item label="责任角色" label-align="center">
          <template
            v-if="Array.isArray(detail.owner?.role) && detail.owner.role.length"
          >
            <span v-for="(roleObj, idx) in detail.owner.role" :key="idx">
              {{ Object.values(roleObj)[0] }}
              <span v-if="idx < detail.owner.role.length - 1">, </span>
            </span>
          </template>
          <template v-else> -</template>
        </el-descriptions-item>
        <el-descriptions-item label="创建人" label-align="center">
          {{ detail.created_name }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间" label-align="center">
          {{ detail.ctime }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间" label-align="center">
          {{ detail.utime }}
        </el-descriptions-item>
        <el-descriptions-item label="关闭时间" label-align="center">
          {{ detail.close_time }}
        </el-descriptions-item>
        <el-descriptions-item label="事件概要" label-align="center">
          {{ detail.event_summary || "-" }}
        </el-descriptions-item>
        <el-descriptions-item :span="1" label="运行日志" label-align="center">
          <div class="runlogs-container">
            <div v-if="!runlogGroups || runlogGroups.length === 0">
              <el-empty class="small-empty" description="暂无运行日志" />
            </div>
            <div v-else class="runlogs-list">
              <Runlog
                v-for="group in runlogGroups"
                :key="group.run_id"
                :title="`执行记录 - ${group.playbook_name}`"
                :timestamp="group.timestamp"
                :items="group.items"
                :show-summary="true"
                :collapse-key="group.run_id"
              />
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="日志数据" label-align="center">
          <div class="logs-container">
            <div v-if="!logDataStr || logDataStr === '{}'">
              <el-empty class="small-empty" description="暂无日志数据" />
            </div>
            <div v-else class="logs-list">
              <div class="log-item">
                <div class="log-content">{{ logDataStr }}</div>
              </div>
            </div>
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </el-scrollbar>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineExpose, defineProps, ref } from "vue";
import { EventDetails } from "@/api/event";
import { Runlog } from "@/components/Rerunlog";

const props = defineProps<{ id?: string | number }>();
const visible = ref(false);
const eventId = ref<string | number | undefined>(props.id);
const detail = ref<any>({});
const loading = ref(false);
const logDataStr = ref("");
const runlogGroups = ref<any[]>([]);

const fetchDetail = async (id: string | number) => {
  loading.value = true;
  try {
    const res = await EventDetails({ id });
    const result = res as any;
    if (result.code === 0) {
      detail.value = result.data;
      // 处理log_data
      try {
        if (!result.data.log_data || result.data.log_data === "-") {
          logDataStr.value = "";
        } else {
          logDataStr.value = JSON.stringify(
            JSON.parse(result.data.log_data),
            null,
            2
          );
        }
      } catch {
        logDataStr.value = "";
      }
      // 处理run_logs
      if (!result.data.run_logs || result.data.run_logs === "-") {
        runlogGroups.value = [];
      } else {
        // 转换 run_logs 数据为组件所需格式
        const groups = [];
        for (const [runId, items] of Object.entries(
          result.data.run_logs as any
        )) {
          if (Array.isArray(items) && items.length > 0) {
            // 简单排序：开始节点在第一个，结束节点在最后一个
            const sortedItems = items.sort((a, b) => {
              if (a.node_type === "start-node") return -1;
              if (b.node_type === "start-node") return 1;
              if (a.node_type === "end-node") return 1;
              if (b.node_type === "end-node") return -1;
              // 其他节点按时间排序
              const timeA = new Date(a.start_time || a.ctime || 0).getTime();
              const timeB = new Date(b.start_time || b.ctime || 0).getTime();
              return timeA - timeB;
            });
            groups.push({
              run_id: runId,
              playbook_name: sortedItems[0]?.playbook_name || "未命名剧本",
              timestamp: sortedItems[0]?.start_time || sortedItems[0]?.ctime,
              items: sortedItems
            });
          }
        }
        // 按运行时间排序，最新的在前
        runlogGroups.value = groups.sort((a, b) => {
          const timeA = new Date(a.timestamp || 0).getTime();
          const timeB = new Date(b.timestamp || 0).getTime();
          return timeB - timeA; // 倒序：最新的运行记录在前
        });
      }
    }
  } finally {
    loading.value = false;
  }
};

const onOpen = () => {
  if (eventId.value) {
    fetchDetail(eventId.value);
  }
};

function open(id: string | number) {
  eventId.value = id;
  visible.value = true;
}

defineExpose({ open });
</script>

<style scoped>
.logs-container {
  width: 100%;
  margin-top: 10px;
}

/* 调整el-descriptions左侧label样式 */
:deep(.el-descriptions__label) {
  white-space: nowrap;
  font-weight: 500;
  color: #666;
  background: #f8f9fa;
  text-align: right;
  padding-right: 12px;
}

.logs-list {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  max-width: 100%;
}

.log-item {
  position: relative;
  display: flex;
  padding: 16px;
  transition: background-color 0.2s ease;
}

.log-item:hover {
  background-color: var(--el-fill-color-light);
}

.log-content {
  flex: 1;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
  padding: 12px;
  font-family: "Menlo", "Monaco", "Consolas", "Courier New", monospace;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
  color: var(--el-text-color-primary);
  margin: 0;
  border-left: 3px solid var(--el-color-primary-light-5);
}

.small-empty {
  --el-empty-padding: 10px 0 10px 0;
  --el-empty-image-width: 60px;
  --el-empty-description-margin-top: 4px;

  .el-empty__image {
    width: 60px !important;
    height: 60px !important;
    margin: 0 auto;
  }

  .el-empty__description {
    font-size: 13px;
    margin-top: 4px;
  }
}

.runlogs-container {
  width: 100%;
  margin-top: 10px;
}

.runlogs-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
</style>
