<template>
  <div class="demo-pagination-block bg-white p-3 flex justify-end">
    <el-pagination
      v-model:current-page="currentPage"
      :background="true"
      :page-size="pageSize"
      :total="total"
      layout="prev, pager, next, jumper"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script lang="ts" setup>
import type { Ref } from "vue";
import { onMounted, ref } from "vue";

// 类型定义（根据实际数据结构补充）
interface UseColumnsReturn {
  dataList: Ref<any[]>;
  total: Ref<number>;
  fetchUserData: (page: number, size: number, query: string) => void;
}

const { total, fetchUserData } = useColumns() as UseColumnsReturn;

// 分页参数
const DEFAULT_PAGE_SIZE = 15;
const currentPage = ref(1);
const pageSize = ref(DEFAULT_PAGE_SIZE);

// 加载初始数据
onMounted(() => {
  fetchUserData(currentPage.value, pageSize.value, "");
});

// 翻页处理
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  fetchUserData(page, pageSize.value, "");
};
</script>

<style scoped>
.demo-pagination-block + .demo-pagination-block {
  margin-top: 10px;
}

.demo-pagination-block .demonstration {
  margin-bottom: 16px;
}
</style>
