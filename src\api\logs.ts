import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

// 获取索引字段列表
export const apiGetLogs = (data: any) => {
  return http.post(baseUrlApi("logs/fields"), { data });
};

// 查询ES日志数据
export const apiGetLogsData = (data: any) => {
  return http.post(baseUrlApi("logs/query"), { data });
};

// 获取索引列表
export const apiGetIndexList = (data: any) => {
  return http.post(baseUrlApi("logs/indexes"), { data });
};
