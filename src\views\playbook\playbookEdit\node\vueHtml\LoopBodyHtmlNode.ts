import { HtmlNode } from "@logicflow/core";
import { dynamicGroup } from "@logicflow/extension";
import { createApp, h } from "vue";
import CycleNode from "../../icon/CycleNode.vue";

class LoopBodyNodeView extends HtmlNode {
  isMounted: boolean;
  r: any;
  app: any;

  constructor(props) {
    super(props);
    this.isMounted = false;
    this.r = h(CycleNode, {
      id: props.model.id,
      properties: props.model.getProperties(),
      text: props.model.inputData,
      model: props.model,
      graphModel: props.graphModel
    });
    this.app = createApp({
      render: () => this.r
    });
  }

  shouldUpdate() {
    if (this.preProperties && this.preProperties === this.currentProperties)
      return;
    this.preProperties = this.currentProperties;
    return true;
  }

  setHtml(rootEl) {
    if (!this.isMounted) {
      this.isMounted = true;
      const node = document.createElement("div");
      rootEl.appendChild(node);
      this.app.mount(node);
    } else {
      this.r.component.props.properties = this.props.model.getProperties();
    }
  }
}

class LoopBodyNodeModel extends dynamicGroup.model {
  setAttributes() {
    super.setAttributes();
    // 禁止循环节点收起
    this.isCollapsed = false; // 设置为非收起状态
    this.collapsible = false; // 禁止收起功能
    // 确保节点有合理的最小尺寸
    this.ensureMinimumSize();
    // 如果有子节点，使用强制初始化检查
    if (this.children && this.children.size > 0) {
      setTimeout(() => {
        this.forceInitializeCheck();
      }, 10);
    }
  }

  // 初始化方法，确保节点在创建时有正确的大小
  initNodeData(data) {
    super.initNodeData(data);
    // 如果没有设置大小，则设置默认大小
    if (!data.width || data.width < 300) {
      this.width = 300;
    }
    if (!data.height || data.height < 300) {
      this.height = 300;
    }
    this.ensureMinimumSize();
  }

  // 节点添加到画布时的回调
  onAddToGraph() {
    super.onAddToGraph();
    // 立即计算大小和约束节点
    this.initializeImmediately();
    // 注册事件监听器
    this.registerEventListeners();
    // 延迟再次检查，确保所有节点都已加载
    setTimeout(() => {
      this.initializeConstraints();
    }, 100);
  }

  // 立即初始化
  initializeImmediately() {
    // 确保节点有合理的最小尺寸
    this.ensureMinimumSize();
    // 如果有子节点，先计算大小，再约束
    if (this.children && this.children.size > 0) {
      // 先计算循环体大小
      this.calculateSizeFromContent();
      // 延迟约束，确保大小计算完成
      setTimeout(() => {
        this.constrainAllChildren();
        // 再次计算大小，确保约束后的节点都在范围内
        this.calculateSizeFromContent();
      }, 50);
    }
  }

  // 约束所有子节点
  constrainAllChildren() {
    if (!this.children || this.children.size === 0) return;
    this.children.forEach(nodeId => {
      this.constrainNodeToContentArea(nodeId);
    });
  }

  // 注册事件监听器
  registerEventListeners() {
    if (!this.graphModel) return;
    // 监听节点拖拽事件
    this.graphModel.eventCenter.on("node:drag", this.handleNodeDrag.bind(this));
    this.graphModel.eventCenter.on(
      "node:dragend",
      this.handleNodeDragEnd.bind(this)
    );
    this.graphModel.eventCenter.on("node:add", this.handleNodeAdd.bind(this));
  }

  // 处理节点拖拽事件
  handleNodeDrag(data) {
    const { nodeId } = data;
    if (this.children && this.children.has(nodeId)) {
      // 检查节点是否仍在内容区域内
      const node = this.graphModel.getNodeModelById(nodeId);
      if (node && !this.isNodeInContentArea(node)) {
        // 如果节点不在内容区域内，强制移回
        console.log(`拖拽过程中节点 ${nodeId} 移出循环体，强制移回`);
        this.constrainNodeToContentArea(nodeId);
      } else {
        // 正常约束节点
        this.constrainNodeToContentArea(nodeId);
      }
    }
  }

  // 处理节点拖拽结束事件
  handleNodeDragEnd(data) {
    const { nodeId } = data;
    if (this.children && this.children.has(nodeId)) {
      // 确保节点仍在循环体内
      const node = this.graphModel.getNodeModelById(nodeId);
      if (node && !this.isNodeInContentArea(node)) {
        // 如果节点不在内容区域内，强制移回
        console.log(`节点 ${nodeId} 不在循环体内容区域内，强制移回`);
        this.constrainNodeToContentArea(nodeId);
      }
      this.calculateSizeFromContent();
    }
  }

  // 处理节点添加事件
  handleNodeAdd(data) {
    const { nodeId } = data;
    setTimeout(() => {
      if (this.children && this.children.has(nodeId)) {
        const node = this.graphModel.getNodeModelById(nodeId);
        if (node && !this.isNodeInContentArea(node)) {
          // 如果新添加的节点不在内容区域内，强制移回
          console.log(`新添加的节点 ${nodeId} 不在循环体内容区域内，强制移回`);
          this.constrainNodeToContentArea(nodeId);
        }
        this.calculateSizeFromContent();
      }
    }, 100);
  }

  // 强制初始化检查
  forceInitializeCheck() {
    if (!this.children || this.children.size === 0) return;
    // 强制计算大小
    this.calculateSizeFromContent();
    // 强制约束所有节点
    this.children.forEach(nodeId => {
      const node = this.graphModel.getNodeModelById(nodeId);
      if (node) {
        // 强制移动节点到正确位置
        this.constrainNodeToContentArea(nodeId);
      }
    });
    // 再次计算大小
    this.calculateSizeFromContent();
  }

  // 初始化约束
  initializeConstraints() {
    if (this.children && this.children.size > 0) {
      // 使用强制初始化检查
      this.forceInitializeCheck();
    }
  }

  // 检查节点是否在内容区域内
  isNodeInContentArea(node) {
    if (!node) return false;

    const { x, y, width, height } = this;
    const { width: nodeWidth, height: nodeHeight } = node;
    // 计算内容区域边界（排除顶部栏）
    const contentTop = y - height / 2 + 32; // 顶部栏高度32px
    const contentBottom = y + height / 2;
    const contentLeft = x - width / 2;
    const contentRight = x + width / 2;
    // 计算节点边界
    const nodeTop = node.y - nodeHeight / 2;
    const nodeBottom = node.y + nodeHeight / 2;
    const nodeLeft = node.x - nodeWidth / 2;
    const nodeRight = node.x + nodeWidth / 2;

    // 检查节点是否完全在内容区域内
    return (
      nodeLeft >= contentLeft &&
      nodeRight <= contentRight &&
      nodeTop >= contentTop &&
      nodeBottom <= contentBottom
    );
  }

  // 约束节点到内容区域
  constrainNodeToContentArea(nodeId) {
    const node = this.graphModel.getNodeModelById(nodeId);
    if (!node) return;

    const { x, y, width, height } = this;
    const { width: nodeWidth, height: nodeHeight } = node;
    // 计算内容区域边界（排除顶部栏）
    const contentTop = y - height / 2 + 32; // 顶部栏高度32px
    const contentBottom = y + height / 2;
    const contentLeft = x - width / 2;
    const contentRight = x + width / 2;
    // 计算节点边界
    const nodeTop = node.y - nodeHeight / 2;
    const nodeBottom = node.y + nodeHeight / 2;
    const nodeLeft = node.x - nodeWidth / 2;
    const nodeRight = node.x + nodeWidth / 2;
    let newX = node.x;
    let newY = node.y;
    let needsUpdate = false;
    // 水平约束
    if (nodeLeft < contentLeft) {
      newX = contentLeft + nodeWidth / 2;
      needsUpdate = true;
    } else if (nodeRight > contentRight) {
      newX = contentRight - nodeWidth / 2;
      needsUpdate = true;
    }
    // 垂直约束（确保不进入顶部栏）
    if (nodeTop < contentTop) {
      newY = contentTop + nodeHeight / 2;
      needsUpdate = true;
    } else if (nodeBottom > contentBottom) {
      newY = contentBottom - nodeHeight / 2;
      needsUpdate = true;
    }
    // 如果位置需要调整，则移动节点
    if (needsUpdate) {
      node.moveTo(newX, newY);
      // 强制更新节点位置
      node.updatePosition();
    }
  }

  // 确保节点有合理的最小尺寸
  ensureMinimumSize() {
    // 设置最小宽度和高度
    const minWidth = 300;
    const minHeight = 300;
    if (this.width < minWidth) {
      this.width = minWidth;
    }
    if (this.height < minHeight) {
      this.height = minHeight;
    }
  }

  // 根据内容计算节点大小
  calculateSizeFromContent() {
    if (!this.children || this.children.size === 0) return;
    let minX = Infinity;
    let maxX = -Infinity;
    let minY = Infinity;
    let maxY = -Infinity;
    // 计算所有子节点的边界
    this.children.forEach(nodeId => {
      const node = this.graphModel.getNodeModelById(nodeId);
      if (node) {
        const nodeLeft = node.x - node.width / 2;
        const nodeRight = node.x + node.width / 2;
        const nodeTop = node.y - node.height / 2;
        const nodeBottom = node.y + node.height / 2;
        minX = Math.min(minX, nodeLeft);
        maxX = Math.max(maxX, nodeRight);
        minY = Math.min(minY, nodeTop);
        maxY = Math.max(maxY, nodeBottom);
      }
    });

    // 如果找到了有效的边界
    if (
      minX !== Infinity &&
      maxX !== -Infinity &&
      minY !== Infinity &&
      maxY !== -Infinity
    ) {
      // 计算需要的尺寸（加上内边距）
      const padding = 60; // 内边距
      const headerHeight = 32; // 顶部栏高度
      const contentWidth = maxX - minX;
      const contentHeight = maxY - minY;
      // 计算新的尺寸，确保循环体能容纳所有内容
      const newWidth = Math.max(contentWidth + padding * 2, 300);
      const newHeight = Math.max(
        contentHeight + padding * 2 + headerHeight,
        300
      );
      // 只更新节点大小，不改变中心位置
      this.width = newWidth;
      this.height = newHeight;
      // 立即约束所有子节点到新的边界内
      setTimeout(() => {
        this.constrainAllChildren();
      }, 10);
    }
  }

  // 重写获取属性方法，确保大小信息被正确保存
  getProperties() {
    const properties = super.getProperties();
    // 确保大小信息被包含在属性中
    properties.width = this.width;
    properties.height = this.height;
    properties.minWidth = 300;
    properties.minHeight = 300;
    return properties;
  }

  // 重写设置属性方法，确保大小信息被正确恢复
  setProperties(properties) {
    super.setProperties(properties);
    // 恢复大小信息
    if (properties.width) {
      this.width = Math.max(properties.width, 300);
    }
    if (properties.height) {
      this.height = Math.max(properties.height, 300);
    }
    // 确保最小尺寸
    this.ensureMinimumSize();
  }

  // 确保动态分组能够正确调整大小
  updateSize() {
    super.updateSize();
    // 确保最小高度包含顶部栏
    if (this.height < 300) {
      this.height = 300;
    }
    // 确保最小宽度
    if (this.width < 300) {
      this.width = 300;
    }
  }

  // 重写获取默认锚点方法，确保连接点位置正确
  getDefaultAnchor() {
    const { width, height, x, y } = this;
    return [
      {
        x: x,
        y: y - height / 2,
        id: `${this.id}_top`,
        type: "input"
      },
      {
        x: x,
        y: y + height / 2,
        id: `${this.id}_bottom`,
        type: "output"
      },
      {
        x: x - width / 2,
        y: y,
        id: `${this.id}_left`,
        type: "input"
      },
      {
        x: x + width / 2,
        y: y,
        id: `${this.id}_right`,
        type: "output"
      }
    ];
  }

  // 自定义节点样式
  getNodeStyle() {
    const style = super.getNodeStyle();
    style.fill = "#f5f5f5"; // 浅灰色背景
    style.stroke = "#e0e0e0"; // 浅灰色边框
    style.strokeWidth = 1; // 细边框
    style.rx = 8; // 圆角
    style.ry = 8;
    return style;
  }

  // 自定义选中状态边框
  getSelectedOutlineStyle() {
    const style = super.getSelectedOutlineStyle();
    style.stroke = "#9c27b0"; // 紫色选中边框
    style.strokeWidth = 2;
    style.strokeDasharray = "none"; // 实线边框
    return style;
  }

  // 自定义悬停状态边框
  getHoverOutlineStyle() {
    const style = super.getHoverOutlineStyle();
    style.stroke = "#9c27b0"; // 紫色悬停边框
    style.strokeWidth = 1.5;
    style.strokeDasharray = "none";
    return style;
  }

  // 自定义拖入高亮样式
  getAddableOutlineStyle() {
    const style = super.getAddableOutlineStyle();
    style.stroke = "#9c27b0"; // 紫色高亮
    style.strokeDasharray = "4 4";
    style.strokeWidth = 2;
    return style;
  }

  // 自定义连接点样式
  getAnchorStyle() {
    const style = super.getAnchorStyle();
    style.fill = "#9c27b0"; // 紫色连接点
    style.stroke = "#ffffff";
    style.strokeWidth = 2;
    style.r = 4;
    return style;
  }

  // 重写添加节点到分组的方法
  addNodeToGroup(nodeId) {
    const result = super.addNodeToGroup(nodeId);
    if (result) {
      // 立即约束和调整大小
      this.constrainNodeToContentArea(nodeId);
      this.calculateSizeFromContent();
      // 延迟再次检查，确保完全生效
      setTimeout(() => {
        this.constrainNodeToContentArea(nodeId);
        this.calculateSizeFromContent();
      }, 50);
    }
    return result;
  }

  // 重写移除节点从分组的方法
  removeNodeFromGroup(nodeId) {
    const result = super.removeNodeFromGroup(nodeId);
    if (result) {
      // 延迟计算大小，确保节点已经完全移除
      setTimeout(() => {
        this.calculateSizeFromContent();
      }, 100);
    }
    return result;
  }

  // 重写节点移动事件，实时调整大小
  onNodeMove(nodeId, deltaX, deltaY) {
    super.onNodeMove(nodeId, deltaX, deltaY);
    // 如果是当前分组的节点，检查位置并重新计算大小
    if (this.children && this.children.has(nodeId)) {
      const node = this.graphModel.getNodeModelById(nodeId);
      if (node && !this.isNodeInContentArea(node)) {
        // 如果节点不在内容区域内，强制移回
        console.log(`节点移动过程中 ${nodeId} 移出循环体，强制移回`);
        this.constrainNodeToContentArea(nodeId);
      }
      this.calculateSizeFromContent();
    }
  }
}

export default {
  type: "loop-body",
  view: LoopBodyNodeView,
  model: LoopBodyNodeModel
};
