<template>
  <div>
    <el-table :data="playbookVersionsTableData" border>
      <el-table-column align="center" label="版本" prop="version" width="auto">
        <template #default="{ row }">
          <el-button
            link
            type="primary"
            @click="
              toDetail(
                {
                  flow_id: row.flow_id,
                  version_id: row.version_id,
                  status: row.status
                },
                'params'
              )
            "
            >{{ row.version }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="状态" prop="status" width="100px">
        <template #default="scope">
          <el-tag :type="scope.row.status == '1' ? 'success' : 'primary'"
            >{{ scope.row.status == "1" ? "已发布" : "草稿" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        :formatter="updatorFormatter"
        align="center"
        label="更新人"
        prop="updator"
        width="150px"
      />
      <el-table-column
        align="center"
        label="更新时间"
        prop="utime"
        width="auto"
      />
      <el-table-column
        :formatter="creatorFormatter"
        align="center"
        label="创建人"
        prop="creator"
        width="150px"
      />
      <el-table-column
        align="center"
        label="创建时间"
        prop="ctime"
        width="auto"
      />
      <el-table-column align="center" label="操作" width="auto">
        <template #default="{ row }">
          <div>
            <el-tooltip :hide-after="0" content="编辑" placement="top">
              <el-button
                link
                type="primary"
                @click="
                  toDetail(
                    {
                      flow_id: row.flow_id,
                      version_id: row.version_id,
                      status: row.status
                    },
                    'params'
                  )
                "
              >
                <Icon
                  height="20"
                  icon="material-symbols:edit-square-outline"
                  width="20"
                />
              </el-button>
            </el-tooltip>

            <el-tooltip :hide-after="0" content="预览" placement="top">
              <el-button link type="primary" @click="viewLogicflowData(row)">
                <Icon height="20" icon="ix:eye" width="20" />
              </el-button>
            </el-tooltip>

            <el-tooltip :hide-after="0" content="删除" placement="top">
              <span class="ml-3">
                <el-popconfirm
                  cancel-button-text="取消"
                  confirm-button-text="确认"
                  title="确认要删除吗？"
                  @confirm="deletePlaybookVersion(row)"
                >
                  <template #reference>
                    <el-button link type="danger">
                      <Icon height="20" icon="uiw:delete" width="20" />
                    </el-button>
                  </template>
                </el-popconfirm>
              </span>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      v-model="isShowViewPlaybook"
      style="width: 1500px; height: 750px"
    >
      <template #header>
        <div>预览流程图</div>
      </template>
      <div class="view-div">
        <div ref="viewLogicflowRef" class="view-logicflow" />
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { useDetail } from "@/views/playbook/hooks";
import {
  apiDeletePlaybookVersion,
  apiGetPlaybookVersionsList
} from "@/api/playbook";
import LogicFlow from "@logicflow/core";
import { registerCustomElement } from "../playbookEdit/node";
import { Icon } from "@iconify/vue";

const lf = ref<LogicFlow>();
const playbookVersionsTableData = ref();
const { toDetail } = useDetail();
const isShowViewPlaybook = ref(false);
const viewLogicflowRef = ref();
const playbookDetail = ref();

//初始数据
let data = {
  nodes: [
    {
      type: "start-node",
      x: 100,
      y: 100
    },
    {
      type: "end-node",
      x: 1000,
      y: 100
    }
  ]
};

//请求剧本版本列表
const getPlaybookVersionList = async id => {
  let res: any = await apiGetPlaybookVersionsList({ playbook_id: id });
  playbookVersionsTableData.value = res.data;
  console.log(playbookVersionsTableData.value);
};

//对表格数据updator列进行处理
const updatorFormatter = row => {
  return `${row.updator.display_name}(${row.updator.username})`;
};

//对表格creator列进行处理
const creatorFormatter = row => {
  return `${row.creator.display_name}(${row.creator.username})`;
};

//显示查看dialog
const viewLogicflowData = row => {
  isShowViewPlaybook.value = true;
  initLogicFlow(row);
};

//渲染流程图数据
const initLogicFlow = async row => {
  console.log(row.flow_id);
  //获取剧本的详细信息
  let res: any = await apiGetPlaybookVersionsList({
    playbook_id: row.flow_id
  });
  const result = res.data.find(item => item.version_id === row.version_id);
  playbookDetail.value = result;
  console.log(playbookDetail.value);
  lf.value = new LogicFlow({
    container: viewLogicflowRef.value,
    width: 1450,
    height: 650,
    grid: {
      visible: false,
      size: 15
    },
    isSilentMode: true
  });
  registerCustomElement(lf.value); //注册自定义节点和边
  lf.value.setDefaultEdgeType("vue-edge"); //边的类型
  //如果是已有剧本，则渲染已有剧本的流程图信息，否则默认为初始数据
  if (Object.entries(playbookDetail.value.flow_json).length > 0) {
    playbookDetail.value.flow_json.nodes.forEach(node => {
      // //基于锚点的位置更新边的路径
      // lf.value.getNodeModelById(node.id).updateField();
      //在渲染流程图之前，先把初始化isWebSocket和scale的值
      if (node.properties && node.properties.isWebSocket !== undefined) {
        node.properties.isWebSocket = false;
        node.properties.scale = 1;
      }
    });
    lf.value.render(playbookDetail.value.flow_json);
  } else {
    lf.value.render(data);
  }
  lf.value.translateCenter(); // 将图形移动到画布中央
};

//删除剧本版本
const deletePlaybookVersion = async row => {
  console.log(row.id);
  let res = await apiDeletePlaybookVersion({ flow_id: row.id });
  console.log(res);
  //删除剧本版本后，重新请求剧本版本列表
  await apiGetPlaybookVersionsList({ playbook_id: row.id });
};

//对外暴露方法
defineExpose({
  getPlaybookVersionList
});
</script>

<style lang="scss" scoped></style>
