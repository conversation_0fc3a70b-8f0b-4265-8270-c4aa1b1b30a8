<template>
  <el-dialog
    v-model="dialogVisible"
    :close-on-click-modal="false"
    :title="isEdit ? '场景编辑' : '场景新建'"
    width="500px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      class="scene-form"
      label-width="80px"
    >
      <el-form-item label="场景" prop="name">
        <el-input
          v-model="formData.name"
          :disabled="isEdit"

        />
      </el-form-item>
      <el-form-item label="场景描述" prop="remark">
        <el-input v-model="formData.remark"  />
      </el-form-item>
      <el-form-item v-if="!isEdit" label="场景状态" prop="status">
        <el-switch
          v-model="formData.status"
          :active-value="1"
          :inactive-value="0"
          :active-text="'启用'"
          :inactive-text="'禁用'"
          inline-prompt
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">提 交</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import type { PropType } from "vue";
import { defineEmits, defineProps, ref, watch } from "vue";
import { type FormInstance, type FormRules } from "element-plus";
import { addScene, editScene } from "@/api/scene";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  editData: {
    type: Object as PropType<{
      id?: string;
      name: string;
      remark: string;
      status: number;
    }>,
    default: () => ({
      name: "",
      remark: "",
      status: 0
    })
  }
});

const emit = defineEmits(["update:visible", "success"]);

const dialogVisible = ref(props.visible);
const formRef = ref<FormInstance>();

// 表单数据
const formData = ref<{
  id?: string;
  name: string;
  remark: string;
  status: number;
}>({
  name: "",
  remark: "",
  status: 0
});

// 表单校验规则
const rules = ref<FormRules>({
  name: [
    { required: true, message: "请输入场景名称", trigger: "blur" },
    { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" }
  ],
  remark: [
    { required: true, message: "请输入场景描述", trigger: "blur" },
    { min: 1, max: 200, message: "长度在 1 到 200 个字符", trigger: "blur" }
  ]
});

// 监听visible变化
watch(
  () => props.visible,
  val => {
    dialogVisible.value = val;
    if (val) {
      if (props.isEdit) {
        // 编辑模式，填充表单数据
        const { id, name = "", remark = "", status = 0 } = props.editData;
        formData.value = { id, name, remark, status };
      } else {
        // 新建模式，重置表单
        formData.value = {
          name: "",
          remark: "",
          status: 0
        };
      }
    }
  }
);

// 关闭弹窗
const handleClose = () => {
  emit("update:visible", false);
  formRef.value?.resetFields();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  // 打印当前操作类型
  if (props.isEdit) {
    await formRef.value.validate(async (valid, fields) => {
      if (valid) {
        const submitData = { ...formData.value };
        delete submitData.name;
        const editRes = (await editScene(submitData)) as any;
        if (editRes.code === 0) {
          emit("success", submitData);
          handleClose();
        }
      }
    });
  } else {
    await formRef.value.validate(async (valid, fields) => {
      if (valid) {
        // 只传递接口要求的参数
        const submitData = {
          name: formData.value.name,
          remark: formData.value.remark,
          status: Number(formData.value.status)
        };
        const addRes = (await addScene(submitData)) as any;
        if (addRes.code === 0) {
          emit("success", submitData);
          handleClose();
        }
      }
    });
  }
};
</script>

<style scoped>
.scene-form {
  padding: 20px 20px 0;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-segmented) {
  width: 100%;
  min-width: 240px;
}

:deep(.el-segmented-item) {
  flex: 1;
  min-width: 0;
}

:deep(.el-segmented-item.is-active) {
  background-color: var(--el-color-primary);
  color: white;
}
</style>
