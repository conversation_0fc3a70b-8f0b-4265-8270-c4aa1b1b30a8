import { http } from "@/utils/http";
import { baseUrlApi } from "./utils"; // 获取报表信息
// 获取报表信息
export const getDashboardData = (data: any) => {
  return http.post(baseUrlApi("dashboard/view"), { data });
};

export const getMttrData = (data: any) => {
  return http.post(baseUrlApi("dashboard/mttr"), { data });
};

export const getPlaybooksNum = (data: any) => {
  return http.post(baseUrlApi("dashboard/playbooks"), { data });
};

export const getAppsNum = (data: any) => {
  return http.post(baseUrlApi("dashboard/apps"), { data });
};

export const getPlaybookSuccessRate = (data: any) => {
  return http.post(baseUrlApi("dashboard/success-rate"), { data });
};
