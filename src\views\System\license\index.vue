<template>
  <div class="event-module-container bg-white dark:bg-[#141414]">
    <el-card class="mt-4">
      <div class="license-container">
        <!-- 基本信息 -->
        <div class="section">
          <div class="section-title">
            <el-icon>
              <InfoFilled />
            </el-icon>
            软件信息
          </div>
          <div class="info-content">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="授权用户">
                <span>{{ licenseInfo.name }}</span>
                <input
                  ref="fileInputRef"
                  accept=".lic"
                  style="display: none"
                  type="file"
                  @change="handleFileChange"
                />
              </el-descriptions-item>
              <el-descriptions-item label="授权状态">
                <el-tag :type="licenseInfo.lic_status ? 'success' : 'danger'">
                  {{ licenseInfo.lic_status ? "已激活" : "未激活" }}
                </el-tag>
                <Perms :value="['lic:c']">
                  <el-button
                    class="ml-2"
                    link
                    size="small"
                    type="primary"
                    @click="handleImportLicense"
                  >
                    手动更新授权
                  </el-button>
                </Perms>
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">
                {{ licenseInfo.createTime }}
              </el-descriptions-item>
              <el-descriptions-item label="过期时间">
                {{ licenseInfo.expireTime }}
              </el-descriptions-item>
              <el-descriptions-item label="版本">
                {{ licenseInfo.version }}
              </el-descriptions-item>
              <el-descriptions-item label="序列号">
                {{ licenseInfo.serial_number }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>

        <div class="section">
          <div class="section-title">
            <el-icon>
              <Connection />
            </el-icon>
            各模块授权
          </div>
          <div class="modules-content">
            <el-row :gutter="20">
              <el-col
                v-for="(item, key) in licenseInfo.menuInfo"
                :key="key"
                :span="8"
                class="mb-4"
              >
                <div class="module-card">
                  <div class="module-header">
                    <el-icon class="module-icon">
                      <Share />
                    </el-icon>
                    <div class="module-title">
                      <div class="name">{{ key }}</div>
                      <div class="desc">
                        {{ item.description || "暂无描述" }}
                      </div>
                    </div>
                  </div>
                  <div class="module-info">
                    <div class="info-item">
                      <span class="label">过期时间：</span>
                      <span>{{ item.expireTime }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">当前状态：</span>
                      <span>{{ item.auth ? "已激活" : "未激活" }}</span>
                    </div>
                    <div
                      v-if="
                        item.value !== null &&
                        item.value !== undefined &&
                        item.value !== ''
                      "
                      class="info-item"
                    >
                      <span class="label">数量：</span>
                      <span>{{ item.value }}</span>
                    </div>
                  </div>
                  <div class="module-status">
                    <el-tag :type="item.auth ? 'success' : 'danger'">
                      {{ item.auth ? "已授权" : "未授权" }}
                    </el-tag>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { getLicenseData, importLicense } from "@/api/license";
import { Connection, InfoFilled, Share } from "@element-plus/icons-vue";
import { onMounted, ref } from "vue";
import { ElMessage } from "element-plus";

const licenseInfo = ref<any>({});
const fileInputRef = ref();

// 获取授权信息
const getLicenseInfo = async () => {
  const res = (await getLicenseData({})) as any;
  licenseInfo.value = res.data;
  console.log(licenseInfo.value);
};

// 处理导入授权文件按钮点击
const handleImportLicense = () => {
  fileInputRef.value.click();
};

// 处理文件选择变化
const handleFileChange = async (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (!file) {
    ElMessage.warning("请选择文件");
    return;
  }
  if (!file.name.toLowerCase().endsWith(".lic")) {
    ElMessage.warning("请上传.lic格式的许可证文件");
    target.value = "";
    return;
  }
  const formData = new FormData();
  formData.append("file", file);
  try {
    const res = (await importLicense(formData)) as any;
    if (res.code === 0) {
      ElMessage.success("许可证导入成功");
      await getLicenseInfo();
    }
  } catch (error) {
    ElMessage.error("许可证导入失败");
  }
  target.value = ""; // 清空文件输入框
};

onMounted(() => {
  getLicenseInfo();
});
</script>

<style lang="scss" scoped>
.event-module-container {
  // padding: 15px;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;

  .title-container {
    .main-title {
      display: flex;
      align-items: center;
      font-size: 24px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 4px;

      .icon {
        margin-right: 8px;
        font-size: 24px;
        color: var(--el-color-primary);
      }
    }

    .sub-title {
      font-size: 14px;
      color: var(--el-text-color-secondary);
    }
  }

  .action-container {
    .action-button {
      .el-icon {
        margin-right: 4px;
      }
    }
  }
}

.license-container {
  .section {
    margin-bottom: 32px;

    .section-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 8px;
      color: var(--el-text-color-primary);

      .el-icon {
        margin-right: 8px;
        color: var(--el-color-primary);
      }
    }

    .section-subtitle {
      font-size: 14px;
      color: var(--el-text-color-secondary);
      margin-bottom: 16px;
    }

    .info-content {
      :deep(.el-descriptions__label) {
        width: 120px;
        justify-content: flex-end;
        padding-right: 16px;
      }
    }

    .modules-content {
      .el-row {
        margin: 0 -10px;
      }

      .el-col {
        padding: 0 10px;
        margin-bottom: 20px;
      }

      .module-card {
        height: 100%;
        background: var(--el-bg-color);
        border-radius: 8px;
        padding: 20px;
        border: 1px solid var(--el-border-color-lighter);
        position: relative;

        .module-header {
          display: flex;
          align-items: flex-start;
          margin-bottom: 16px;

          .module-icon {
            font-size: 24px;
            color: var(--el-color-primary);
            margin-right: 12px;
            margin-top: 2px;
          }

          .module-title {
            flex: 1;

            .name {
              font-size: 16px;
              font-weight: 500;
              color: var(--el-text-color-primary);
              margin-bottom: 8px;
            }

            .desc {
              font-size: 12px;
              color: var(--el-text-color-secondary);
              line-height: 1.4;
            }
          }
        }

        .module-info {
          .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            font-size: 14px;

            &:last-child {
              margin-bottom: 0;
            }

            .label {
              color: var(--el-text-color-secondary);
              margin-right: 8px;
              min-width: 70px;
            }
          }
        }

        .module-status {
          position: absolute;
          top: 20px;
          right: 20px;
        }
      }
    }
  }
}
</style>
