<template>
  <el-dialog v-model="isShowWebSocketDialog" title="查看结果">
    <!-- 动作节点 -->
    <el-descriptions v-if="data.node_type == 'action-node'" :column="2" border>
      <el-descriptions-item
        label="开始时间"
        label-width="80px"
        label-align="center"
        >{{ data.start_time }}</el-descriptions-item
      >
      <el-descriptions-item
        label="结束时间"
        label-width="80px"
        label-align="center"
        >{{ data.end_time }}</el-descriptions-item
      >
      <el-descriptions-item
        label="应用名称"
        label-width="80px"
        label-align="center"
        >{{ data.tool_display_name }}</el-descriptions-item
      >
      <el-descriptions-item
        label="应用版本"
        label-width="80px"
        label-align="center"
        >{{ data.tool_version }}</el-descriptions-item
      >
      <el-descriptions-item
        label="动作名称"
        label-width="80px"
        label-align="center"
        >{{ data.action_display_name }}</el-descriptions-item
      >
      <el-descriptions-item
        label="状态"
        label-width="80px"
        label-align="center"
      >
        <div class="info-value">
          <span :class="getStatusClass(data.status)" class="status-tag"
            >{{ getStatusText(data.status) }}
          </span>
        </div>
      </el-descriptions-item>
      <el-descriptions-item
        :span="2"
        label="输入参数"
        label-width="80px"
        label-align="center"
        >{{ data.args || "无输入参数" }}</el-descriptions-item
      >
      <el-descriptions-item
        :span="2"
        label="结果"
        label-width="80px"
        label-align="center"
        >{{ data.result }}</el-descriptions-item
      >
    </el-descriptions>

    <!-- 判断节点 -->
    <el-descriptions v-if="data.node_type == 'judge-node'" :column="2" border>
      <el-descriptions-item
        label="开始时间"
        label-width="80px"
        label-align="center"
        >{{ data.start_time }}</el-descriptions-item
      >
      <el-descriptions-item
        label="结束时间"
        label-width="80px"
        label-align="center"
        >{{ data.end_time }}</el-descriptions-item
      >
      <el-descriptions-item
        label="状态"
        label-width="80px"
        label-align="center"
      >
        <div class="info-value">
          <span :class="getStatusClass(data.status)" class="status-tag"
            >{{ getStatusText(data.status) }}
          </span>
        </div></el-descriptions-item
      >
      <el-descriptions-item
        :span="2"
        label="判断参数"
        label-width="80px"
        label-align="center"
        >{{ data.args || "无输入参数" }}</el-descriptions-item
      >
    </el-descriptions>

    <!-- 人工节点 -->
    <el-descriptions
      v-if="data.node_type == 'approval-node'"
      :column="3"
      border
    >
      <el-descriptions-item
        label="开始时间"
        label-width="80px"
        label-align="center"
        >{{ data.start_time }}</el-descriptions-item
      >
      <el-descriptions-item
        label="结束时间"
        label-width="80px"
        label-align="center"
        >{{ data.end_time }}</el-descriptions-item
      >
      <el-descriptions-item
        label="状态"
        label-width="80px"
        label-align="center"
      >
        <div class="info-value">
          <span :class="getApprovalStatusClass(data.status)" class="status-tag"
            >{{ getApprovalStatusText(data.status) }}
          </span>
        </div></el-descriptions-item
      >
      <el-descriptions-item
        :span="2"
        label="自定义参数"
        label-width="80px"
        label-align="center"
        >{{ data.args || "无输入参数" }}</el-descriptions-item
      >
    </el-descriptions>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted } from "vue";

interface SSEResultType {
  start_time: string;
  end_time: string;
  tool_display_name: string;
  tool_version: string;
  action_display_name: string;
  status: number;
  args: string;
  result: string;
  node_type: string;
  [key: string]: any;
}

//使用v-model约定命名
const props = defineProps({
  modelValue: { type: Boolean, default: false }, // 固定为modelValue
  SSEResult: { type: Object, default: () => ({}) }
});

const emit = defineEmits(["update:modelValue"]);

const data = ref<SSEResultType>({} as SSEResultType);

watch(
  () => props.SSEResult,
  (newVal: SSEResultType) => {
    if (newVal) {
      data.value = newVal;
      console.log("data:", data.value);
    }
  },
  { immediate: true }
);

const isShowWebSocketDialog = computed({
  get: () => props.modelValue,
  set: val => emit("update:modelValue", val)
});

const getStatusClass = status => {
  return status === 0 ? "success" : "fail";
};

const getStatusText = status => {
  return status === 0 ? "成功" : "失败";
};

const getApprovalStatusClass = status => {
  return status === 0 ? "handle" : "success";
};

const getApprovalStatusText = status => {
  return status === 0 ? "处理中" : "已完成";
};
</script>

<style lang="scss" scoped>
.info-value {
  font-size: 16px;
  color: #333;
}

.status-tag {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  color: #fff;
}
.success {
  background: #52c41a;
}

.fail {
  background: #f5222d;
}

.handle {
  background: #e6a23c;
}
</style>
