<template>
  <svg>
    <g style="visibility: visible" transform="translate(0.5,0.5)">
      <ellipse
        cx="15.98"
        cy="14.96"
        fill="#ffffff"
        pointer-events="all"
        rx="13.600000000000001"
        ry="13.600000000000001"
        stroke="#000000"
        stroke-width="1.3"
      />
    </g>
  </svg>
</template>
<script setup></script>
<style scoped>
.svg-node {
  left: 1px;
  top: 1px;
  width: 32px;
  height: 30px;
  display: block;
  position: relative;
  overflow: hidden;
}
</style>
