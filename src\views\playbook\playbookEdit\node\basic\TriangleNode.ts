import { h } from "@logicflow/core";
import { RectResize } from "@logicflow/extension";
import {
  getShapeStyleFuction,
  getTextStyleFunction
} from "@/views/playbook/playbookEdit/node/getShapeStyleUtil";

// 三角形
class TriangleModel extends RectResize.model {
  // 设置 model 形状属性
  setAttributes() {
    const { width = 200, height = 100 } = this.properties;
    // 需要手动设置形状属性
    this.width = width;
    this.height = height;
  }

  getNodeStyle() {
    const style = super.getNodeStyle();
    const properties = this.getProperties();
    return getShapeStyleFuction(style, properties);
  }

  getTextStyle() {
    const style = super.getTextStyle();
    const properties = this.getProperties();
    return getTextStyleFunction(style, properties);
  }
}

class TriangleView extends RectResize.view {
  getResizeShape() {
    const { x, y, width, height } = this.props.model;
    const style = this.props.model.getNodeStyle();
    const attrs = {
      ...style,
      x,
      y,
      width,
      height,
      points: [
        [x - width / 2, y + height / 2],
        [x - width / 2, y - height / 2],
        [x + width / 2, y]
      ]
    };
    return h("g", {}, [h("polygon", { ...attrs })]);
  }
}

export default {
  type: "triangle",
  view: TriangleView,
  model: TriangleModel
};
