<template>
  <el-dialog
    v-model="visible"
    :close-on-click-modal="false"
    title="编辑角色"
    width="500px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-position="right"
      label-width="80px"
    >
      <el-form-item label="角色名称" prop="name">
        <el-input v-model="formData.name"  />
        <div v-if="nameError" class="error-tip">{{ nameError }}</div>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          :rows="3"

          type="textarea"
        />
        <div v-if="descError" class="error-tip">{{ descError }}</div>
      </el-form-item>
      <el-form-item label="权限" prop="permissions">
        <el-cascader
          v-model="permissionsSelected"
          :options="permissionOptions"
          :props="cascaderProps"
          clearable
          collapse-tags
          collapse-tags-tooltip
          placeholder="请选择权限"
          style="width: 100%"
          @change="handlePermissionChange"
        />
        <div v-if="permError" class="error-tip">{{ permError }}</div>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from "vue";
import type { FormInstance } from "element-plus";
import { ElMessage } from "element-plus";
import { searchRole, updateRole } from "@/api/role";
import { useRoleableStore } from "@/store/modules/roletable";

const roleStore = useRoleableStore();
const permissionOptions = ref([]);

// 转换API数据为级联选择器格式
const transformPermissions = permissions => {
  return permissions.map(item => {
    // 构建子选项 - 使用后端返回的权限数据
    const children = item.Permissions.map(perm => ({
      value: perm.value,
      label: perm.label,
      disabled: item.model === "dashboard" // 仪表盘权限不能取消
    }));

    // 返回转换后的格式
    return {
      value: item.model,
      label: item.description,
      children: children,
      disabled: item.model === "dashboard" // 仪表盘模块不能取消
    };
  });
};

// 在组件挂载时获取权限数据
onMounted(async () => {
  await roleStore.fetchPermissionsList();
  permissionOptions.value = transformPermissions(roleStore.permissionsList);
});

const emit = defineEmits(["success"]);
const visible = ref(false);
const formRef = ref<FormInstance>();
const roleId = ref<string>("");

// 错误信息
const nameError = ref("");
const descError = ref("");
const permError = ref("");

// 级联选择器配置
const cascaderProps = {
  multiple: true,
  checkStrictly: false,
  emitPath: true
};

// 用于存储级联选择器选中的原始值
const permissionsSelected = ref([]);

const formData = reactive({
  id: "",
  name: "",
  description: "",
  permissions: [] as string[] // 存储格式化后的权限字符串数组
});

const rules = {
  name: [
    { required: true, message: "请输入角色名称", trigger: "blur" },
    { min: 2, max: 20, message: "长度在 2 到 20 个字符", trigger: "blur" }
  ],
  description: [
    { required: true, message: "请输入角色描述", trigger: "blur" },
    { max: 200, message: "最多输入200个字符", trigger: "blur" }
  ]
};

// 将权限字符串数组转换为级联选择器需要的格式
const parsePermissions = (permissions: string[]) => {
  const result = [];
  permissions.forEach(perm => {
    const [module, actions] = perm.split(":");
    if (module && actions) {
      const actionList = actions.split(",");
      actionList.forEach(action => {
        result.push([module, action]);
      });
    }
  });
  return result;
};

// 处理权限选择变化，将级联选择器的值转换为所需格式
const handlePermissionChange = values => {
  permError.value = "";

  // 确保仪表盘权限始终存在
  const dashboardPermission = ["dashboard", "r"];
  const hasDefaultDashboard = values.some(
    path => path.length === 2 && path[0] === "dashboard" && path[1] === "r"
  );

  if (!hasDefaultDashboard) {
    values.push(dashboardPermission);
  }

  if (!values || values.length === 0) {
    formData.permissions = ["dashboard:r"]; // 至少包含仪表盘权限
    return;
  }

  // 处理选择数据
  const modulePermissions = {};
  // 遍历所有选中的值
  values.forEach(path => {
    if (path.length === 1) {
      // 一级节点，不处理，因为checkStrictly为false时，选择父节点会自动选择所有子节点
      // 子节点的处理会在下面的else if中进行
    } else if (path.length === 2) {
      const module = path[0];
      const permission = path[1];
      // 如果模块不存在，创建一个新数组
      if (!modulePermissions[module]) {
        modulePermissions[module] = [];
      }
      // 添加权限到模块
      if (!modulePermissions[module].includes(permission)) {
        modulePermissions[module].push(permission);
      }
    }
  });
  // 将对象转换为所需的字符串数组格式
  const formattedPermissions = [];
  for (const module in modulePermissions) {
    const permissions = modulePermissions[module].join(",");
    formattedPermissions.push(`${module}:${permissions}`);
  }
  // 更新表单数据
  formData.permissions = formattedPermissions;
  console.log("格式化后的权限:", formData.permissions);
};

// 清除所有错误信息
const clearErrors = () => {
  nameError.value = "";
  descError.value = "";
  permError.value = "";
};

// 确保权限中包含仪表盘权限
const ensureDashboardPermission = (permissions: string[]) => {
  const hasDashboard = permissions.some(perm => perm.startsWith("dashboard:"));
  if (!hasDashboard) {
    permissions.unshift("dashboard:r"); // 在数组开头添加仪表盘权限
  }
  return permissions;
};

// 获取角色信息
const fetchRoleData = async (id: string) => {
  try {
    // 定义响应类型
    interface RoleResponse {
      data: {
        id: string;
        name: string;
        description: string;
        permissions: Array<{
          model: string;
          model_description: string;
          sp: Array<{ [key: string]: string }>;
        }>;
      };
    }

    const response = (await searchRole({ role_id: id })) as RoleResponse;
    const roleData = response.data;

    // 填充表单数据
    formData.id = roleData.id;
    formData.name = roleData.name;
    formData.description = roleData.description;

    // 转换权限对象数组为字符串数组格式
    if (roleData.permissions && Array.isArray(roleData.permissions)) {
      const permissionStrings = roleData.permissions.map(perm => {
        const actions = perm.sp
          .map(sp => {
            const key = Object.keys(sp)[0];
            return key;
          })
          .join(",");
        return `${perm.model}:${actions}`;
      });
      formData.permissions = ensureDashboardPermission(permissionStrings);
    } else {
      formData.permissions = ["dashboard:r"]; // 默认至少包含仪表盘权限
    }

    // 转换权限格式并设置级联选择器的值
    permissionsSelected.value = parsePermissions(formData.permissions);
  } catch (error) {
    ElMessage.error("获取角色信息失败");
    console.error(error);
  }
};

const open = (id: string) => {
  visible.value = true;
  roleId.value = id;
  clearErrors();
  fetchRoleData(id);
};

const handleClose = () => {
  visible.value = false;
  formRef.value?.resetFields();
  // 重置为默认权限而不是清空
  permissionsSelected.value = [["dashboard", "r"]];
  formData.permissions = ["dashboard:r"];
  clearErrors();
};

const handleSubmit = async () => {
  if (!formRef.value) return;
  // 清除之前的错误信息
  clearErrors();
  // 检查表单项
  let hasError = false;
  if (!formData.name || formData.name.trim().length === 0) {
    nameError.value = "请输入角色名称";
    hasError = true;
  } else if (formData.name.length < 2 || formData.name.length > 20) {
    nameError.value = "长度在 2 到 20 个字符";
    hasError = true;
  }
  if (!formData.description || formData.description.trim().length === 0) {
    descError.value = "请输入角色描述";
    hasError = true;
  } else if (formData.description.length > 200) {
    descError.value = "最多输入200个字符";
    hasError = true;
  }
  if (permissionsSelected.value.length === 0) {
    permError.value = "请选择至少一个权限";
    hasError = true;
  }
  if (hasError) {
    return;
  }
  // 所有验证通过，提交表单
  try {
    // 确保提交的权限中包含仪表盘权限
    const finalPermissions = ensureDashboardPermission([
      ...formData.permissions
    ]);

    const res = (await updateRole({
      role_id: formData.id,
      name: formData.name,
      description: formData.description,
      permissions: finalPermissions
    })) as any;
    if (res.code === 0) {
      ElMessage.success("更新成功");
      handleClose();
      emit("success");
    } else {
      ElMessage.error(res.message || "更新失败");
    }
  } catch (error) {
    ElMessage.error("更新失败");
    console.error(error);
  }
};

defineExpose({
  open
});
</script>

<style scoped>
.dialog-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.error-tip {
  padding-top: 4px;
  font-size: 12px;
  line-height: 1;
  color: #f56c6c;
}
</style>
