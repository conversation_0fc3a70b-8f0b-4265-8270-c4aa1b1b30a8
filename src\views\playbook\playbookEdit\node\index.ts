// 基础图形
import CircleNode from "@/views/playbook/playbookEdit/node/basic/CircleNode";
import RectNode from "@/views/playbook/playbookEdit/node/basic/RectNode";
import RectRadiusNode from "@/views/playbook/playbookEdit/node/basic/RectRadiusNode";
import EllipseNode from "@/views/playbook/playbookEdit/node/basic/EllipseNode";
import HtmltNode from "@/views/playbook/playbookEdit/node/vueHtml/ActionHtmlNode";
import ActionHtmlNode from "@/views/playbook/playbookEdit/node/vueHtml/ActionHtmlNode";
import DiamondNode from "@/views/playbook/playbookEdit/node/basic/DiamondNode";
import TriangleNode from "@/views/playbook/playbookEdit/node/basic/TriangleNode";
import StartHtmlNode from "@/views/playbook/playbookEdit/node/vueHtml/StartHtmlNode";
import JudgeHtmlNode from "@/views/playbook/playbookEdit/node/vueHtml/JudgeHtmlNode";
import AggregationHtmlNode from "@/views/playbook/playbookEdit/node/vueHtml/AggregationHtmlNode";
import SubscriptHtmlNode from "@/views/playbook/playbookEdit/node/vueHtml/SubscriptHtmlNode";
import EndHtmlNode from "@/views/playbook/playbookEdit/node/vueHtml/EndHtmlNode";
import approvalHtmlNode from "@/views/playbook/playbookEdit/node/vueHtml/approvalHtmlNode";
import CycleHtmlNode from "@/views/playbook/playbookEdit/node/vueHtml/CycleHtmlNode";
import LoopBodyHtmlNode from "@/views/playbook/playbookEdit/node/vueHtml/LoopBodyHtmlNode";
import type LogicFlow from "@logicflow/core";
import vueEdge from "@/views/playbook/playbookEdit/node/edge/vueEdge";

export const registerCustomElement = (lf: LogicFlow) => {
  // 注册图形
  lf.register(CircleNode);
  lf.register(RectNode);
  lf.register(RectRadiusNode);
  lf.register(EllipseNode);
  lf.register(DiamondNode);
  lf.register(HtmltNode);
  lf.register(TriangleNode);
  lf.register(ActionHtmlNode);
  lf.register(StartHtmlNode);
  lf.register(JudgeHtmlNode);
  lf.register(AggregationHtmlNode);
  lf.register(SubscriptHtmlNode);
  lf.register(EndHtmlNode);
  lf.register(approvalHtmlNode);
  lf.register(CycleHtmlNode);
  lf.register(LoopBodyHtmlNode);
  lf.register(vueEdge);
};
