<template>
  <div class="chat-float-container">
    <el-button
      circle
      class="float-button"
      plain
      type="info"
      @click="toggleChat"
    >
      <IconifyIconOffline
        style="color: #fff"
        height="30px"
        icon="icon-park-outline:computer"
        width="30px"
      />
    </el-button>

    <div v-if="showChat" class="chat-container" @click.stop>
      <WarRoom :is-floating="true" class="war-room" />
    </div>
    <div v-if="showChat" class="overlay" @click="toggleChat" />
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import WarRoom from "@/views/warroom/WarRoom.vue";
import IconifyIconOffline from "@/components/ReIcon/src/iconifyIconOffline";
const showChat = ref(false);

const toggleChat = () => {
  showChat.value = !showChat.value;
};
</script>

<style scoped>
.chat-float-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 2001;
}

.float-button {
  background-color: #2c3e50;
  width: 60px;
  height: 60px;
  min-width: 60px;
  min-height: 60px;
  font-size: 25px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 50% !important;
  flex-shrink: 0;
}

.chat-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90vw;
  height: 90vh;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  z-index: 2000;
}

.war-room {
  flex: 1;
  width: 100%;
  height: calc(100%);
  overflow: hidden;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1999;
}
</style>
