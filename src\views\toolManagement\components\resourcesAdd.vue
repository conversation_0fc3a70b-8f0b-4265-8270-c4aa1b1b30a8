<script lang="ts" setup>
import { ref, watch } from "vue";
import {
  addTool,
  addToolActionPermissions,
  getToolActionPermissions,
  getToolEngineList,
  updateTool
} from "@/api/toolManagement";
import { useUserTableStore } from "@/store/modules/usertable";
import { useRoleableStore } from "@/store/modules/roletable";
import { getRoleList } from "@/api/role";
import { ElMessage } from "element-plus";

// ===== Store实例化 =====
const userTableStore = useUserTableStore();
const roleableStore = useRoleableStore();

// ===== 基础状态管理 =====
const visible = ref(false);
const activeTab = ref("basic");

// ===== 表单相关 =====
// 基础表单数据
const form = ref({
  tool_name: "", // 工具名称
  name: "", // 资源名称
  description: "", // 资源描述
  engine: "", // 新增执行引擎字段
  resources: {} // 资源配置参数
});

// 执行引擎列表
const engineList = ref<string[]>([]);

// 定义 emit
const emit = defineEmits(["success"]);

// 资源参数值更新处理
const handleValueChange = (key: string, value: string | number | boolean) => {
  if (form.value.resources[key]) {
    form.value.resources[key].value = value;
  }
};

// 验证必填字段
const validateRequiredFields = () => {
  const errors = [];

  // 验证基本信息
  if (!form.value.name.trim()) {
    errors.push("资源名称不能为空");
  }

  // 验证资源配置参数
  Object.entries(form.value.resources).forEach(
    ([key, config]: [string, any]) => {
      if (config.required) {
        const value = config.value;
        if (
          value === null ||
          value === undefined ||
          value === "" ||
          (typeof value === "string" && !value.trim())
        ) {
          errors.push(`参数"${key}"为必填项，不能为空`);
        }
      }
    }
  );

  return errors;
};

// 提交基础表单
const handleSubmit = () => {
  // 验证必填字段
  const validationErrors = validateRequiredFields();
  if (validationErrors.length > 0) {
    ElMessage.error(validationErrors[0]); // 显示第一个错误
    return;
  }
  const data = filterEmptyValues(form.value);
  if (isEdit.value) {
    // 编辑
    updateTool(data).then((res: any) => {
      if (res.code === 0) {
        ElMessage.success(res.data.message);
        handleClose();
        emit("success", { name: form.value.tool_name });
      } else {
        ElMessage.error(res.data.message);
      }
    });
  } else {
    // 添加
    addTool(data).then((res: any) => {
      if (res.code === 0) {
        ElMessage.success(res.data.message);
        handleClose();
        emit("success", { name: form.value.tool_name });
      } else {
        ElMessage.error(res.data.message);
      }
    });
  }
};

// ===== 权限控制相关 =====
// 动作相关
const actions = ref([]);
const actionList = ref([]);
const actionForm = ref({
  name: "",
  actions: [],
  users_id: [],
  roles_id: [],
  resource_id: undefined
});

// 获取动作列表数据
const getActionList = () => {
  getToolActionPermissions({
    name: form.value.tool_name,
    resource_id: actionForm.value.resource_id
  }).then((res: any) => {
    actionList.value = res.data;
    console.log("actionList", actionList.value);
  });
};
//获取执行引擎列表
const getEngineList = () => {
  getToolEngineList({}).then((res: any) => {
    if (res.code === 0 && Array.isArray(res.data)) {
      engineList.value = res.data;
    } else {
      engineList.value = [];
    }
  });
};
// 获取资源详情

// 用户穿梭框相关
const transferData = ref([]);
const selectedUsers = ref([]);

// 角色穿梭框相关
const roleTransferData = ref([]);
const selectedRoles = ref([]);

// 穿梭框项目渲染函数
const renderFunc = (item: any) => {
  return `${item.label} (${item.username})`;
};

// 用户穿梭框过滤方法
const filterMethod = (query: string, item: any) => {
  return (
    item.label.toLowerCase().includes(query.toLowerCase()) ||
    item.username.toLowerCase().includes(query.toLowerCase())
  );
};

// 角色穿梭框过滤方法
const filterRoleMethod = (query: string, item: any) => {
  return (
    item.label.toLowerCase().includes(query.toLowerCase()) ||
    item.description.toLowerCase().includes(query.toLowerCase())
  );
};

// 用户选择变化处理
const handleUserChange = (value: string[]) => {
  console.log("选中的用户ID:", selectedUsers.value);
};

// 角色选择变化处理
const handleRoleChange = (value: string[]) => {
  console.log("选中的角色ID:", selectedRoles.value);
};

// 提交权限控制表单
const actionsSubmit = async () => {
  actionForm.value.users_id = selectedUsers.value;
  actionForm.value.roles_id = selectedRoles.value;
  actionForm.value.actions = actions.value;
  const actionsData = filterEmptyValues(actionForm.value);
  console.log("actionForm", actionForm.value);
  console.log("actionsData", actionsData);
  await addToolActionPermissions(actionsData).then((res: any) => {
    if (res.code === 0) {
      console.log(actionsData);
      ElMessage.success(res.data.message);
      handleClose();
      // 提交成功后通知父组件刷新数据
      emit("success", { name: actionForm.value.name });
    } else {
      ElMessage.error(res.data.message);
    }
  });
};

// ===== 弹窗操作方法 =====
// 打开弹窗并初始化数据
const isEdit = ref(false);

const openDialog = async (resources: any, toolName: string, editData?: any) => {
  visible.value = true;
  form.value.resources = resources;
  form.value.tool_name = toolName;
  actionForm.value.name = toolName;

  await getEngineList();

  if (editData) {
    isEdit.value = true;
    form.value.name = editData.name || "";
    form.value.description = editData.description || "";
    form.value.engine = editData.engine || "";
    actionForm.value.resource_id = editData.id;
  } else {
    isEdit.value = false;
    form.value.name = "";
    form.value.description = "";
    form.value.engine = "";
    actionForm.value.resource_id = undefined;
  }

  // 获取并初始化用户列表数据
  await userTableStore.fetchUserList();
  transferData.value = userTableStore.userOptions.map(user => ({
    key: user.value,
    label: user.label,
    username: user.username,
    disabled: false
  }));
  // 获取并初始化角色列表数据
  await roleableStore.fetchRoleList();
  roleTransferData.value = roleableStore.roleList.map(role => ({
    key: role.value,
    label: role.label,
    description: role.description,
    permissions: role.permissions,
    disabled: false
  }));
  console.log("roleTransferData", roleTransferData.value);
  await getRoleList({}).then((res: any) => {
    console.log("角色列表:", res);
  });
  await getActionList();
};

// 关闭弹窗
const handleClose = () => {
  visible.value = false;
};

// ===== 通用工具方法 =====
// 过滤对象空值
const filterEmptyValues = (obj: Record<string, any>) => {
  const result: Record<string, any> = {};
  Object.entries(obj).forEach(([key, value]) => {
    if (value !== "" && value !== null && value !== undefined) {
      result[key] = value;
    }
  });
  return result;
};

// ===== 监听器 =====
// 标签页切换监听
watch(activeTab, newVal => {
  if (newVal === "permission") {
    // 权限标签页切换逻辑待完善
  }
});

// 监听动作权限选择变化
watch(actions, newActions => {
  if (!newActions || !actionList.value) return;
  // 兼容actionList中动作的key为name或action_name
  const selectedActionData = actionList.value.filter(action =>
    newActions.includes(action.name || action.action_name)
  );
  // 合并所有选中动作的users_id
  const allUsersIds = selectedActionData.reduce((acc, curr) => {
    if (curr.users_id && Array.isArray(curr.users_id)) {
      return [...acc, ...curr.users_id];
    }
    return acc;
  }, []);
  selectedUsers.value = [...new Set(allUsersIds)];

  // 合并所有选中动作的roles_id
  const allRolesIds = selectedActionData.reduce((acc, curr) => {
    if (curr.roles_id && Array.isArray(curr.roles_id)) {
      return [...acc, ...curr.roles_id];
    }
    return acc;
  }, []);
  selectedRoles.value = [...new Set(allRolesIds)];
});

// 获取排序后的资源配置数据
const getSortedResources = () => {
  const entries = Object.entries(form.value.resources);
  return entries.sort((a, b) => {
    const orderA = (a[1] as any).order || 0;
    const orderB = (b[1] as any).order || 0;
    return orderA - orderB;
  });
};

// 暴露组件方法
defineExpose({
  openDialog
});
</script>

<template>
  <el-dialog
    v-model="visible"
    :close-on-click-modal="false"
    class="resources-dialog"
    title="资源添加"
    width="900px"
    @close="handleClose"
  >
    <!-- 资源添加 -->
    <div class="dialog-content">
      <el-tabs v-model="activeTab" tab-position="left">
        <el-tab-pane label="基本信息" name="basic">
          <div class="tab-content-wrapper">
            <el-form :model="form" label-width="100px">
              <el-form-item label="资源名称" required>
                <el-input
                  v-model="form.name"

                  :disabled="isEdit"
                />
              </el-form-item>
              <el-form-item label="描述">
                <el-input v-model="form.description"  />
              </el-form-item>
              <el-form-item label="执行引擎">
                <el-select v-model="form.engine" placeholder="请选择执行引擎">
                  <el-option
                    v-for="engine in engineList"
                    :key="engine"
                    :label="engine"
                    :value="engine"
                  />
                </el-select>
              </el-form-item>
            </el-form>
            <div class="divider" />
            <div class="resource-section">
              <div class="section-header">资源配置 · 参数</div>
              <el-table :data="getSortedResources()" border>
                <el-table-column label="参数" prop="0" width="200" />
                <el-table-column label="参数描述" prop="1.description" />
                <el-table-column label="必须" width="80">
                  <template #default="{ row }">
                    <el-tag
                      :type="row[1].required ? 'danger' : 'success'"
                      size="small"
                    >
                      {{ row[1].required ? "必填" : "可选" }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="值">
                  <template #default="{ row }">
                    <div
                      class="input-wrapper"
                      :class="{ 'required-field': row[1].required }"
                    >
                      <!-- 根据数据类型渲染不同的输入组件 -->
                      <template v-if="row[1].data_type === 'boolean'">
                        <el-switch
                          v-model="row[1].value"
                          active-text="是"
                          inactive-text="否"
                          :active-value="true"
                          :inactive-value="false"
                          inline-prompt
                          @change="handleValueChange(row[0], $event)"
                        />
                      </template>
                      <template
                        v-else-if="
                          row[1].data_type === 'number' ||
                          row[1].data_type === 'integer'
                        "
                      >
                        <el-input
                          v-model="row[1].value"
                          type="number"
                          min="0"
                          style="width: 100%"
                          @change="handleValueChange(row[0], $event)"
                        />
                      </template>
                      <template v-else-if="row[1].data_type === 'password'">
                        <el-input
                          v-model="row[1].value"
                          type="password"

                          show-password
                          @input="handleValueChange(row[0], $event)"
                        />
                      </template>
                      <template v-else-if="row[1].data_type === 'textarea'">
                        <el-input
                          v-model="row[1].value"
                          type="textarea"
                          :rows="3"

                          @input="handleValueChange(row[0], $event)"
                        />
                      </template>
                      <template v-else>
                        <!-- 默认文本输入 -->
                        <el-input
                          v-model="row[1].value"

                          @input="handleValueChange(row[0], $event)"
                        />
                      </template>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>
        <!-- 权限控制 -->
        <el-tab-pane label="权限控制" name="permission">
          <div class="tab-content-wrapper">
            <el-form :model="form" label-width="100px">
              <el-form-item label="动作权限" required>
                <el-select
                  v-model="actions"
                  multiple
                  placeholder="请选择动作权限"
                >
                  <el-option
                    v-for="action in actionList"
                    :key="action.id"
                    :label="action.description"
                    :value="action.name"
                  />
                </el-select>
              </el-form-item>
            </el-form>
            <!-- 用户穿梭框 -->
            <el-form-item>
              <div class="transfer-container">
                <el-transfer
                  v-model="selectedUsers"
                  :data="transferData"
                  :filter-method="filterMethod"
                  :titles="['待选用户', '已选用户']"
                  filterable
                  @change="handleUserChange"
                >
                  <template #default="{ option }">
                    <div class="transfer-item">
                      <span class="account">{{ option.username }}</span>
                    </div>
                  </template>
                  <template #left-footer>
                    <div class="transfer-footer">
                      共 {{ transferData.length }} 项
                    </div>
                  </template>
                  <template #right-footer>
                    <div class="transfer-footer">
                      已选 {{ selectedUsers.length }} 项
                    </div>
                  </template>
                </el-transfer>
              </div>
            </el-form-item>
            <!-- 角色穿梭框 -->
            <el-form-item>
              <div class="transfer-container">
                <el-transfer
                  v-model="selectedRoles"
                  :data="roleTransferData"
                  :filter-method="filterRoleMethod"
                  :titles="['待选角色', '已选角色']"
                  filterable
                  @change="handleRoleChange"
                >
                  <template #default="{ option }">
                    <div class="transfer-item">
                      <span class="account">{{ option.label }}</span>
                    </div>
                  </template>
                  <template #left-footer>
                    <div class="transfer-footer">
                      共 {{ roleTransferData.length }} 项
                    </div>
                  </template>
                  <template #right-footer>
                    <div class="transfer-footer">
                      已选 {{ selectedRoles.length }} 项
                    </div>
                  </template>
                </el-transfer>
              </div>
            </el-form-item>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <!-- 固定在底部的按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <Perms :value="['tool:u']">
          <el-button
            v-if="activeTab === 'basic'"
            type="primary"
            @click="handleSubmit"
          >
            保存
          </el-button>
          <el-button
            v-if="activeTab === 'permission'"
            type="primary"
            @click="actionsSubmit"
          >
            保存
          </el-button>
        </Perms>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.resources-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
    max-height: 70vh;
    min-height: 50vh;
  }
  :deep(.el-dialog__footer) {
    padding: 10px 20px;
    border-top: 1px solid var(--el-border-color-lighter);
    background-color: var(--el-bg-color);
  }
}

.dialog-content {
  height: 100%;

  .el-tabs {
    height: 100%;
    display: flex;
  }
}

:deep(.el-tabs--left) {
  .el-tabs__header.is-left {
    width: 110px;
    margin-right: 0;
  }

  .el-tabs__content {
    padding: 0;
    flex: 1;
    overflow: hidden;
  }
}

.tab-content-wrapper {
  height: calc(70vh - 60px);
  overflow-y: auto;
  padding: 20px;
  box-sizing: border-box;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: var(--el-border-color);
  }

  &::-webkit-scrollbar-track {
    background-color: var(--el-fill-color-lighter);
  }
}

.divider {
  height: 1px;
  margin: 20px 0;
  background-color: var(--el-border-color-light);
}

.resource-section {
  .section-header {
    margin-bottom: 16px;
    font-size: 14px;
    color: var(--el-text-color-primary);
  }

  .tip-text {
    display: flex;
    gap: 4px;
    align-items: center;
    margin-top: 8px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
}

.section-header {
  margin: 16px 0;
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.permission-content {
  padding: 20px;

  .tip {
    font-size: 14px;
    color: var(--el-text-color-secondary);
  }
}

.transfer-container {
  margin-bottom: 20px;

  :deep(.el-transfer) {
    width: 100%;
    display: flex;
    align-items: flex-start;
    justify-content: center;

    .el-transfer-panel {
      flex: 1;
      width: auto;
      max-width: none;
      margin: 0 10px;
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 4px;

      .el-transfer-panel__header {
        padding: 8px 12px;
        background-color: var(--el-fill-color-light);
        border-bottom: 1px solid var(--el-border-color-lighter);

        .el-checkbox {
          color: var(--el-text-color-regular);
        }
      }

      .el-transfer-panel__body {
        height: 240px;

        .el-transfer-panel__list {
          height: 200px;
          overflow-y: auto;

          &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
          }

          &::-webkit-scrollbar-thumb {
            border-radius: 3px;
            background-color: var(--el-border-color);
          }

          &::-webkit-scrollbar-track {
            background-color: var(--el-fill-color-lighter);
          }
        }

        .el-transfer-panel__filter {
          margin: 10px;

          .el-input__wrapper {
            padding: 0 8px;
          }
        }

        .transfer-item {
          padding: 4px 0;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .username {
            font-weight: 500;
          }

          .account {
            color: var(--el-text-color-secondary);
            font-size: 12px;
          }

          .description {
            color: var(--el-text-color-secondary);
            font-size: 12px;
            margin-left: 4px;
          }
        }
      }
    }

    .el-transfer__buttons {
      padding: 0 20px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin: auto 10px;

      .el-button {
        margin: 5px 0;
      }
    }
  }

  .transfer-footer {
    padding: 6px 0;
    text-align: center;
    color: var(--el-text-color-secondary);
    font-size: 12px;
    border-top: 1px solid var(--el-border-color-lighter);
  }
}

.dialog-footer {
  margin-top: 20px;
  text-align: right;
}

/* 必填字段样式 */
.input-wrapper {
  position: relative;
}

.required-field {
  &::before {
    content: "*";
    position: absolute;
    top: -2px;
    right: -8px;
    color: #f56c6c;
    font-size: 14px;
    font-weight: bold;
    z-index: 1;
  }
}

/* 必填字段输入框样式 */
.required-field :deep(.el-input__wrapper) {
  border-color: #f56c6c;
}

.required-field :deep(.el-textarea__inner) {
  border-color: #f56c6c;
}
</style>
