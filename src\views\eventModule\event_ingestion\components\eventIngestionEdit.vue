<template>
  <div>
    <!-- 抽屉组件 -->
    <el-drawer
      :before-close="handleClose"
      :model-value="visible"
      :size="600"
      :title="drawerTitle"
      @update:model-value="handleVisibleChange"
    >
      <div class="drawer-content">
        <!-- 表单内容 -->
        <div class="form-container">
          <el-form
            ref="formRef"
            :model="form"
            :rules="rules"
            label-width="120px"
          >
            <!-- 配置名称 -->
            <el-form-item label="配置名称：" prop="event_ingestion_name">
              <el-input
                v-model="form.event_ingestion_name"
                :disabled="true"

              />
            </el-form-item>
            <!-- 描述 -->
            <el-form-item label="描述：" prop="event_describe">
              <el-input
                v-model="form.event_describe"

                type="textarea"
              />
            </el-form-item>
            <!-- 接入方式 -->
            <el-form-item label="接入方式：" prop="event_source_type">
              <el-select
                v-model="form.event_source_type"
                placeholder="请选择事件来源类型"
                @change="handleEventSourceTypeChange"
              >
                <el-option label="syslog" value="syslog" />
                <el-option label="kafka" value="kafka" />
              </el-select>
            </el-form-item>
            <!-- 事件来源地址 -->
            <el-form-item
              v-if="form.event_source_type === 'kafka'"
              label="事件来源地址："
              prop="event_source_address"
            >
              <el-input
                v-model="form.event_source_address"
                placeholder="多个事件来源地址之间用逗号分隔"
                type="textarea"
              />
              <!-- 服务器配置说明 -->
              <div class="tip-text">
                <el-icon>
                  <InfoFilled />
                </el-icon>
                多个服务器之间用逗号分隔；如果server端绑定hostname，请使用hostname访问，示例:
                ***********:9092, lion-kafka:9092
              </div>
            </el-form-item>
            <!-- syslog端口 -->
            <el-form-item
              v-if="form.event_source_type === 'syslog'"
              label="syslog端口："
              prop="syslog_port"
              required
            >
              <el-input
                v-model="form.syslog_port"
                max="600"
                min="500"

                type="number"
              />
            </el-form-item>
            <!-- 协议 -->
            <el-form-item
              v-if="form.event_source_type === 'syslog'"
              label="协议："
              prop="communication_type"
            >
              <el-select
                v-model="form.communication_type"
                placeholder="请选择协议"
                style="width: 100%"
              >
                <el-option label="TCP" value="tcp" />
                <el-option label="UDP" value="udp" />
              </el-select>
            </el-form-item>
            <!-- 消费者组ID -->
            <div v-if="form.event_source_type === 'kafka'">
              <!-- 事件来源类型为kafka时，显示消费者组ID -->
              <el-form-item label="消费者组ID：" prop="kafka_group_id">
                <el-input
                  v-model="form.kafka_group_id"

                />
              </el-form-item>
              <!-- 主题名称 -->
              <el-form-item label="主题名称：" prop="kafka_topic">
                <el-input
                  v-model="form.kafka_topic"

                  type="textarea"
                />
                <!-- 主题配置说明 -->
                <div class="tip-text">
                  <el-icon>
                    <InfoFilled />
                  </el-icon>
                  多个主题之间用逗号分隔，例如：topic1,topic2,topic3
                </div>
              </el-form-item>
              <!-- 认证机制 -->
              <el-form-item label="认证机制：" prop="sasl_mechanism">
                <el-select
                  v-model="form.sasl_mechanism"
                  placeholder="请选择认证机制"
                >
                  <el-option label="SCRAM-PLAINTEXT(PLAIN)" value="PLAIN" />
                  <el-option
                    label="SCRAM-PLAINTTEXT(SCRAM-SHA-256)"
                    value="SCRAM-SHA-256"
                  />
                  <el-option
                    label="SCRAM-PLAINTEXT(SCRAM-SHA-512)"
                    value="SCRAM-SHA-512"
                  />
                </el-select>
              </el-form-item>
              <!-- 用户名 -->
              <el-form-item label="用户名：" prop="sasl_username">
                <el-input
                  v-model="form.sasl_username"

                />
              </el-form-item>
              <!-- 密码 -->
              <el-form-item label="密码：" prop="sasl_password">
                <el-input
                  v-model="form.sasl_password"

                  show-password
                />
              </el-form-item>
            </div>
          </el-form>
        </div>
        <!-- 底部按钮区域 -->
        <div class="drawer-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
import { defineEmits, defineProps, ref, watch } from "vue";
import { InfoFilled } from "@element-plus/icons-vue";
import type { FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import { eventIngestionDetail, eventIngestionUpdate } from "@/api/event";

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  rowData: {
    type: Object,
    default: () => ({})
  }
});

// 定义组件事件
const emit = defineEmits(["update:visible", "refresh"]);

// 组件数据定义
const drawerTitle = ref("事件接入编辑");
const formRef = ref();

// 表单数据
const form = ref({
  id: "", // 事件ID
  event_ingestion_name: "", // 配置名称
  event_describe: "", // 描述
  event_source_type: "", // 接入方式
  event_source_address: "", // 事件来源地址
  syslog_port: "", // syslog端口
  communication_type: "", // 协议
  transforms_type: "", // 转换类型
  kafka_group_id: "", // 消费者组ID
  transforms_config: "", // 数据解析配置
  sasl_mechanism: "", // 认证机制
  sasl_username: "", // 用户名
  sasl_password: "", // 密码
  kafka_topic: "" // 主题名称
});

// 用于控制高级配置显示的状态变量
const enable_ssl = ref(false);
const enable_sasl = ref(false);

// 监听 visible 变化，当抽屉打开时获取详情数据
watch(
  () => props.visible,
  async newVal => {
    if (newVal && props.rowData && props.rowData.id) {
      // 调用获取详情接口
      await eventIngestionDetail({ id: props.rowData.id })
        .then((res: any) => {
          const eventData = res.data.event;
          const sourcesData = res.data.sources;
          const newForm = { ...form.value }; // 复制现有表单结构

          // 1. 填充通用字段
          newForm.id = eventData.id;
          newForm.event_ingestion_name = eventData.event_ingestion_name;
          newForm.event_describe = eventData.event_ingestion_describe;
          newForm.event_source_type = eventData.event_source_type;

          // 2. 根据事件来源类型填充特定字段
          if (eventData.event_source_type === "syslog") {
            if (eventData.syslog_config) {
              const match = String(eventData.syslog_config).match(
                /^([a-zA-Z]+)(\d+)$/
              );
              if (match) {
                newForm.communication_type = match[1]; // 协议
                newForm.syslog_port = match[2]; // 端口
              }
            }
          } else if (eventData.event_source_type === "kafka") {
            // 2.1 获取动态的 vector_name
            const vectorName = eventData.vector_name;
            if (vectorName && sourcesData && sourcesData[vectorName]) {
              // 2.2 获取 kafka 的详细配置
              const kafkaConfig = sourcesData[vectorName];

              // 2.3 正确映射数据
              newForm.event_source_address =
                kafkaConfig.bootstrap_servers || "";
              newForm.kafka_group_id = kafkaConfig.group_id || "";
              // 将 topics 数组转换为逗号分隔的字符串
              newForm.kafka_topic = Array.isArray(kafkaConfig.topics)
                ? kafkaConfig.topics.join(",")
                : "";

              // 填充 SASL 认证信息
              if (kafkaConfig.sasl && kafkaConfig.sasl.enabled) {
                newForm.sasl_mechanism = kafkaConfig.sasl.mechanism || "";
                newForm.sasl_username = kafkaConfig.sasl.username || "";
                newForm.sasl_password = kafkaConfig.sasl.password || "";
              } else {
                // 如果没有SASL配置，则清空相关字段
                newForm.sasl_mechanism = "";
                newForm.sasl_username = "";
                newForm.sasl_password = "";
              }
            }
          }
          form.value = newForm; // 更新表单数据
        })
        .catch(error => {
          console.error("获取详情失败:", error);
        });
    }
  }
);

// 关闭抽屉
const handleClose = () => {
  enable_ssl.value = false;
  enable_sasl.value = false;
  form.value = {
    id: "", // 事件ID
    event_ingestion_name: "", // 配置名称
    event_describe: "", // 描述
    event_source_type: "", // 接入方式
    event_source_address: "", // 事件来源地址
    syslog_port: "", // syslog端口
    communication_type: "", // 协议
    transforms_type: "", // 转换类型
    kafka_group_id: "", // 消费者组ID
    transforms_config: "", // 数据解析配置
    sasl_mechanism: "", // 认证机制
    sasl_username: "", // 用户名
    sasl_password: "", // 密码
    kafka_topic: "" // 主题名称
  };
  emit("update:visible", false);
};
// 监听抽屉打开状态变化
const handleVisibleChange = (visible: boolean) => {
  emit("update:visible", visible);
};

// 事件来源类型改变
const handleEventSourceTypeChange = (val: string) => {
  // 清除所有相关字段
  if (val === "syslog") {
    // 切换到 syslog 时清除 kafka 相关字段
    form.value.event_source_address = "";
    form.value.kafka_group_id = "";
    form.value.kafka_topic = "";
    form.value.sasl_mechanism = "";
    form.value.sasl_username = "";
    form.value.sasl_password = "";
  } else if (val === "kafka") {
    // 切换到 kafka 时清除 syslog 相关字段
    form.value.syslog_port = "";
    form.value.communication_type = "";
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  const valid = await formRef.value.validate().catch(() => false);
  if (valid) {
    // 处理事件来源地址为数组
    const event_source_address = form.value.event_source_address
      ? form.value.event_source_address
          .split(",")
          .map(addr => addr.trim())
          .filter(addr => addr)
      : [];
    // 处理主题名称为数组
    const kafka_topic = form.value.kafka_topic
      ? form.value.kafka_topic
          .split(",")
          .map(topic => topic.trim())
          .filter(topic => topic)
      : [];

    // 创建提交数据对象，过滤掉空值和event_ingestion_name
    const submitData = Object.entries({
      ...form.value,
      event_source_address,
      kafka_topic
    }).reduce((acc, [key, value]) => {
      if (
        value !== "" &&
        value !== null &&
        value !== undefined &&
        key !== "event_ingestion_name" &&
        (Array.isArray(value) ? value.length > 0 : true)
      ) {
        acc[key] = value;
      }
      return acc;
    }, {});

    console.log("submitData:", submitData);
    // eventIngestionUpdate(submitData).then((res: any) => {
    //   if (res.code === 0) {
    //     ElMessage.success(res.data);
    //     handleClose();
    //     emit("refresh");
    //   } else {
    //     ElMessage.error(res.msg || "更新失败");
    //   }
    // });
  }
};

// 表单验证规则
const rules = ref<FormRules>({
  syslog_port: [
    { required: true, message: "请输入syslog端口", trigger: "blur" },
    {
      validator: (_, value, callback) => {
        if (value < 500 || value > 600) {
          callback(new Error("端口范围应在500-600之间"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  communication_type: [
    { required: true, message: "请选择协议", trigger: "change" }
  ]
});
</script>

<style lang="scss" scoped>
/* 抽屉内容样式 */
.drawer-content {
  padding: 0;
  /* 标题样式 */
  .section-title {
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }

  /* 表单容器样式 */
  .form-container {
    padding: 0px;
  }

  /* 提示文本样式 */
  .tip-text {
    display: flex;
    gap: 4px;
    align-items: center;
    margin-top: 4px;
    font-size: 12px;
    color: #909399;
  }

  /* 底部按钮区域样式 */
  .drawer-footer {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding: 20px;
  }
}

/* 表单标签样式 */
:deep(.el-form-item__label) {
  font-weight: normal;
}

/* 复选框样式 */
:deep(.el-checkbox) {
  margin-right: 20px;
}
</style>
