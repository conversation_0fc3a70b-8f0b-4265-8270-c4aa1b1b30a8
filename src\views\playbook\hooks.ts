import { isEmpty, isString } from "@pureadmin/utils";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import {
  type LocationQueryRaw,
  type RouteParamsRaw,
  useRoute,
  useRouter
} from "vue-router";
import usePlaybookStore from "@/store/modules/playbook";

export function useDetail() {
  const route = useRoute();
  const router = useRouter();
  const playbookStore = usePlaybookStore();
  const getParameter = isEmpty(route.params) ? route.query : route.params;

  function toDetail(
    parameter: LocationQueryRaw | RouteParamsRaw,
    model: "query" | "params"
  ) {
    // ⚠️ 这里要特别注意下，因为vue-router在解析路由参数的时候会自动转化成字符串类型，比如在使用useRoute().route.query或useRoute().route.params时，得到的参数都是字符串类型
    // 所以在传参的时候，如果参数是数字类型，就需要在此处 toString() 一下，保证传参跟路由参数类型一致都是字符串，这是必不可少的环节！！！
    Object.keys(parameter).forEach(param => {
      if (!isString(parameter[param])) {
        parameter[param] = parameter[param].toString();
      }
    });
    if (model === "query") {
      // 保存信息到标签页
      useMultiTagsStoreHook().handleTags("push", {
        path: `/tabs/query-detail`,
        name: "TabQueryDetail",
        query: parameter,
        meta: {
          title: {
            zh: `No.${parameter.id} - 详情信息`,
            en: `No.${parameter.id} - DetailInfo`
          },
          // 如果使用的是非国际化精简版title可以像下面这么写
          // title: `No.${index} - 详情信息`,
          // 最大打开标签数
          dynamicLevel: 3
        }
      });
      // 路由跳转
      router.push({ name: "TabQueryDetail", query: parameter });
    } else if (model === "params") {
      router.push({ name: "playbookEdit", params: parameter });
      //在跳转时，将剧本ID版本ID和剧本状态保存到pinia
      playbookStore.flow_id = String(parameter.flow_id);
      playbookStore.version_id = String(parameter.version_id);
      playbookStore.status = String(parameter.status);
      console.log("playbookStore.flow_id:", playbookStore.flow_id);
      console.log("playbookStore.version_id:", playbookStore.version_id);
      console.log("playbookStore.status:", playbookStore.status);
    }
  }

  // 用于页面刷新，重新获取浏览器地址栏参数并保存到标签页
  const initToDetail = (model: "query" | "params") => {
    if (getParameter) toDetail(getParameter, model);
  };

  return { toDetail, initToDetail, getParameter, router };
}
