import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";
// import { getToken } from "@/utils/auth";
//更新工具资源
export const updateTool = (data: any) => {
  return http.post(baseUrlApi("tools/resource/update"), { data });
};

//获取所有应用列表
export const getToolList = (data: any) => {
  return http.post(baseUrlApi("tools/all"), { data });
};

//上传工具接口 - 流式处理
export const uploadTool = async (
  formData: FormData,
  onProgress?: (data: string) => void
) => {
  // 无需token，cookie中aioe-auth
  const response = await fetch(baseUrlApi("tools/upload"), {
    method: "POST",
    body: formData
  });
  if (!response.ok) {
    throw new Error(`网络错误: ${response.status}`);
  }
  const reader = response.body?.getReader();
  const decoder = new TextDecoder();
  let result = "";
  if (reader) {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value, { stream: true });
      result += chunk;
      // 调用进度回调
      if (onProgress) {
        onProgress(chunk);
      }
    }
  }
  return result;
};

//新增工具资源
export const addTool = (data: any) => {
  return http.post(baseUrlApi("tools/resource/add"), { data });
};

//下载工具 ZIP 文件接口
export const downloadTool = (data: any) => {
  return http.post(
    baseUrlApi("tools/download"),
    { data },
    {
      responseType: "blob"
    }
  );
};

//获取工具的资源模型
export const getToolModel = (data: any) => {
  return http.post(baseUrlApi("tools/resource/model"), { data });
};

//删除工具资源
export const deleteTool = (data: any) => {
  return http.post(baseUrlApi("tools/resource/delete"), { data });
};
//删除工具全部版本
export const deleteToolAll = (data: any) => {
  return http.post(baseUrlApi("tools/delete/all"), { data });
};
//删除工具单个版本
export const deleteToolVersion = (data: any) => {
  return http.post(baseUrlApi("tools/delete/version"), { data });
};
//获取工具的资源详情
export const getToolDetail = (data: any) => {
  return http.post(baseUrlApi("tools/resource/detailed"), { data });
};

//获取工具的所有动作列表
export const getToolActionList = (data: any) => {
  return http.post(baseUrlApi("tools/actions"), { data });
};

//获取具体工具的所有版本列表
export const getToolVersionList = (data: any) => {
  return http.post(baseUrlApi("tools/versions"), { data });
};

//获取工具的所有资源列表
export const getToolResourceList = (data: any) => {
  return http.post(baseUrlApi("tools/resource/all"), { data });
};

//更新工具状态
export const updateToolStatus = (data: any) => {
  return http.post(baseUrlApi("tools/upstatus"), { data });
};

//新增标签
export const addTag = (data: any) => {
  return http.post(baseUrlApi("tools/tag/add"), { data });
};

//删除标签
export const deleteTag = (data: any) => {
  return http.post(baseUrlApi("tools/tag/delete"), { data });
};

//获取工具全部动作权限
export const getToolActionPermissions = (data: any) => {
  return http.post(baseUrlApi("tools/action/permissions/select"), {
    data
  });
};
//新增动作权限
export const addToolActionPermissions = (data: any) => {
  return http.post(baseUrlApi("tools/action/permissions/add"), {
    data
  });
};
//修改工具健康检查配置信息
export const updateToolHealthCheck = (data: any) => {
  return http.post(baseUrlApi("tools/health/check/config"), {
    data
  });
};
//获取工具健康检查配置信息
export const ToolHealthConfig = (data: any) => {
  return http.post(baseUrlApi("tools/health/check/get"), {
    data
  });
};
//获取工具执行引擎列表
export const getToolEngineList = (data: any) => {
  return http.post(baseUrlApi("tools/engine/list"), { data });
};
