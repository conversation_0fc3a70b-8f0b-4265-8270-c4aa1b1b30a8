import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

export type UserResult = {
  success: any;
  code: number;
  msg?: string;
  data: {
    permit_id: any;
    avatar: string;
    permissions: any[];
    mfa_enable: boolean;
    mfa_uri: any;
    /** 是否绑定MFA */
    mfa_bind: boolean;
    /** 用户ID */
    id: string;
    /** 用户名 */
    username: string;
    /** 邮箱 */
    email: string;
    /** 手机号 */
    phone: string;
    /** 创建时间 */
    ctime: string;
    /** 更新时间 */
    utime: string;
    /** 账号状态 */
    disabled: boolean;
    /** 角色列表 */
    roles: string[];
  };
};

export type RefreshTokenResult = {
  code: number;
  data: {
    "aioe-auth": string;
  };
};

/** 登录 */
export const getLogin = (data?: object) => {
  return http.request<UserResult>("post", baseUrlApi("user/login"), {
    data
  });
};

/** 刷新`token` */
export const refreshTokenApi = (data?: object) => {
  return http.request<RefreshTokenResult>(
    "post",
    baseUrlApi("user/refresh-token"),
    {
      data
    }
  );
};
