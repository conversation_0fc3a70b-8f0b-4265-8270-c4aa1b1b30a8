import { HtmlNode, HtmlNodeModel } from "@logicflow/core";
import { createApp, h } from "vue";
import CycleNode from "@/views/playbook/playbookEdit/icon/CycleNode.vue";

class CycleNodeView extends HtmlNode {
  isMounted: boolean;
  r: any;
  app: any;

  constructor(props) {
    super(props);
    this.isMounted = false;
    //在这里添加的属性，才能被自定义HTML节点拿到
    this.r = h(CycleNode, {
      id: props.model.id,
      properties: props.model.getProperties(),
      text: props.model.inputData,
      model: props.model,
      graphModel: props.graphModel,
      onBtnCopyClick: () => {
        props.graphModel.eventCenter.emit("custom:onBtnCopyClick", {
          props: this.props
        });
      },
      onBtnDelClick: () => {
        props.graphModel.eventCenter.emit("custom:onBtnDelClick", {
          props: this.props
        });
      },
      onBtnWebSocket: () => {
        props.graphModel.eventCenter.emit("custom:onBtnWebSocket", {
          props: this.props
        });
      },
      onBtnCloseClick: () => {
        props.graphModel.eventCenter.emit("custom:onBtnCloseClick", {
          props: this.props
        });
      }
    });
    this.app = createApp({
      render: () => this.r
    });
  }

  //更新节点信息
  shouldUpdate() {
    if (this.preProperties && this.preProperties === this.currentProperties)
      return;
    this.preProperties = this.currentProperties;
    return true;
  }

  setHtml(rootEl) {
    if (!this.isMounted) {
      this.isMounted = true;
      const node = document.createElement("div");
      rootEl.appendChild(node);
      this.app.mount(node);
    } else {
      this.r.component.props.properties = this.props.model.getProperties();
    }
  }
}

class CycleNodeModel extends HtmlNodeModel {
  setAttributes() {
    const { scale = 1, width = 200, height = 100 } = this.properties;
    this.width = width;
    this.height = height * (scale as number);
    this.text.editable = false;
    this.inputData = this.text.value;
  }

  //扩大节点
  updateField() {
    // 为了保持节点顶部位置不变，在节点变化后，对节点进行一个位移,位移距离为添加高度的一半。
    this.move(0, 30 / 2);
    // 更新节点连接边的path
    this.incoming.edges.forEach(egde => {
      // 调用自定义的更新方案
      (egde as any).updatePathByAnchor();
    });
    this.outgoing.edges.forEach(edge => {
      // 调用自定义的更新方案
      (edge as any).updatePathByAnchor();
    });
  }

  //缩小节点
  resetField() {
    // 为了保持节点顶部位置不变，在节点变化后，对节点进行一个位移,位移距离为添加高度的一半。
    this.move(0, -30 / 2);
  }

  // 定义节点只有左右两个锚点. 锚点位置通过中心点和宽度算出来。
  getDefaultAnchor() {
    const { width, height, x, y, id } = this;
    return [
      {
        x: x - width / 2,
        y: y - height / 2 + 50,
        name: "left",
        id: `${id}_1`
      },
      {
        x: x + width / 2,
        y: y - height / 2 + 50,
        name: "right",
        id: `${id}_0`
      },
      {
        x: x,
        y: y - height / 2 + 100,
        name: "bottom",
        id: `${id}_2`
      }
    ];
  }

  getOutlineStyle() {
    const style = super.getOutlineStyle();
    style.stroke = "none";
    style.hover.stroke = "red";
    return style;
  }
}

export default {
  type: "cycle-node",
  view: CycleNodeView,
  model: CycleNodeModel
};
