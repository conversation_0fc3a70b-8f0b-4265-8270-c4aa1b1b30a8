import { defineStore } from "pinia";
import {
  resetRouter,
  router,
  routerArrays,
  storageLocal,
  store,
  type userType
} from "../utils";
import {
  getLogin,
  refreshTokenApi,
  type RefreshTokenResult,
  type UserResult
} from "@/api/user";
import { useMultiTagsStoreHook } from "./multiTags";
import { type DataInfo, removeToken, setToken, userKey } from "@/utils/auth";
import { useRefreshTokenStore } from "./RefreshToken";
import { useChatStore } from "../warChat";
import { initRouter } from "@/router/utils";

export const useUserStore = defineStore({
  id: "pure-user",
  state: (): userType => ({
    // 用户ID
    id: storageLocal().getItem<DataInfo<number>>(userKey)?.id ?? "",
    // 头像
    avatar: storageLocal().getItem<DataInfo<number>>(userKey)?.avatar ?? "",
    // 用户名
    username: storageLocal().getItem<DataInfo<number>>(userKey)?.username ?? "",
    // 邮箱
    email: storageLocal().getItem<DataInfo<number>>(userKey)?.email ?? "",
    // 联系电话
    phone: storageLocal().getItem<DataInfo<number>>(userKey)?.phone ?? "",
    // 登录id控制
    permit_id:
      storageLocal().getItem<DataInfo<number>>(userKey)?.permit_id ?? [],
    // 昵称
    display_name:
      storageLocal().getItem<DataInfo<number>>(userKey)?.display_name ?? "",
    // 页面级别权限
    roles: storageLocal().getItem<DataInfo<number>>(userKey)?.roles ?? [],
    // 按钮级别权限
    permissions:
      storageLocal().getItem<DataInfo<number>>(userKey)?.permissions ?? [],
    // 是否勾选了登录页的免登录
    isRemembered: false,
    // 登录页的免登录存储几天，默认7天
    loginDay: 7
  }),
  actions: {
    /** 存储用户ID */
    SET_ID(id: string | number) {
      this.id = id;
    },
    /** 存储邮箱 */
    SET_EMAIL(email: string) {
      this.email = email;
    },
    /** 存储联系电话 */
    SET_PHONE(phone: string) {
      this.phone = phone;
    },
    /** 存储登录id控制 */
    SET_PERMIT_ID(permit_id: Array<string>) {
      this.permit_id = permit_id;
    },
    /** 存储头像 */
    SET_AVATAR(avatar: string) {
      this.avatar = avatar;
    },
    /** 存储用户名 */
    SET_USERNAME(username: string) {
      this.username = username;
    },
    /** 存储昵称 */
    SET_NICKNAME(display_name: string) {
      this.display_name = display_name;
    },
    /** 存储角色 */
    SET_ROLES(roles: Array<string>) {
      this.roles = roles;
    },
    /** 存储按钮级别权限 */
    SET_PERMS(permissions: Array<string>) {
      this.permissions = permissions;
    },
    /** 存储是否勾选了登录页的免登录 */
    SET_ISREMEMBERED(bool: boolean) {
      this.isRemembered = bool;
    },
    /** 设置登录页的免登录存储几天 */
    SET_LOGINDAY(value: number) {
      this.loginDay = Number(value);
    },
    /** 登入 */
    async loginByUsername(data) {
      return new Promise<UserResult>((resolve, reject) => {
        getLogin(data)
          .then(async data => {
            if (data?.code === 0) {
              // 保存用户信息到本地存储
              const userInfo = {
                id: data.data.id,
                username: data.data.username,
                email: data.data.email,
                phone: data.data.phone,
                permit_id: data.data.permit_id,
                display_name: data.data.username,
                roles: data.data.roles,
                permissions: data.data.permissions || [],
                avatar: data.data.avatar || ""
              };
              storageLocal().setItem(userKey, userInfo);
              // 更新 state
              this.SET_ID(userInfo.id);
              this.SET_USERNAME(userInfo.username);
              this.SET_EMAIL(userInfo.email);
              this.SET_PHONE(userInfo.phone);
              this.SET_PERMIT_ID(userInfo.permit_id);
              this.SET_NICKNAME(userInfo.display_name);
              this.SET_ROLES(userInfo.roles);
              this.SET_PERMS(userInfo.permissions);
              this.SET_AVATAR(userInfo.avatar);

              // 设置token
              // setToken({
              //   accessToken: data.data["aioe-auth"],
              //   refreshToken: data.data["aioe-auth"],
              //   expires: new Date(Date.now() + 24 * 60 * 60 * 1000)
              // });
              // 只有在MFA未启用时才立即初始化路由和启动刷新定时器
              if (!data.data.mfa_enable) {
                useRefreshTokenStore().startRefreshTimer();
                resetRouter(); // 重置路由
                await initRouter(); // 初始化路由
              }
              // 如果MFA已启用，路由初始化和定时器启动将在MFA认证完成后进行
            }
            resolve(data);
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    /** 前端登出（不调用接口） */
    logOut() {
      // 关闭WebSocket连接
      const chatStore = useChatStore();
      chatStore.closeWebSocket();
      useRefreshTokenStore().stopRefreshTimer();
      this.id = ""; // 清除用户ID
      this.username = "";
      this.email = "";
      this.phone = "";
      this.permit_id = [];
      this.roles = [];
      this.permissions = [];
      // 清除token
      removeToken();
      // 清除多标签
      useMultiTagsStoreHook().handleTags("equal", [...routerArrays]);
      // 停止刷新定时器
      useRefreshTokenStore().stopRefreshTimer();
      // 重置路由
      resetRouter();
      // 跳转登录
      router.replace("/login").catch(() => {});
    },
    /** 刷新`token` */
    async handRefreshToken(data) {
      return new Promise<RefreshTokenResult>((resolve, reject) => {
        refreshTokenApi(data)
          .then(data => {
            if (data) {
              setToken({
                accessToken: data.data["aioe-auth"],
                refreshToken: data.data["aioe-auth"],
                expires: new Date(Date.now() + 24 * 60 * 60 * 1000) // 设置24小时过期
              });
              resolve(data);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    }
  }
});

export function useUserStoreHook() {
  return useUserStore(store);
}
