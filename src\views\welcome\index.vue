<script lang="ts" setup>
import { computed, onMounted, onUnmounted, ref } from "vue";

import * as echarts from "echarts";
import {
  getAppsNum,
  getDashboardData,
  getMttrData,
  getPlaybooksNum,
  getPlaybookSuccessRate
} from "@/api/dashboard";
import { useECharts } from "@pureadmin/utils";

defineOptions({
  name: "Welcome"
});

// 使用echart实现图表：事件总数、事件数量变化趋势、事件处理状态分布、事件平均关闭时间MTTR、执行次数TOP10的剧本
// 获取数据通过 getDashboardData 接口

let theme = computed(() => {
  return "default";
});

const getCurrentDate = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

const getPastMonthDate = () => {
  const now = new Date();
  now.setMonth(now.getMonth() - 1);
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

const loading = ref({
  lineChart: true,
  pieChart: true
});

const timeRange = ref([getPastMonthDate(), getCurrentDate()]); // 设置默认时间为最近一个月
const trendChart = ref();
const { setOptions: setEventTrendChart } = useECharts(trendChart, { theme });
const statusChart = ref();
const { setOptions: setStatusChart } = useECharts(statusChart, { theme });
const top10Chart = ref();
const { setOptions: setTopChart } = useECharts(top10Chart, { theme });
const mttrChart = ref();
const { setOptions: setMttrChart } = useECharts(mttrChart, { theme });
const actionChart = ref();
const { setOptions: setActionChart } = useECharts(actionChart, { theme });
const pbSuccChart = ref();
const { setOptions: setPbSuccChart } = useECharts(pbSuccChart, { theme });

const eventTotal = ref(0);
const mttr = ref(0);
const playbookTotal = ref(0);
const appTotal = ref(0);

//首次加载拉取数据
onMounted(async () => {
  timeRange.value = [getPastMonthDate(), getCurrentDate()];
  await fetchAllData();
});

onUnmounted(() => {
  // 销毁图表实例
  if (trendChart.value) trendChart.value.dispose();
  if (statusChart.value) statusChart.value.dispose();
  if (top10Chart.value) top10Chart.value.dispose();
  if (mttrChart.value) mttrChart.value.dispose();
});

const fetchAllData = async () => {
  try {
    await fetchPlaybookTotal();
    await fetchAppTotal();
    await fetchEventTrend();
    await fetchEventStatus();
    await fetchMTTR();
    await fetchTop10Playbooks();
    await fetchTop10Actions();
    await fetchSuccessRate();
  } catch (error) {
    console.error("数据获取失败:", error);
  }
};

interface GroupedItem {
  day: string;
  time: number;
  counts: number;
}

// 新增：根据时间跨度判断分组粒度
const getGroupingInterval = (start: string, end: string) => {
  const startDate = new Date(start).getTime();
  const endDate = new Date(end).getTime();
  const diffDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
  if (diffDays <= 7) return "day";
  else if (diffDays <= 31) return "week";
  else return "month";
};

// 新增：按时间粒度分组聚合数据
const groupDataByInterval = (data: any[], interval: string) => {
  const grouped: Record<string, GroupedItem> = {};
  data.forEach((item: any) => {
    const date = new Date(item.day);
    let key;
    if (interval === "day") {
      key = item.day;
    } else if (interval === "week") {
      const week = Math.floor((date.getDate() - 1) / 7) + 1;
      key = `${String(date.getMonth() + 1)}月第${week}周`;
    } else if (interval === "month") {
      key = `${date.getFullYear()}年${String(date.getMonth() + 1).padStart(2, "0")}月`;
    }
    if (!grouped[key])
      grouped[key] = { day: key, time: date.getTime(), counts: 0 };
    grouped[key].counts += item.counts;
  });

  return Object.values(grouped).sort((a, b) => a.time - b.time);
};

const fetchPlaybookTotal = async () => {
  const response = (await getPlaybooksNum({})) as any;
  playbookTotal.value = response.data;
};

const fetchAppTotal = async () => {
  const response = (await getAppsNum({})) as any;
  appTotal.value = response.data;
};

const fetchEventTrend = async () => {
  const params = {
    table_name: "events",
    group_by: "ctime",
    count_key: "id",
    start_time: timeRange.value[0] + " 00:00:00",
    end_time: timeRange.value[1] + " 23:59:59"
  };

  const response = (await getDashboardData(params)) as any;

  const interval = getGroupingInterval(timeRange.value[0], timeRange.value[1]);
  const groupedData = groupDataByInterval(response.data, interval);

  setEventTrendChart({
    tooltip: { trigger: "axis" },
    xAxis: {
      type: "category",
      data: groupedData.map(item => item.day),
      axisLabel: {
        formatter: value => {
          return value;
        }
      }
    },
    yAxis: {
      type: "value"
    },
    series: [
      {
        name: "事件数量",
        type: "line",
        smooth: true,
        data: groupedData.map(item => item.counts),
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "rgba(58,77,233,0.8)" },
            { offset: 1, color: "rgba(58,77,233,0.1)" }
          ])
        },
        lineStyle: { width: 3, color: "#3a4de9" },
        itemStyle: { color: "#3a4de9" },
        label: {
          show: true,
          position: "top",
          formatter: "{c}",
          fontSize: 14,
          color: "#3a4de9"
        }
      }
    ],
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: "10%",
      containLabel: true
    }
  });
};

const fetchEventStatus = async () => {
  const params = {
    table_name: "events",
    group_by: "event_process_status",
    count_key: "id",
    start_time: timeRange.value[0] + " 00:00:00",
    end_time: timeRange.value[1] + " 23:59:59"
  };

  const response = (await getDashboardData(params)) as any;
  const data = response.data;
  eventTotal.value = data.reduce(
    (acc: number, cur: any) => acc + cur.counts,
    0
  );

  setStatusChart({
    legend: {
      orient: "vertical",
      right: 10, // 图例距离右侧
      bottom: 20, // 图例距离底部
      data: data.map(item => item.event_process_status)
    },
    series: [
      {
        name: "事件状态",
        type: "pie",
        radius: ["30%", "60%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: "#fff",
          borderWidth: 2
        },
        label: {
          show: true,
          position: "outside",
          formatter: "{c}（{d}%）",
          fontSize: 14,
          align: "center"
        },
        emphasis: {
          label: {
            show: true,
            fontSize: "16",
            fontWeight: "bold"
          }
        },
        labelLine: {
          show: true, // 显示连接线
          length: 10, // 连接线第一段长度
          length2: 20 // 连接线第二段长度
        },
        data: data.map(item => ({
          value: item.counts,
          name: item.event_process_status
        }))
      }
    ],
    // 缩小图表内容以适应更小的高度
    grid: {
      top: "10%",
      bottom: "10%",
      left: "10%",
      right: "10%",
      containLabel: true
    }
  });
};

const fetchMTTR = async () => {
  const params = {
    start_time: timeRange.value[0] + " 00:00:00",
    end_time: timeRange.value[1] + " 23:59:59"
  };

  const response = (await getMttrData(params)) as any;
  // 根据结果中的counts计算平均值
  mttr.value =
    response.data.length > 0
      ? Number(
          (
            response.data.reduce(
              (acc: number, cur: any) => acc + cur.counts,
              0
            ) / response.data.length
          ).toFixed(1)
        )
      : 0;

  const interval = getGroupingInterval(timeRange.value[0], timeRange.value[1]);
  const groupedData = groupDataByInterval(response.data, interval);

  // validCount > 0 ? Number((totalHours / validCount).toFixed(1)) : 0;

  // 更新趋势图
  setMttrChart({
    tooltip: { trigger: "axis" },
    xAxis: {
      type: "category",
      data: groupedData.map(item => item.day),
      axisLabel: {
        formatter: value => {
          return value;
        }
      }
    },
    yAxis: {
      type: "value"
    },
    series: [
      {
        name: "事件平均响应时长",
        type: "line",
        smooth: true,
        data: groupedData.map(item => item.counts.toFixed(1)),
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "rgba(58,77,233,0.8)" },
            { offset: 1, color: "rgba(58,77,233,0.1)" }
          ])
        },
        lineStyle: { width: 3, color: "#3a4de9" },
        itemStyle: { color: "#3a4de9" },
        label: {
          show: true,
          position: "top",
          formatter: "{c}小时",
          fontSize: 14,
          color: "#3a4de9"
        }
      }
    ],
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: "10%",
      containLabel: true
    }
  });
};

// 新增：分页状态管理
const top10Data = ref<any[]>([]); // 剧本执行次数排行总数据
const top10CurrentPage = ref(1);
const top10PageSize = 5;

const actionData = ref<any[]>([]); // 动作执行次数排行总数据
const actionCurrentPage = ref(1);
const actionPageSize = 5;

const successRateData = ref<any[]>([]); // 剧本执行成功率排行总数据
const successRateCurrentPage = ref(1);
const successRatePageSize = 5;

// 修改剧本执行次数排行排序逻辑为从高到低
const fetchTop10Playbooks = async () => {
  const params = {
    table_name: "runlog",
    group_by: "playbook_name",
    count_key: "run_id",
    conditions: { playbook_name__not: "执行动作" },
    start_time: timeRange.value[0] + " 00:00:00",
    end_time: timeRange.value[1] + " 23:59:59"
  };

  const response = (await getDashboardData(params)) as any;
  const allData = response.data;
  // 修改排序逻辑：从高到低排序（b.counts - a.counts）
  top10Data.value = allData.sort((a, b) => b.counts - a.counts);
  updateTop10Chart();
};

// 新增：更新剧本执行次数排行图表（带分页）
const updateTop10Chart = () => {
  const startIndex = (top10CurrentPage.value - 1) * top10PageSize;
  const pagedData = top10Data.value
    .slice(startIndex, startIndex + top10PageSize)
    .sort((a, b) => a.counts - b.counts);

  const playbooks = pagedData.map(item => item.playbook_name);
  const counts = pagedData.map(item => item.counts);

  setTopChart({
    tooltip: {
      trigger: "axis",
      axisPointer: { type: "shadow" }
    },
    xAxis: {
      type: "value",
      axisTick: { show: true },
      axisLine: { show: true },
      splitLine: { show: true }
    },
    yAxis: {
      type: "category",
      data: playbooks,
      axisTick: { show: true },
      axisLine: { show: true },
      axisLabel: {
        formatter: value =>
          value.slice(0, 10) + (value.length > 10 ? "..." : ""),
        fontSize: 14,
        color: "#303133"
      }
    },
    series: [
      {
        name: "执行次数",
        type: "bar",
        data: counts,
        barWidth: "60%",
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: "rgba(58,77,233,0.8)" },
            { offset: 1, color: "#3a4de9" }
          ])
        },
        label: {
          show: true,
          position: "right",
          formatter: "{c}",
          fontSize: 14,
          color: "#303133"
        }
      }
    ],
    grid: {
      left: "5%",
      right: "13%",
      bottom: "5%",
      top: "5%",
      containLabel: true
    }
  });
};

// 新增：剧本执行次数分页改变处理
const handleTop10PageChange = (page: number) => {
  top10CurrentPage.value = page;
  updateTop10Chart();
};

// 新增：更新动作执行次数排行图表（带分页）
const updateActionChart = () => {
  const startIndex = (actionCurrentPage.value - 1) * actionPageSize;
  const pagedData = actionData.value
    .slice(startIndex, startIndex + actionPageSize)
    .sort((a, b) => a.counts - b.counts);

  const actions = pagedData.map(item => item.action_display_name);
  const counts = pagedData.map(item => item.counts);

  setActionChart({
    tooltip: {
      trigger: "axis",
      axisPointer: { type: "shadow" }
    },
    // 修改坐标轴方向为竖向柱状图
    xAxis: {
      type: "value",
      axisTick: { show: true },
      axisLine: { show: true },
      splitLine: { show: true }
    },
    yAxis: {
      type: "category",
      data: actions,
      axisTick: { show: true },
      axisLine: { show: true },
      axisLabel: {
        formatter: value =>
          value.slice(0, 10) + (value.length > 10 ? "..." : ""),
        fontSize: 14,
        color: "#303133"
      }
    },
    series: [
      {
        name: "执行次数",
        type: "bar",
        data: counts,
        barWidth: "60%",
        // 使用与"事件数量变化趋势"相同的渐变配色
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: "rgba(58,77,233,0.8)" },
            { offset: 1, color: "#3a4de9" }
          ])
        },
        label: {
          show: true,
          position: "right",
          formatter: "{c}",
          fontSize: 13,
          color: "#303133"
        }
      }
    ],
    // 调整网格布局适应竖向图表
    grid: {
      left: "5%",
      right: "13%",
      bottom: "5%",
      top: "5%",
      containLabel: true
    }
  });
};

// 新增：动作执行次数分页改变处理
const handleActionPageChange = (page: number) => {
  actionCurrentPage.value = page;
  updateActionChart();
};

const fetchTop10Actions = async () => {
  const params = {
    table_name: "runlog",
    group_by: "action_display_name",
    count_key: "id",
    conditions: { action_display_name__isnull: false },
    start_time: timeRange.value[0] + " 00:00:00",
    end_time: timeRange.value[1] + " 23:59:59"
  };

  const response = (await getDashboardData(params)) as any;
  const allData = response.data;
  actionData.value = allData.sort((a, b) => b.counts - a.counts);
  updateActionChart();
};

// 修改剧本执行成功率分页绑定变量
const handleSuccessRatePageChange = (page: number) => {
  successRateCurrentPage.value = page;
  updateSuccessRateChart();
};

const updateSuccessRateChart = () => {
  const startIndex = (successRateCurrentPage.value - 1) * successRatePageSize;
  const pagedData = successRateData.value
    .slice(startIndex, startIndex + successRatePageSize)
    .sort((a, b) => b.success_rate - a.success_rate);

  const playbooks = pagedData.map(item => item.playbook_name);
  const rates = pagedData.map(item => item.success_rate * 100); // 转换为百分比

  setPbSuccChart({
    tooltip: {
      trigger: "axis",
      axisPointer: { type: "shadow" },
      formatter: (params: any) => {
        return `${params[0].name}<br/>执行成功率: ${params[0].value.toFixed(1)}%`;
      }
    },
    xAxis: {
      type: "value",
      axisTick: { show: true },
      axisLine: { show: true },
      splitLine: { show: true }
    },
    yAxis: {
      type: "category",
      data: playbooks,
      axisTick: { show: true },
      axisLine: { show: true },
      axisLabel: {
        formatter: value =>
          value.slice(0, 10) + (value.length > 10 ? "..." : ""),
        fontSize: 14,
        color: "#303133"
      }
    },
    series: [
      {
        name: "成功率",
        type: "bar",
        data: rates,
        barWidth: "60%",
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: "rgba(58,77,233,0.8)" },
            { offset: 1, color: "#3a4de9" }
          ])
        },
        label: {
          show: true,
          position: "right",
          formatter: "{c}%",
          fontSize: 14,
          color: "#303133"
        }
      }
    ],
    grid: {
      left: "5%",
      right: "13%",
      bottom: "5%",
      top: "5%",
      containLabel: true
    }
  });
};

const fetchSuccessRate = async () => {
  const response = (await getPlaybookSuccessRate({})) as any;
  const allData = response.data;
  // 修改排序逻辑：从高到低排序
  successRateData.value = allData.sort(
    (a, b) => a.success_rate - b.success_rate
  );
  updateSuccessRateChart();
};
</script>

<template>
  <div class="dashboard-container bg-white dark:bg-[#141414]">
    <el-card>
      <!-- 日期选择器部分保持不变 -->
      <div justify="end" style="margin-bottom: 20px">
        <el-date-picker
          v-model="timeRange"
          end-placeholder="结束时间"
          range-separator="至"
          start-placeholder="开始时间"
          style="width: fit-content"
          type="daterange"
          value-format="YYYY-MM-DD"
          @change="fetchAllData"
        />
      </div>

      <!-- 统计卡片行保持不变 -->
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card>
            <div class="stat-header">
              <i class="el-icon-s-data" />
              <span>事件总数</span>
            </div>
            <div class="stat-value">{{ eventTotal }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card>
            <div class="stat-header">
              <i class="el-icon-timer" />
              <span>平均响应时长(MTTR)</span>
            </div>
            <div class="stat-value">{{ mttr }} 小时</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card>
            <div class="stat-header">
              <i class="el-icon-timer" />
              <span>已上线剧本总数</span>
            </div>
            <div class="stat-value">{{ playbookTotal }} 个</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card>
            <div class="stat-header">
              <i class="el-icon-timer" />
              <span>已对接应用数量</span>
            </div>
            <div class="stat-value">{{ appTotal }} 个</div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 中间图表行保持不变 -->
      <el-row :gutter="20" style="margin-top: 15px">
        <el-col :span="8">
          <el-card>
            <div class="chart-header">
              <h3>事件处理状态分布</h3>
            </div>
            <div ref="statusChart" style="height: 250px" />
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card>
            <div class="chart-header">
              <h3>事件数量变化趋势</h3>
            </div>
            <div ref="trendChart" style="height: 250px" />
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card>
            <div class="chart-header">
              <h3>MTTR变化趋势</h3>
            </div>
            <div ref="mttrChart" style="height: 250px" />
          </el-card>
        </el-col>
      </el-row>

      <!-- 关键修改：最后一行图表容器 -->
      <el-row :gutter="20" class="charts-row">
        <el-col :span="8">
          <el-card class="chart-card">
            <div class="chart-header">
              <h3>剧本执行成功率排行</h3>
            </div>
            <div class="chart-container">
              <div ref="pbSuccChart" class="chart-content" />
              <el-pagination
                :current-page="successRateCurrentPage"
                :page-size="successRatePageSize"
                :total="successRateData.length"
                layout="prev, pager, next"
                size="small"
                @current-change="handleSuccessRatePageChange"
              />
            </div>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card class="chart-card">
            <div class="chart-header">
              <h3>剧本执行次数排行</h3>
            </div>
            <div class="chart-container">
              <div ref="top10Chart" class="chart-content" />
              <el-pagination
                :current-page="top10CurrentPage"
                :page-size="top10PageSize"
                :total="top10Data.length"
                layout="prev, pager, next"
                size="small"
                @current-change="handleTop10PageChange"
              />
            </div>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card class="chart-card">
            <div class="chart-header">
              <h3>动作执行次数排行</h3>
            </div>
            <div class="chart-container">
              <div ref="actionChart" class="chart-content" />
              <el-pagination
                :current-page="actionCurrentPage"
                :page-size="actionPageSize"
                :total="actionData.length"
                layout="prev, pager, next"
                size="small"
                @current-change="handleActionPageChange"
              />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<style scoped>
.dashboard-container {
  height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
}

.dashboard-container :deep(.el-card) {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.1);
}

.dashboard-container :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 修改：优化图表行的flex布局，确保正确分配空间 */
.charts-row {
  flex: 1;
  margin-top: 15px !important;
  display: flex;
  min-height: 0; /* 允许flex项目收缩 */
}

.charts-row :deep(.el-col) {
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许flex项目收缩 */
}

/* 修改：优化图表卡片样式以支持更好的高度自适应 */
.chart-card {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chart-card :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许flex项目收缩 */
  padding: 6px; /* 减小内边距 */
}

/* 修改：优化图表容器样式 */
.chart-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许flex项目收缩 */
}

.chart-content {
  flex: 1;
  min-height: 0; /* 允许收缩 */
}

.stat-header {
  display: flex;
  align-items: center;
  color: black;
  font-size: 18px;
  font-weight: bold;
  height: 30px;
}

.stat-header i {
  margin-right: 10px;
  font-size: 20px;
  color: #3a4de9;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  text-align: center;
  color: #3a4de9;
  margin: 10px 0;
}

.chart-header {
  padding: 0;
  text-align: center;
  color: black;
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 5px;
}

.el-row {
  margin-bottom: 5px;
}

.el-col {
  margin-bottom: 5px;
}

/* 分页控件样式 */
.el-pagination {
  padding: 10px 0;
  justify-content: center;
  border-top: 1px solid #ebeef5;
}
</style>
