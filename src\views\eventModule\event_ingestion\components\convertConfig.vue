<template>
  <div class="convert-config-container">
    <el-drawer
      :before-close="handleClose"
      :destroy-on-close="true"
      :model-value="visible"
      :size="800"
      :title="'数据解析配置'"
      class="convert-config-drawer"
      @update:model-value="handleVisibleChange"
    >
      <el-form
        ref="formRef"
        :model="convertData"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="事件名称：">
          <el-input
            v-model="convertData.event_ingestion_name"
            :disabled="true"

          />
        </el-form-item>
        <!-- 转换类型 -->
        <el-form-item label="解析方式：" prop="transforms_type">
          <el-select
            v-model="convertData.transforms_type"
            placeholder="请选择解析器"
            style="width: 100%"
          >
            <el-option
              v-for="option in transformsOptions"
              :key="option.key"
              :label="option.type"
              :value="option.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="convertData.transforms_type === 'grok'"
          prop="transforms_config"
        >
          <el-input
            v-model="transforms_config"
            :rows="4"

            type="textarea"
          />
        </el-form-item>
        <el-form-item
          v-if="convertData.transforms_type === 'kv'"
          prop="transforms_config"
        >
          <div class="key-value-settings">
            <div class="separator-item">
              <span class="separator-label">键值对分隔符：</span>
              <el-select
                v-model="key_delimiter_value"
                class="separator-input"
                placeholder="请选择"
              >
                <el-option :value="'\n'" label="回车" />
                <el-option label="逗号 ," value="," />
                <el-option label="分号 ;" value=";" />
                <el-option label="自定义" value="custom" />
              </el-select>
              <el-input
                v-if="key_delimiter_value === 'custom'"
                v-model="customKeyDelimiterValue"
                class="custom-separator-input"

              />
            </div>
            <div class="separator-item">
              <span class="separator-label">键值分隔符：</span>
              <el-select
                v-model="key_value_delimiter"
                class="separator-input"
                placeholder="请选择"
              >
                <el-option label="等号 =" value="=" />
                <el-option label="冒号 :" value=":" />
                <el-option label="空格" value=" " />
                <el-option label="自定义" value="custom" />
              </el-select>
              <el-input
                v-if="key_value_delimiter === 'custom'"
                v-model="customKeyValueDelimiter"
                class="custom-separator-input"

              />
            </div>
          </div>
        </el-form-item>
        <!-- 原始日志样例 -->
        <el-form-item class="log-form-item">
          <div class="logs-container">
            <div class="logs-header">
              <span class="title">原始日志样例：</span>
              <el-button :loading="loading" type="primary" @click="fetchLogs">
                测试
              </el-button>
            </div>
            <div v-if="loading" class="loading-container">
              <el-skeleton :rows="3" animated />
            </div>
            <template v-else>
              <el-empty v-if="!originalLog" description="暂无日志数据" />
              <div v-else class="logs-list">
                <div class="log-item">
                  <div class="log-content">{{ originalLog }}</div>
                </div>
              </div>
            </template>
          </div>
        </el-form-item>
        <!-- 解析日志 -->
        <el-form-item class="log-form-item">
          <div class="logs-container">
            <div class="logs-header">
              <span class="title">解析结果：</span>
            </div>
            <div v-if="loading" class="loading-container">
              <el-skeleton :rows="3" animated />
            </div>
            <template v-else>
              <el-empty v-if="!parsedLogContent" description="暂无解析数据" />
              <div v-else class="logs-list">
                <div class="log-item">
                  <div class="log-content">
                    <json-viewer
                      v-if="isValidJSON(parsedLogContent)"
                      :expand-depth="999"
                      :value="JSON.parse(parsedLogContent)"
                      copyable
                      sort
                      style="background-color: #f5f7fa"
                    />
                    <pre
                      v-else
                      style="
                        margin: 0;
                        white-space: pre-wrap;
                        word-break: break-word;
                      "
                    >
                      {{ parsedLogContent }}
                    </pre>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </el-form-item>
        <!-- 底部按钮区域 -->
        <div class="drawer-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </div>
      </el-form>
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
import { defineEmits, defineProps, onMounted, ref, watch } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import {
  eventIngestionDetail,
  eventIngestionOriginalLog,
  eventIngestionParseLog,
  eventIngestionTransformsList,
  eventIngestionUpdate
} from "@/api/event";
import JsonViewer from "vue-json-viewer";
// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  rowData: {
    type: Object,
    default: () => ({})
  }
});

// 定义组件事件
const emit = defineEmits(["update:visible", "refresh"]);
const convertData = ref<any>({});
const originalLog = ref<string | null>(null);
const event_source_type = ref<string>("");
const transforms_config = ref<string>("");
const key_value_delimiter = ref<string>("");
const key_delimiter_value = ref<string>("");
const customKeyValueDelimiter = ref("");
const customKeyDelimiterValue = ref("");
const loading = ref(false);
const formRef = ref<FormInstance>();
const parsedLogContent = ref<string | null>(null);
const transformsOptions = ref<any[]>([]);

// 检查字符串是否为有效的JSON
const isValidJSON = (str: string): boolean => {
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
};

// 定义表单验证规则
const rules = ref<FormRules>({
  transforms_type: [
    { required: true, message: "请选择转换类型", trigger: "change" }
  ],
  transforms_config: [
    {
      required: true,
      message: "请输入数据解析配置",
      trigger: "blur",
      validator: (rule, value, callback) => {
        const currentType = convertData.value.transforms_type;
        // Json、CEF、syslog、common_log、logfmt、xml、Nginx_log类型不需要验证配置
        if (
          currentType === "JSON" ||
          currentType === "CEF" ||
          currentType === "syslog" ||
          currentType === "common_log" ||
          currentType === "logfmt" ||
          currentType === "xml" ||
          currentType === "Nginx_log"
        ) {
          callback();
          return;
        }
        // key-value类型需要验证分隔符
        if (currentType === "key-value") {
          if (!key_value_delimiter.value || !key_delimiter_value.value) {
            callback(new Error("请完善键值分隔符和键值对分隔符配置"));
            return;
          }
          callback();
          return;
        }
        // Grok、Delimiter和Regex类型需要验证transforms_config
        if (
          (currentType === "Grok" ||
            currentType === "Delimiter" ||
            currentType === "Regex") &&
          !transforms_config.value
        ) {
          callback(new Error("请输入数据解析配置"));
          return;
        }
        callback();
      }
    }
  ]
});

// 监听visible属性变化
watch(
  () => props.visible,
  async newVal => {
    if (newVal && props.rowData && props.rowData.id) {
      loading.value = true;
      try {
        const res: any = await eventIngestionDetail({ id: props.rowData.id });
        if (res.code === 0 && res.data && res.data.event) {
          const event = res.data.event;
          convertData.value = {
            ...event,
            transforms_type: event.transforms_type || ""
          };
          event_source_type.value = event.transforms_type;
          // 事件名称
          convertData.value.event_ingestion_name =
            event.event_ingestion_name || "";
          // 转换类型
          convertData.value.transforms_type = event.transforms_type || "";

          // 配置内容
          if (event.transforms_type === "Grok") {
            transforms_config.value =
              event.config_info?.Grok?.transformsconfig || "";
          } else if (event.transforms_type === "Delimiter") {
            transforms_config.value =
              event.config_info?.Delimiter?.transformsconfig || "";
          } else if (event.transforms_type === "Regex") {
            transforms_config.value =
              event.config_info?.Regex?.transformsconfig || "";
          } else if (event.transforms_type === "key-value") {
            // 键值分隔符 - 直接绑定 key_value_delimiter
            const keyValue =
              event.config_info?.["key-value"]?.key_value_delimiter;
            if (keyValue === "=" || keyValue === ":" || keyValue === " ") {
              key_value_delimiter.value = keyValue;
              customKeyValueDelimiter.value = "";
            } else {
              key_value_delimiter.value = "custom";
              customKeyValueDelimiter.value = keyValue || "";
            }
            // 键值对分隔符 - 直接绑定 key_delimiter_value
            const keyPair =
              event.config_info?.["key-value"]?.key_delimiter_value;
            if (keyPair === "\n" || keyPair === "," || keyPair === ";") {
              key_delimiter_value.value = keyPair;
              customKeyDelimiterValue.value = "";
            } else {
              key_delimiter_value.value = "custom";
              customKeyDelimiterValue.value = keyPair || "";
            }
          } else if (
            event.transforms_type === "JSON" ||
            event.transforms_type === "CEF" ||
            event.transforms_type === "syslog" ||
            event.transforms_type === "common_log" ||
            event.transforms_type === "logfmt" ||
            event.transforms_type === "xml" ||
            event.transforms_type === "Nginx_log"
          ) {
            // 这些类型不需要配置，清空相关字段
            transforms_config.value = "";
            key_value_delimiter.value = "";
            key_delimiter_value.value = "";
          } else {
            transforms_config.value = "";
            key_value_delimiter.value = "";
            key_delimiter_value.value = "";
          }
        }
      } finally {
        loading.value = false;
        await fetchLogs();
      }
    }
  }
);

// 获取原始日志的处理逻辑
const fetchLogs = async () => {
  if (!props.rowData || !props.rowData.id) return;
  loading.value = true;
  try {
    const res = (await eventIngestionOriginalLog({
      id: props.rowData.id,
      size: 1
    })) as any;
    if (res.code === 0 && Array.isArray(res.data) && res.data.length > 0) {
      const logObj = res.data[0];
      let logToShow = null;

      // 优先处理 message 字段，否则使用整个日志对象
      if (logObj && typeof logObj.message === "string" && logObj.message) {
        logToShow = logObj.message;
      } else {
        logToShow = logObj;
      }

      // 对要显示的内容进行健壮的处理，确保其为格式化好的字符串
      let finalContent = "";
      if (typeof logToShow === "string") {
        try {
          // 尝试将其作为JSON字符串解析和格式化
          finalContent = JSON.stringify(logToShow, null, 2);
        } catch (e) {
          // 如果解析失败，说明它就是普通字符串
          finalContent = logToShow;
        }
      } else if (typeof logToShow === "object" && logToShow !== null) {
        // 如果是对象，直接格式化
        finalContent = JSON.stringify(logToShow, null, 2);
      } else {
        finalContent = String(logToShow);
      }
      originalLog.value = finalContent;

      // 获取到日志后，立即获取解析日志
      if (originalLog.value) {
        await fetchParseLogs();
      }
    } else {
      originalLog.value = null;
      parsedLogContent.value = null; // 清空解析日志
    }
  } catch (error) {
    originalLog.value = null;
    parsedLogContent.value = null; // 清空解析日志
  } finally {
    loading.value = false;
  }
};

// 获取解析日志
const fetchParseLogs = async () => {
  if (convertData.value.transforms_type === "") {
    return;
  }
  // 构建API参数
  const apiParams: any = {
    id: convertData.value.id,
    msg: originalLog.value,
    transforms_type: convertData.value.transforms_type,
    transforms_config: transforms_config.value,
    key_value_delimiter: key_value_delimiter.value,
    key_delimiter_value: key_delimiter_value.value
  };
  const parseRes: any = await eventIngestionParseLog(apiParams);
  if (parseRes.code === 0) {
    let content = parseRes.data;
    let finalContent = "";
    if (typeof content === "string") {
      // 如果是字符串，直接显示，不尝试解析JSON
      finalContent = content;
    } else if (typeof content === "object" && content !== null) {
      // 返回的已是对象，直接格式化
      finalContent = JSON.stringify(content, null, 2);
    } else {
      // 处理其他原始类型（如数字、布尔值）
      finalContent = String(content);
    }
    // 确保换行符正确显示
    parsedLogContent.value = finalContent.replace(/\\n/g, "\n");
  }
};

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    // 处理表单提交
    const formData: {
      id: any;
      transforms_type: string;
      transforms_config?: string;
      key_value_delimiter?: string;
      key_delimiter_value?: string;
    } = {
      id: convertData.value.id,
      transforms_type: convertData.value.transforms_type
    };

    if (
      convertData.value.transforms_type === "Grok" ||
      convertData.value.transforms_type === "Delimiter" ||
      convertData.value.transforms_type === "Regex"
    ) {
      if (transforms_config.value) {
        formData.transforms_config = transforms_config.value;
      }
    } else if (convertData.value.transforms_type === "key-value") {
      formData.key_value_delimiter =
        key_value_delimiter.value === "custom"
          ? customKeyValueDelimiter.value
          : key_value_delimiter.value;
      formData.key_delimiter_value =
        key_delimiter_value.value === "custom"
          ? customKeyDelimiterValue.value
          : key_delimiter_value.value;
    }

    // 过滤掉所有空值
    const filteredFormData = Object.fromEntries(
      Object.entries(formData).filter(
        ([_, value]) => value !== "" && value !== null && value !== undefined
      )
    );

    console.log("filteredFormData:", filteredFormData);
    const res: any = await eventIngestionUpdate(filteredFormData);
    if (res.code === 0) {
      ElMessage.success("更新成功");
      await fetchParseLogs();
      await fetchLogs();
      emit("refresh");
    } else {
      ElMessage.error(res.msg || "更新失败");
    }
  } catch (error) {
    console.error("表单验证失败:", error);
    return;
  }
};

// 关闭抽屉
const handleClose = () => {
  event_source_type.value = "";
  transforms_config.value = "";
  key_value_delimiter.value = "";
  key_delimiter_value.value = "";
  originalLog.value = null;
  parsedLogContent.value = null;
  convertData.value = {};
  loading.value = false;
  emit("update:visible", false);
};

// 监听抽屉打开状态变化
const handleVisibleChange = (visible: boolean) => {
  emit("update:visible", visible);
};
onMounted(async () => {
  try {
    const res: any = await eventIngestionTransformsList({});
    if (res.code === 0 && Array.isArray(res.data)) {
      transformsOptions.value = res.data;
      console.log("转换类型选项:", transformsOptions.value);
    }
  } catch (error) {
    console.error("获取转换类型列表失败:", error);
  }
});
</script>

<style lang="scss" scoped>
.convert-config-container {
  .convert-config-drawer {
    :deep(.el-drawer__header) {
      margin-bottom: 0;
      padding: 16px 20px;
      border-bottom: 1px solid #ebeef5;
      color: #303133;
      font-weight: bold;
    }

    :deep(.el-drawer__body) {
      padding: 0;
      position: relative;
      height: 100%;
    }

    .el-form {
      height: calc(100% - 60px);
      overflow-y: auto;
      padding: 20px;
    }

    .drawer-footer {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 60px;
      padding: 10px 20px;
      background-color: var(--el-bg-color);
      border-top: 1px solid var(--el-border-color-light);
      text-align: right;
      z-index: 10;

      .el-button {
        margin-left: 12px;
      }
    }
  }

  .logs-container {
    width: 100%;
    margin-top: 10px;

    .logs-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 0 4px;

      .title {
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        position: relative;
        padding-left: 12px;

        &::before {
          content: "";
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 16px;
          background-color: var(--el-color-primary);
          border-radius: 2px;
        }
      }
    }

    .logs-list {
      background: var(--el-bg-color);
      border: 1px solid var(--el-border-color);
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      max-width: 100%;
    }

    .log-item {
      position: relative;
      display: flex;
      padding: 16px;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: var(--el-fill-color-light);
      }

      .log-content {
        flex: 1;
        background-color: var(--el-fill-color-light);
        border-radius: 4px;
        padding: 12px;
        font-family: "Menlo", "Monaco", "Consolas", "Courier New", monospace;
        font-size: 13px;
        line-height: 1.5;
        white-space: pre-wrap;
        word-break: break-word;
        color: var(--el-text-color-primary);
        margin: 0;
        border-left: 3px solid var(--el-color-primary-light-5);
      }
    }
  }

  .loading-container {
    padding: 20px;
    background-color: var(--el-fill-color-light);
    border-radius: 8px;
    margin-bottom: 16px;
  }

  .key-value-settings {
    .separator-item {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .separator-label {
        width: 100px;
        color: var(--el-text-color-regular);
        font-size: 14px;
        margin-right: 8px;
      }

      .separator-input {
        width: 240px;
      }

      .custom-separator-input {
        width: 200px;
        margin-left: 8px;
      }
    }
  }

  :deep(.log-form-item) {
    margin: 0;
    display: flex;
    justify-content: center;
    width: 100%;
    margin-bottom: 10px;

    .el-form-item__content {
      margin-left: 0 !important;
      width: 100%;
      max-width: 900px;
    }
  }
}
</style>
