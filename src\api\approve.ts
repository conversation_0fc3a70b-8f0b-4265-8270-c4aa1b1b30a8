import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

//获取审批人为当前用户的审批任务信息（需要approve:r权限）
export const approveList = (data: any) => {
  return http.post(baseUrlApi("approve/list"), { data });
};

//发送审核结果（需要approve:u权限）
export const apiApproveUpdate = (data: any) => {
  return http.post(baseUrlApi("approve/update"), { data });
};

//获取当前登录用户的待处理任务信息（需要approve:r权限）
export const apiApproveTodolist = (data: any) => {
  return http.post(baseUrlApi("approve/todolist"), { data });
};

//获取审批任务详情（需要approve:r权限）
export const apiApproveDetail = (data: any) => {
  return http.post(baseUrlApi("approve/detail"), { data });
};
