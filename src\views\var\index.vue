<template>
  <div class="event-module-container bg-white dark:bg-[#141414]">
    <el-card>
      <div class="top-actions">
        <!-- 搜索区域 -->
        <div class="search-area">
          <Perms :value="['var:r']">
            <el-input
              v-model="keyword"
              clearable
              style="width: 300px"
              @clear="search"
            >
              <template #append>
                <el-button @click="search">
                  <el-icon>
                    <Search />
                  </el-icon>
                </el-button>
              </template>
            </el-input>
          </Perms>
        </div>
        <!-- 新建按钮 -->
        <Perms :value="['var:c']">
          <el-button type="primary" @click="addVar">新增</el-button>
        </Perms>
      </div>
      <el-table :data="varList" border style="width: 100%; margin: 20px 0">
        <el-table-column label="名称" prop="name" width="300" />
        <el-table-column
          :formatter="formatValue"
          label="值（列表格式）"
          prop="value"
        />
        <el-table-column label="更新人" prop="updator" width="200">
          <template #default="scope">
            {{ scope.row.updator.display_name }}({{
              scope.row.updator.username
            }})
          </template>
        </el-table-column>
        <el-table-column label="更新时间" prop="utime" width="200">
          <template #default="scope">
            {{ formatTime(scope.row.utime) }}
          </template>
        </el-table-column>
        <!-- 设置权限只有var: u或者d 才显示操作列-->
        <Perms :value="['var:u', 'var:d']">
          <el-table-column label="操作" prop="description" width="200">
            <template #default="{ row }">
              <div>
                <Perms :value="['var:u']">
                  <el-tooltip :hide-after="0" content="编辑" placement="top">
                    <el-button link type="primary" @click="editVar(row)">
                      <IconifyIconOffline
                        height="20"
                        icon="mingcute:edit-line"
                        width="20"
                      />
                    </el-button>
                  </el-tooltip>
                </Perms>
                <Perms :value="['var:d']">
                  <el-tooltip :hide-after="0" content="删除" placement="top">
                    <span class="ml-3">
                      <el-popconfirm
                        cancel-button-text="取消"
                        confirm-button-text="确认"
                        title="确认要删除该变量吗？"
                        @confirm="delVar(row.name)"
                      >
                        <template #reference>
                          <el-button link type="danger">
                            <IconifyIconOffline
                              height="18"
                              icon="icon-park-outline:delete"
                              width="18"
                            />
                          </el-button>
                        </template>
                      </el-popconfirm>
                    </span>
                  </el-tooltip>
                </Perms>
              </div>
            </template>
          </el-table-column>
        </Perms>
      </el-table>
      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="page"
          v-model:page-size="Size"
          :page-sizes="[15, 50, 100]"
          :total="total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      <var-add ref="varAddRef" @refresh="getdataList" /><!--新增变量-->
      <var-edit ref="varEditRef" @refresh="getdataList" /><!--编辑变量-->
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { deleteVar, getVarList } from "@/api/var";
import varAdd from "./components/varAdd.vue"; //新增变量
import varEdit from "./components/varEdit.vue"; //编辑变量
import { ElMessage } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import { Icon } from "@iconify/vue";
import IconifyIconOffline from "@/components/ReIcon/src/iconifyIconOffline";
const varList = ref([]);
const page = ref(1);
const Size = ref(15);
const total = ref(0);
const varAddRef = ref(); //新增变量
const varEditRef = ref(); //编辑变量
const keyword = ref("");

//搜索
const search = () => {
  getdataList();
};

//新增变量
const addVar = () => {
  varAddRef.value.showDrawer();
};

//编辑变量
const editVar = (row: any) => {
  varEditRef.value.showDrawer(row);
};

//删除变量
const delVar = (name: any) => {
  console.log(name);
  deleteVar({ name: name }).then((res: any) => {
    if (res.code === 0) {
      ElMessage.success("删除成功");
      getdataList();
    } else {
      ElMessage.error("删除失败");
    }
  });
};

//表格value值格式化
const formatValue = (value: any) => {
  if (value.value) {
    return JSON.stringify(value.value);
  } else {
    return "无";
  }
};

// 时间格式化函数
const formatTime = timestamp => {
  if (!timestamp) return "";
  const date = new Date(timestamp);
  return date
    .toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false
    })
    .replace(/\//g, "-");
};

//首次加载拉取数据
onMounted(() => {
  getdataList();
});

//分页
const handleSizeChange = (size: number) => {
  Size.value = size;
};
//分页
const handleCurrentChange = (current: number) => {
  page.value = current;
};

//拉取数据
const getdataList = () => {
  getVarList({
    page: page.value,
    size: Size.value,
    keyword: keyword.value
  }).then((res: any) => {
    total.value = res.data.total;
    varList.value = res.data.data;
  });
};
</script>

<style lang="scss" scoped>
.event-module-container {
  padding: 0;

  .top-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
  }

  .pagination-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 16px;

    .pagination-info {
      color: #606266;
    }
  }

  .search-area {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }
}
</style>
