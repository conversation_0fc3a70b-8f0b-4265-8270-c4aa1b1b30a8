<script lang="ts" setup>
import { ElMessage, ElMessageBox } from "element-plus";
import {
  deleteToolAll,
  deleteToolVersion,
  downloadTool
} from "@/api/toolManagement";
import { Delete, Download } from "@element-plus/icons-vue";
import { computed } from "vue";

const props = defineProps<{
  node: any;
}>();

const emit = defineEmits<{
  (e: "refresh"): void;
}>();

// 判断是否为父节点
const isParentNode = computed(() => props.node.children?.length > 0);

// 处理删除节点
const handleDeleteNode = async (node: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除${node.children?.length > 0 ? ` ${node.name} 工具及所有版本` : ` ${node.name} 工具 ${node.version} 版本`}吗？此操作不可恢复！`,
      "删除确认",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }
    );
    const isParent = node.children?.length > 0;
    if (isParent) {
      const res = (await deleteToolAll({ name: node.name })) as any;
      if (res.code === 0) {
        ElMessage.success("删除成功");
        emit("refresh");
      } else {
        ElMessage.error("删除失败");
      }
      return;
    }
    const res = (await deleteToolVersion({
      name: node.name,
      version: node.version
    })) as any;
    if (res.code === 0) {
      ElMessage.success("删除成功");
      emit("refresh");
    } else {
      ElMessage.error("删除失败");
    }
  } catch (error) {
    // 用户取消，无需提示
  }
};

// 处理下载
const handleDownload = async () => {
  console.log(props.node);
  try {
    const res = await downloadTool({
      tool_id: props.node.id.replace(/^ver-/, "").replace(/^tool-/, "")
    });

    if (res instanceof Blob && res.type === "application/zip") {
      // 正常下载
      const url = window.URL.createObjectURL(res);
      const link = document.createElement("a");
      link.href = url;
      const fileName = props.node.version
        ? `${props.node.name}_${props.node.version}.zip`
        : `${props.node.name}.zip`;
      link.setAttribute("download", fileName);
      document.body.appendChild(link);
      link.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);
    }
  } catch (error: any) {
    // 重点：处理 axios 的 error.response.data
    if (error?.response?.data) {
      const blob = error.response.data as Blob;
      const text = await blob.text();
      try {
        const json = JSON.parse(text);
        if (json.message) {
          console.log(json.message);
        } else {
          console.log(text);
        }
      } catch {
        console.log(text);
      }
    } else {
      console.error("下载异常", error);
    }
  }
};
</script>

<template>
  <template v-if="!isParentNode">
    <div style="display: flex; align-items: center">
      <Perms :value="['tool:r']">
        <el-tooltip
          :content="props.node.internal ? '内部应用不支持下载' : '下载'"
          :hide-after="0"
          placement="bottom"
        >
          <el-button
            :disabled="props.node.internal"
            :icon="Download"
            :style="{ fontSize: '20px', margin: '0px' }"
            :type="props.node.internal ? 'info' : 'primary'"
            link
            @click.stop="handleDownload"
          />
        </el-tooltip>
      </Perms>
      <el-tooltip :hide-after="0" content="删除" placement="bottom">
        <el-button
          :icon="Delete"
          :style="{ fontSize: '20px', margin: '0px' }"
          link
          type="primary"
          @click.stop="handleDeleteNode(props.node)"
        />
      </el-tooltip>
    </div>
  </template>
  <template v-else>
    <el-tooltip :hide-after="0" content="删除" placement="bottom">
      <el-button
        :icon="Delete"
        :style="{ fontSize: '20px', margin: '0px' }"
        link
        type="primary"
        @click.stop="handleDeleteNode(props.node)"
      />
    </el-tooltip>
  </template>
</template>

<style lang="scss" scoped>
.el-dropdown {
  margin-left: 0px;
}

:deep(.el-dropdown-menu__item) {
  outline: none;
}
</style>
