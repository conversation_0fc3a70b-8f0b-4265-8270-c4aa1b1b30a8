<template>
  <el-card>
    <el-form
      :model="oauthConfig"
      class="w-[80%] mx-auto p-[20px] sm:p-[10px]"
      label-width="auto"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="是否启用OAuth2认证：">
            <el-switch
              v-model="oauthConfig.use_oauth2"
              active-value="true"
              inactive-value="false"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="oauthConfig.use_oauth2 === 'true'" :span="24">
          <el-form-item label="获取授权码的URI：" required>
            <el-input
              v-model="oauthConfig.authorize_uri"

            />
          </el-form-item>
        </el-col>
        <el-col v-if="oauthConfig.use_oauth2 === 'true'" :span="6">
          <el-form-item label="客户端ID：" required>
            <el-input
              v-model="oauthConfig.client_id_alias"
              placeholder="默认一般是 client_id"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="oauthConfig.use_oauth2 === 'true'" :span="18">
          <el-input
            v-model="oauthConfig.client_id"
            placeholder="请填写SSO系统分配的值"
          />
        </el-col>
        <el-col v-if="oauthConfig.use_oauth2 === 'true'" :span="6">
          <el-form-item label="客户端密钥：" required>
            <el-input
              v-model="oauthConfig.client_secret_alias"
              default-value="client_secret"
              placeholder="默认一般是 client_secret"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="oauthConfig.use_oauth2 === 'true'" :span="18">
          <el-input
            v-model="oauthConfig.client_secret"
            placeholder="请填写SSO系统分配的值"
          />
        </el-col>
        <el-col v-if="oauthConfig.use_oauth2 === 'true'" :span="24">
          <el-form-item label="获取AccessToken的URI：" required>
            <el-input
              v-model="oauthConfig.token_uri"
              placeholder="例如：https://xxx.com/login/oauth/access_token"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="oauthConfig.use_oauth2 === 'true'" :span="24">
          <el-form-item label="提取Token的JsonPath：" required>
            <el-input
              v-model="oauthConfig.token_path"
              placeholder="例如：$.access_token"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="oauthConfig.use_oauth2 === 'true'" :span="24">
          <el-form-item label="获取用户信息的URI：" required>
            <el-input
              v-model="oauthConfig.user_info_uri"
              placeholder="例如：https://api.xxx.com/user"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="oauthConfig.use_oauth2 === 'true'" :span="24">
          <el-form-item label="用户唯一标识的取值路径：" required>
            <el-input
              v-model="oauthConfig.user_id_path"
              placeholder="从获取用户信息的接口响应中提取值，例如：$.login"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="oauthConfig.use_oauth2 === 'true'" :span="24">
          <el-form-item label="用户姓名的取值路径：">
            <el-input
              v-model="oauthConfig.display_name_path"
              placeholder="从获取用户信息的接口响应中提取值，例如：$.name"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="oauthConfig.use_oauth2 === 'true'" :span="24">
          <el-form-item label="用户邮箱的取值路径：">
            <el-input
              v-model="oauthConfig.email_path"
              placeholder="从获取用户信息的接口响应中提取值，例如：$.email"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="oauthConfig.use_oauth2 === 'true'" :span="24">
          <el-form-item label="用户手机号的取值路径：">
            <el-input
              v-model="oauthConfig.phone_path"
              placeholder="从获取用户信息的接口响应中提取值，例如：$.phone"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="oauthConfig.use_oauth2 === 'true'" :span="6">
          <el-form-item label="请求资源范围：">
            <el-input
              v-model="oauthConfig.scopes_alias"
              default-value="scope"
              placeholder="默认一般是 scope"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="oauthConfig.use_oauth2 === 'true'" :span="18">
          <el-input
            v-model="oauthConfig.scopes"
            placeholder="请填写请求资源范围的值"
          />
        </el-col>
        <el-col v-if="oauthConfig.use_oauth2 === 'true'" :span="24">
          <el-form-item label="是否自动创建新用户：">
            <el-switch
              v-model="oauthConfig.auto_create"
              active-value="true"
              inactive-value="false"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- 保存按钮右对齐 -->
    <Perms :value="['sso:u']">
      <el-button
        class="w-[200px] mx-auto mt-[10px]"
        size="large"
        type="primary"
        @click="updateOauthConfig"
        >保存
      </el-button>
    </Perms>
  </el-card>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { getOauthConfig, saveOauthConfig } from "@/api/system";
import { ElMessage } from "element-plus";

interface OauthConfigData {
  use_oauth2: string;
  client_id: string;
  client_id_alias: string;
  client_secret: string;
  client_secret_alias: string;
  token_uri: string;
  token_path: string;
  user_info_uri: string;
  user_id_path: string;
  email_path: string;
  phone_path: string;
  display_name_path: string;
  scopes_alias: string;
  scopes: string;
  auto_create: string;
  authorize_uri: string;
}

const oauthConfig = ref<OauthConfigData>({
  auto_create: "true",
  use_oauth2: "false",
  client_id: "",
  client_id_alias: "client_id",
  client_secret: "",
  client_secret_alias: "client_secret",
  token_uri: "",
  token_path: "",
  user_info_uri: "",
  user_id_path: "",
  email_path: "",
  phone_path: "",
  display_name_path: "",
  scopes_alias: "scope",
  scopes: "",
  authorize_uri: ""
});

const getOauthCfg = async () => {
  const response = (await getOauthConfig({})) as any;
  // 修改: 合并接口数据与默认值，避免覆盖未返回字段
  oauthConfig.value = { ...oauthConfig.value, ...response.data };
};

const updateOauthConfig = async () => {
  try {
    const res = (await saveOauthConfig(oauthConfig.value)) as any;
    if (res.code === 0) {
      ElMessage.success("保存成功");
    }
  } catch (error) {
    console.error(error);
  }
};

// 页面加载时获取数据
onMounted(() => {
  getOauthCfg();
});
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-start; // 修改对齐方式为左侧对齐
}
</style>
