<template>
  <el-dialog
    v-model="dialogVisible"
    :close-on-click-modal="false"
    :title="props.isEdit ? '事件编辑' : '事件添加'"
    width="50%"
  >
    <template v-if="!userLoading && !roleLoading">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        class="event-form"
        label-width="120px"
      >
        <el-form-item label="事件名称" prop="event_name">
          <el-input
            v-model="formData.event_name"

          />
        </el-form-item>
        <el-form-item label="责任人" prop="owner">
          <el-select-v2
            v-model="userValue"
            :loading="userLoading"
            :options="userTableStore.userOptions"
            :props="userprops"
            filterable
            multiple
            placeholder="选择负责人"
          />
        </el-form-item>
        <el-form-item label="负责角色" prop="owner">
          <el-select-v2
            v-model="roleValue"
            :loading="roleLoading"
            :options="roleStore.roleList"
            :props="roleprops"
            filterable
            multiple
            placeholder="选择负责角色"
          />
        </el-form-item>
        <el-form-item label="事件概要" prop="event_summary">
          <el-input
            v-model="formData.event_summary"
            :rows="4"

            type="textarea"
          />
        </el-form-item>
      </el-form>
    </template>
    <template v-else>
      <el-skeleton :rows="4" animated />
    </template>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from "vue";
import { ElMessage, type FormInstance } from "element-plus";
import { EventAdd, eventUpdate } from "@/api/event";
import { useUserTableStore } from "@/store/modules/usertable";
import { useRoleableStore } from "@/store/modules/roletable";

const userTableStore = useUserTableStore();
const roleStore = useRoleableStore();
const userValue = ref<string[]>([]);
const roleValue = ref<string[]>([]);
const userprops = {
  label: "label",
  value: "value"
};
const roleprops = {
  label: "label",
  value: "value"
};

// 监听userValue变化，更新formData.owner
watch(userValue, newVal => {
  formData.owner.user = [...newVal];
});

// 监听roleValue变化，更新formData.owner
watch(roleValue, newVal => {
  formData.owner.role = [...newVal];
});

///////
const dialogVisible = ref(false);
const formRef = ref<FormInstance>();
const formData = reactive({
  id: "",
  event_name: "",
  owner: {
    role: [],
    user: []
  },
  event_source: "人工",
  event_summary: ""
});

// 监听弹窗关闭
watch(dialogVisible, newVal => {
  if (!newVal) {
    resetForm();
  } else {
    userTableStore.fetchUserList();
    roleStore.fetchRoleList();
  }
});

const rules = {
  event_name: [{ required: true, message: "请输入事件名称", trigger: "blur" }],
  event_process_status: [
    { required: true, message: "请选择事件类型", trigger: "change" }
  ]
};
// 在 script setup 中定义 emit
const emit = defineEmits(["refresh"]);
// 修改提交处理函数
const handleSubmit = async () => {
  if (!formRef.value) return;
  await formRef.value.validate(valid => {
    if (valid) {
      const api = props.isEdit ? eventUpdate : EventAdd;
      api(formData).then((res: any) => {
        if (res.code === 0) {
          ElMessage.success(props.isEdit ? "编辑成功" : "提交成功");
          resetForm();
          dialogVisible.value = false;
          emit("refresh");
        } else {
          ElMessage.error(props.isEdit ? "编辑失败" : "提交失败");
        }
      });
    }
  });
};
// 对外暴露打开弹窗的方法
const open = async (data = null) => {
  userLoading.value = true;
  roleLoading.value = true;
  // 并行加载
  await Promise.all([
    userTableStore.fetchUserList().finally(() => (userLoading.value = false)),
    roleStore.fetchRoleList().finally(() => (roleLoading.value = false))
  ]);
  // 数据加载完再赋值
  if (props.isEdit && data) {
    formData.id = data.id || "";
    formData.event_name = data.event_name || "";
    formData.event_summary = data.event_summary || "";
    formData.event_source = data.event_source || "";

    // 处理 user
    userValue.value = Array.isArray(data.owner?.user)
      ? data.owner.user.map(u => {
          const userObj = Object.values(u)[0] as {
            display_name: string;
            username: string;
          };
          return userObj.username;
        })
      : [];

    // 处理 role
    roleValue.value = Array.isArray(data.owner?.role)
      ? data.owner.role.map(r => Object.values(r)[0]) // 取 "Root"
      : [];

    formData.owner = {
      user: [...userValue.value],
      role: [...roleValue.value]
    };
  } else {
    resetForm();
  }
  dialogVisible.value = true;
};
defineExpose({
  open
});

const resetForm = () => {
  formData.id = "";
  formData.event_name = "";
  formData.owner = {
    role: [],
    user: []
  };
  formData.event_summary = "";
  userValue.value = [];
  roleValue.value = [];
};

const props = defineProps({
  isEdit: { type: Boolean, default: false },
  editData: { type: Object, default: () => ({}) }
});

const userLoading = ref(false);
const roleLoading = ref(false);
</script>

<style lang="scss" scoped>
.event-form {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 20px;
}
</style>
