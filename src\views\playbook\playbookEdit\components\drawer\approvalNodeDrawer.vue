<template>
  <div>
    <el-drawer v-model="isShowDrawer" size="50%">
      <template #header>
        <div class="action-drawer-title">节点信息</div>
      </template>
      <template #default>
        <div class="approval-card">
          <el-form
            ref="approvalFormRef"
            :model="nodeData.properties"
            :rules="approvalFormRules"
            class="approval-form"
            label-width="100px"
          >
            <el-form-item label="节点ID:">
              <el-button link type="primary" @click="$_copyNodeId(nodeData.id)"
                >{{ nodeData.id }}
              </el-button>
            </el-form-item>
            <el-form-item label="节点标题:" prop="node_name">
              <el-input v-model="nodeData.properties.node_name" />
            </el-form-item>
            <el-form-item label="用户:">
              <el-select
                v-model="selectedUserValue"
                multiple
                placeholder="请选择用户"
              >
                <el-option
                  v-for="item in allUserList"
                  :key="item.id"
                  :label="item.display_name"
                  :value="item.id"
                >
                  <div>{{ item.display_name }}({{ item.username }})</div>
                </el-option>
                <template #footer>
                  <el-button
                    v-if="!isAddUser"
                    :icon="Plus"
                    size="small"
                    @click="addArtificialUser()"
                  >
                    添加用户
                  </el-button>
                  <template v-else>
                    <el-input
                      v-model="optionName"
                      class="option-input"
                      placeholder="请添加由${}包裹的节点出参"
                      size="small"
                    />
                    <el-button
                      size="small"
                      type="primary"
                      @click="addArtificialUserConfirm()"
                    >
                      添加
                    </el-button>
                    <el-button size="small" @click="clear">取消</el-button>
                  </template>
                </template>
              </el-select>
            </el-form-item>
            <el-form-item label="角色:">
              <el-select
                v-model="selectedRoleValue"
                multiple
                placeholder="请选择角色"
              >
                <el-option
                  v-for="item in allRoleList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="过期时间:" prop="timeout">
              <div class="approval-form-timeout">
                <el-input-number
                  v-model="nodeData.properties.timeout"
                  :max="nodeData.properties.timeoutType === '60' ? 600 : 10"
                  :min="0"
                />
                <el-select
                  v-model="nodeData.properties.timeoutType"
                  placeholder="请选择单位"
                  prop="timeoutType"
                  style="width: 160px"
                >
                  <el-option
                    v-for="item in timeUnitOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="String(item.value)"
                  />
                </el-select>
              </div>
            </el-form-item>
            <el-form-item label="审批内容:" prop="content">
              <div class="form-item-content">
                <uuid-display-input
                  v-model="nodeData.properties.content"
                  :lf="props.lf"
                />
              </div>
            </el-form-item>
            <el-form-item label="处理类型:">
              <el-radio-group v-model="nodeData.properties.approve_type">
                <el-radio size="large" value="1">
                  <div class="approval-type">
                    <div class="text">或签</div>
                    <el-tooltip
                      :hide-after="0"
                      content="所有用户里面,至少有一个审批通过"
                      effect="dark"
                      placement="top"
                    >
                      <el-icon>
                        <QuestionFilled />
                      </el-icon>
                    </el-tooltip>
                  </div>
                </el-radio>
                <el-radio size="large" value="2">
                  <div class="approval-type">
                    <div class="text">会签</div>
                    <el-tooltip
                      :hide-after="0"
                      content="必须选择的所有用户审批通过"
                      effect="dark"
                      placement="top"
                    >
                      <el-icon>
                        <QuestionFilled />
                      </el-icon>
                    </el-tooltip>
                  </div>
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
        <el-card class="approval-card" shadow="never">
          <el-tabs>
            <el-tab-pane label="自定义参数">
              <el-form ref="approvalFormDataRef" :model="approvalFormData">
                <!-- 顶部 label 行 -->
                <el-row :gutter="20" class="form-label-row">
                  <el-col :span="4">
                    <div class="form-label">参数名</div>
                  </el-col>
                  <el-col :span="4">
                    <div class="form-label">参数描述</div>
                  </el-col>
                  <el-col :span="4">
                    <div class="form-label">值类型</div>
                  </el-col>
                  <el-col :span="4">
                    <div class="form-label">是否必须</div>
                  </el-col>
                  <el-col :span="4">
                    <div class="form-label">默认值</div>
                  </el-col>
                  <el-col :span="4">
                    <div class="form-label">操作</div>
                  </el-col>
                </el-row>
                <el-divider />
                <div v-for="(item, index) in approvalFormData" :key="index">
                  <el-row :gutter="20">
                    <el-col :span="4">
                      <el-form-item prop="name">
                        <el-input
                          v-model="item.name"

                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="4">
                      <el-form-item prop="desc">
                        <el-input
                          v-model="item.desc"

                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="4">
                      <el-form-item prop="type">
                        <el-select
                          v-model="item.type"
                          placeholder="请选择值类型"
                          @change="handleValueChange(item)"
                        >
                          <el-option label="字符串" value="string" />
                          <el-option label="数字" value="number" />
                          <el-option label="密码" value="password" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="4">
                      <el-form-item prop="required">
                        <el-checkbox v-model="item.required" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="4">
                      <el-form-item prop="default">
                        <div v-if="item.type === 'string'">
                          <el-input
                            v-model="item.default"

                          />
                        </div>
                        <div v-if="item.type === 'number'">
                          <el-input
                            v-model="item.default"

                          />
                        </div>
                        <div v-if="item.type === 'password'">
                          <el-input
                            v-model="item.default"

                            type="password"
                          />
                        </div>
                      </el-form-item>
                    </el-col>
                    <el-col :span="4">
                      <el-form-item>
                        <el-button
                          link
                          type="danger"
                          @click="handleDelete(index)"
                        >
                          <Icon height="20" icon="uiw:delete" width="20" />
                        </el-button>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-divider />
                </div>
              </el-form>
            </el-tab-pane>
          </el-tabs>
          <el-button :icon="Plus" class="mt-4" @click="addApprovalParameter()"
            >添加参数
          </el-button>
        </el-card>
      </template>
      <template #footer>
        <div>
          <el-button>取消</el-button>
          <el-button type="primary" @click="nodeEditConfirm(approvalFormRef)"
            >保存
          </el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
import LogicFlow from "@logicflow/core";
import { nextTick, onMounted, reactive, ref } from "vue";
import { Plus, QuestionFilled } from "@element-plus/icons-vue";
import { Icon } from "@iconify/vue";
import { apiuserAccountList, apiUserRoleList } from "@/api/userList";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import { validateUniqueParameterNames } from "@/utils/playbook";
import UuidDisplayInput from "@/components/UuidDisplayInput.vue";

const props = defineProps({
  lf: LogicFlow
});

interface nodeDataFormType {
  node_name: string;
  content: string;
  timeout: number;

  [key: string]: any;
}

interface ParamsType {
  name: string;
  desc: string;
  type: any;
  required: boolean;
  default: string | number;
}

const isShowDrawer = ref(false);
const nodeData = ref<LogicFlow.NodeData>();
const timeUnitOptions = [
  { label: "分钟", value: 60 },
  { label: "小时", value: 3600 }
];
const isAddUser = ref(false);
const optionName = ref("");
const approvalFormData = ref<ParamsType[]>([]);
const allUserList = ref();
const allRoleList = ref();
const approvalFormRef = ref();
const approvalFormDataRef = ref();
const selectedUserValue = ref([]);
const selectedRoleValue = ref([]);

onMounted(() => {
  getAllUserList();
  getAllRoleList();
});

//获取全部用户信息
const getAllUserList = async () => {
  let res: any = await apiuserAccountList({});
  allUserList.value = res.data.users;
  console.log(allUserList.value);
};

//获取全部角色信息
const getAllRoleList = async () => {
  let res: any = await apiUserRoleList({});
  allRoleList.value = res.data.roles;
  console.log(allRoleList.value);
};
const addArtificialUser = () => {
  isAddUser.value = true;
};

//添加用户
const addArtificialUserConfirm = () => {
  if (optionName.value) {
    allUserList.value.push({
      id: Date.now(),
      display_name: optionName.value
    });
    clear();
  }
};

//取消添加并清空数据
const clear = () => {
  optionName.value = "";
  isAddUser.value = false;
};

//添加自定义参数
const addApprovalParameter = () => {
  const newRow = {
    name: "",
    desc: "",
    type: "string",
    required: true,
    default: ""
  };
  approvalFormData.value.push(newRow);
};

//删除自定义参数
const handleDelete = index => {
  approvalFormData.value.splice(index, 1);
};

const handleValueChange = row => {
  // 在这里可以处理值类型变化时的逻辑，比如重置默认值
  row.defaultValue = "";
};

//打开抽屉(判断新旧节点)
const openNodeEditDrawer = (data: LogicFlow.NodeData) => {
  nodeData.value = data;
  if (nodeData.value.properties.node_name) {
    //旧节点修改user和role的数据格式,并赋值给selectedUserValue和selectedRoleValue
    if (nodeData.value.properties.users) {
      selectedUserValue.value = nodeData.value.properties.users.split(",");
    }
    if (nodeData.value.properties.roles) {
      selectedRoleValue.value = nodeData.value.properties.roles.split(",");
    }
    //旧节点将params的值传给approvalFormData
    approvalFormData.value = nodeData.value.properties.params;
  } else {
    //新节点approve_type初始为或签
    nodeData.value.properties.approve_type = "1";
    //新节点添加空的content
    nodeData.value.properties.content = "";
    //新节点清空approvalFormData
    approvalFormData.value = [];
    //新节点清空selectedUserValue和selectedRoleValue
    selectedUserValue.value = [];
    selectedRoleValue.value = [];
  }
  console.log(nodeData.value);
  isShowDrawer.value = true;
  //清空残留的表单校验
  nextTick(() => {
    approvalFormRef.value.clearValidate(); // 只清除清除验证
  });
};

//关闭抽屉
const closeNodeEditDrawer = () => {
  isShowDrawer.value = false;
};

//确认并保存数据到nodeData的properties上
const nodeEditConfirm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  if (!validateUniqueParameterNames(approvalFormData.value)) {
    return;
  }
  await formEl.validate((valid, fields) => {
    if (valid) {
      isShowDrawer.value = false;
      nodeData.value.properties.isOld = true;
      //保存数据
      nodeData.value.properties.params = approvalFormData.value;
      nodeData.value.properties.expire_time =
        nodeData.value.properties.timeout *
        nodeData.value.properties.timeoutType;
      nodeData.value.properties.users = selectedUserValue.value;
      nodeData.value.properties.roles = selectedRoleValue.value;
      //修改users和roles的数据格式
      if (nodeData.value.properties.users) {
        nodeData.value.properties.users =
          nodeData.value.properties.users.join(",");
      }
      if (nodeData.value.properties.roles) {
        nodeData.value.properties.roles =
          nodeData.value.properties.roles.join(",");
      }

      //推送数据到节点的properties上并更新(很重要，没送上来你前面怎么改都没用)
      props.lf.setProperties(nodeData.value.id, nodeData.value.properties!);
      console.log(props.lf.getGraphData());
    } else {
      console.log("error submit!", fields);
    }
  });
};

// 表单校验规则
const approvalFormRules = reactive<FormRules<nodeDataFormType>>({
  node_name: [
    {
      required: true,
      message: "节点标题不能为空",
      trigger: "blur"
    }
  ],
  content: [
    {
      required: true,
      message: "审批内容不能为空",
      trigger: "blur"
    }
  ],
  timeout: [
    {
      required: true,
      message: "请输入过期时间",
      trigger: "blur"
    },
    {
      type: "number",
      min: 0,
      message: "过期时间必须大于等于0",
      trigger: "blur"
    },
    {
      validator: (rule, value, callback) => {
        const timeout = Number(value);
        const timeoutType = Number(nodeData.value.properties.timeoutType);
        if (timeout * timeoutType > 36000) {
          callback(new Error("过期时间最多只能设置10个小时"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  timeoutType: [
    {
      required: true,
      message: "请选择时间单位",
      trigger: "blur"
    },
    {
      validator: (rule, value, callback) => {
        const timeout = Number(nodeData.value.properties.timeout);
        const timeoutType = Number(value);
        if (timeout * timeoutType > 36000) {
          callback(new Error("过期时间最多只能设置10个小时"));
        } else {
          callback();
        }
      },
      trigger: "change"
    }
  ]
});

const approvalFormDataRules = reactive<FormRules<nodeDataFormType>>({
  name: [
    {
      required: true,
      message: "节点标题不能为空",
      trigger: "blur"
    }
  ]
});
// 复制节点ID
const $_copyNodeId = async (id: string) => {
  try {
    // 使用现代Clipboard API替代已废弃的execCommand
    await navigator.clipboard.writeText(`\$\{${id}\}`);
    ElMessage.success("已复制该节点ID");
  } catch (err) {
    console.error("复制失败:", err);
    // 添加失败反馈（可根据项目需求替换为错误提示）
  }
};

//对外暴露方法
defineExpose({
  openNodeEditDrawer,
  closeNodeEditDrawer
});
</script>

<style lang="scss" scoped>
.approval-card {
  margin-bottom: 20px;

  .approval-form {
    padding-right: 50px;

    .approval-form-timeout {
      display: flex;
      gap: 10px;
    }

    .approval-type {
      display: flex;
      align-items: center;

      .text {
        margin-right: 5px;
      }
    }
  }
}

.option-input {
  width: 100%;
  margin-bottom: 8px;
}

.form-item-content {
  width: 100%;
}
</style>
