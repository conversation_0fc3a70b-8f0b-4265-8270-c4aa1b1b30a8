<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { message } from "@/utils/message";
import { editUser, setUserRole } from "@/api/userList";
// import { useRoleableStore } from "@/store/modules/roletable";
import { useColumns } from "../columns";
import { QuestionFilled } from "@element-plus/icons-vue";
import { getRoleNameList } from "@/api/role";
const { fetchUserData } = useColumns();
// const roleStore = useRoleableStore();

const visible = ref(false);
const formRef = ref();
const roleOptions = ref([]);
const filteredRoleOptions = ref([]);
const ipShow = ref(false);
const loading = ref(false);
// 表单数据
const form = ref({
  username: "",
  display_name: "",
  phone: "",
  email: "",
  roles: [],
  permit_ip: ""
});

// 获取角色列表
const fetchRoleList = async () => {
  try {
    const res = (await getRoleNameList({})) as any;
    if (res.code === 0 && res.data && Array.isArray(res.data.roles)) {
      const roles = res.data.roles.map(role => ({
        label: role.name,
        value: role.name
      }));
      roleOptions.value = roles;
      filteredRoleOptions.value = roles;
    } else {
      console.error("获取角色列表失败: 数据格式错误", res);
    }
  } catch (err) {
    console.error("获取角色列表失败:", err);
  }
};

// 打开弹窗
const openDialog = async (row: any) => {
  // 确保角色列表已加载
  if (roleOptions.value.length === 0) {
    await fetchRoleList();
  }
  form.value = {
    username: row.username,
    display_name: row.display_name || "",
    phone: row.phone || "",
    email: row.email || "",
    roles: row.roles || [],
    permit_ip: row.permit_ip || ""
  };
  ipShow.value = !!row.permit_ip;
  visible.value = true;
};

const emit = defineEmits(["refresh"]); // 定义emit事件

// 提交表单
const handleSubmit = async () => {
  try {
    const roleNames = form.value.roles.join(",");
    // 过滤掉空值的函数
    const filterEmptyValues = (obj: Record<string, any>) => {
      const result: Record<string, any> = {};
      Object.entries(obj).forEach(([key, value]) => {
        if (value !== "" && value !== null && value !== undefined) {
          result[key] = value;
        }
      });
      return result;
    };
    // 过滤用户数据中的空值
    const userParams = filterEmptyValues({
      username: form.value.username,
      display_name: form.value.display_name,
      email: form.value.email,
      phone: form.value.phone,
      permit_ip: form.value.permit_ip,
      role_names: roleNames
    });
    // 同时调用两个接口
    const [roleRes, userRes] = (await Promise.all([
      setUserRole({
        username: form.value.username,
        role_names: roleNames
      }),
      editUser(userParams) // 使用过滤后的参数
    ])) as any;
    // 检查两个接口是否都成功
    if (roleRes.code === 0 && userRes.code === 0) {
      message("修改成功", { type: "success" });
      visible.value = false;
      emit("refresh"); // 触发刷新事件
    }
  } catch (error) {
    console.error("提交失败:", error);
  }
};

// 关闭弹窗
const handleClose = () => {
  visible.value = false;
};

// 过滤角色
const filterRoles = (query: string) => {
  if (query) {
    filteredRoleOptions.value = roleOptions.value.filter(option =>
      option.label.toLowerCase().includes(query.toLowerCase())
    );
  } else {
    filteredRoleOptions.value = roleOptions.value;
  }
};

// 组件挂载时获取角色列表
onMounted(() => {
  fetchRoleList();
});

defineExpose({
  openDialog
});
</script>

<template>
  <el-dialog
    v-model="visible"
    :close-on-click-modal="false"
    title="用户编辑"
    width="800px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      label-position="right"
      label-width="auto"
    >
      <el-form-item label="用户名" prop="username">
        <el-input v-model="form.username" disabled  />
      </el-form-item>
      <el-form-item label="姓名" prop="display_name">
        <el-input v-model="form.display_name"  />
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input v-model="form.phone"  />
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="form.email"  />
      </el-form-item>
      <el-form-item label="角色" prop="roles">
        <el-select
          v-model="form.roles"
          :loading="loading"
          filterable
          multiple
          placeholder="请选择角色"
          style="width: 100%"
          @search-change="filterRoles"
        >
          <el-option
            v-for="item in filteredRoleOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <template #label>
          <span class="flex items-center">
            IP白名单:
            <el-tooltip
              :hide-after="0"
              content="只有白名单内的IP地址能够登录该账号"
              placement="top"
            >
              <el-icon class="ml-2 cursor-help"><QuestionFilled /></el-icon>
            </el-tooltip>
          </span>
        </template>
        <el-input
          v-model="form.permit_ip"
          autosize

          type="textarea"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>
