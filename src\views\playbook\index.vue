<template>
  <el-card>
    <div class="playbook">
      <div class="top">
        <el-form
          ref="searchForm"
          :inline="true"
          :model="playbook"
          class="search-form"
        >
          <Perms :value="['workflow:r']">
            <el-form-item label="剧本名称:" prop="name">
              <el-input v-model="playbook.name" />
            </el-form-item>
            <el-form-item label="安全场景:" prop="session">
              <el-input v-model="playbook.scene" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getPlaybookList({})"
                >查询
              </el-button>
              <!--            <el-button @click="searchReset()">重置</el-button>-->
            </el-form-item>
          </Perms>
          <div class="button-group-wrapper">
            <el-form-item>
              <Perms :value="['workflow:c']">
                <el-button
                  type="primary"
                  @click="opencreatePlaybookDialog({ title: '新建剧本' })"
                  >新建
                </el-button>
              </Perms>
              <el-button type="primary" @click="handleImportClick"
                >导入
              </el-button>
              <el-button
                :disabled="isDisabledExport"
                type="primary"
                @click="exportPlaybook()"
                >导出
              </el-button>
              <!-- 隐藏的文件输入框 -->
              <input
                ref="fileInput"
                accept=".json"
                style="display: none"
                type="file"
                @change="handleFileChange"
              />
            </el-form-item>
          </div>
        </el-form>
      </div>
      <div class="tag">
        <div class="text">标签:</div>
        <el-tag
          v-for="tag in dynamicTags"
          :key="tag"
          :disable-transitions="false"
          style="margin-right: 10px; align-items: center"
          @click="handleTagClick(tag)"
        >
          {{ tag }}

          <el-popconfirm
            cancel-button-text="取消"
            confirm-button-text="确认"
            title="确认要删除吗？"
            @confirm="handleClose(tag)"
          >
            <template #reference>
              <el-button link type="primary" @click.stop>
                <IconifyIconOffline
                  height="12"
                  icon="icon-park-outline:close"
                  width="12"
                />
              </el-button>
            </template>
          </el-popconfirm>
        </el-tag>

        <Perms :value="['workflow:u']">
          <el-input
            v-if="inputVisible"
            ref="InputRef"
            v-model="inputValue"
            class="input-tag"
            size="small"
            @blur="handleInputConfirm"
            @keyup.enter="handleInputConfirm"
          />
          <el-button
            v-else
            class="button-new-tag"
            size="small"
            @click="showInput"
          >
            + 添加标签
          </el-button>
        </Perms>
      </div>
      <div class="bottom">
        <el-table
          :data="tableData"
          border
          class="table"
          fit
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column align="center" type="selection" width="50px" />
          <el-table-column
            align="center"
            label="剧本名称"
            prop="name"
            width="320px"
          >
            <template #default="{ row }">
              <el-tooltip :content="row.name" :hide-after="0" placement="top">
                <el-button
                  link
                  type="primary"
                  @click="openLatestPlaybookVersion(row)"
                  >{{ row.name }}
                </el-button>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            class-name="remark-column"
            label="描述"
            prop="remark"
            width="auto"
          >
            <template #default="scope">
              <div
                v-if="scope.row.remark"
                class="remark-content"
                @click="handleImageClick($event, scope.row.remark)"
                v-html="scope.row.remark"
              />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            label="场景"
            prop="scenes"
            width="180px"
          />
          <el-table-column
            align="center"
            label="标签"
            prop="tags"
            width="180px"
          >
            <template #default="scope">
              <div class="tag-container">
                <el-tag
                  v-for="tag in typeof scope.row.tags === 'string'
                    ? scope.row.tags.split(',')
                    : scope.row.tags || []"
                  :key="tag"
                  size="small"
                  style="margin: 2px"
                  type="primary"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            label="创建时间"
            prop="ctime"
            width="180px"
          />
          <el-table-column
            align="center"
            label="更新时间"
            prop="utime"
            width="180px"
          />
          <el-table-column
            align="center"
            label="历史版本数"
            prop="versions"
            width="100px"
          >
            <template #default="{ row }">
              <el-button
                link
                type="primary"
                @click="openPlaybookVersionsDialog(row)"
              >
                {{ row.versions }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" width="220px">
            <template #default="{ row }">
              <div>
                <Perms :value="['workflow:u']">
                  <el-tooltip :hide-after="0" content="编辑" placement="top">
                    <el-button
                      :disabled="hasPermissions(row.id, 'u')"
                      link
                      type="primary"
                      @click="openUpdateDialog(row)"
                    >
                      <IconifyIconOffline
                        height="20"
                        icon="mingcute:edit-line"
                        width="20"
                      />
                    </el-button>
                  </el-tooltip>
                </Perms>
                <Perms :value="['workflow:u']">
                  <el-tooltip
                    :hide-after="0"
                    content="权限分配"
                    placement="top"
                  >
                    <el-button
                      :disabled="hasPermissions(row.id, 'p')"
                      link
                      type="primary"
                      @click="openPermissionDialog(row)"
                    >
                      <IconifyIconOffline
                        height="20"
                        icon="icon-park-outline:permissions"
                        width="20"
                      />
                    </el-button>
                  </el-tooltip>
                </Perms>
                <el-tooltip :hide-after="0" content="克隆" placement="top">
                  <el-button
                    :disabled="hasPermissions(row.id, 'u')"
                    link
                    type="primary"
                    @click="openCloneDialog(row)"
                  >
                    <IconifyIconOffline
                      height="20"
                      icon="cil:clone"
                      width="20"
                    />
                  </el-button>
                </el-tooltip>
                <Perms :value="['workflow:d']">
                  <el-tooltip :hide-after="0" content="删除" placement="top">
                    <span class="ml-3">
                      <el-popconfirm
                        cancel-button-text="取消"
                        confirm-button-text="确认"
                        title="确认要删除吗？"
                        @confirm="deletePlaybook(row)"
                      >
                        <template #reference>
                          <el-button
                            :disabled="hasPermissions(row.id, 'd')"
                            link
                            type="danger"
                          >
                            <IconifyIconOffline
                              height="20"
                              icon="icon-park-outline:delete"
                              width="20"
                            />
                          </el-button>
                        </template>
                      </el-popconfirm>
                    </span>
                  </el-tooltip>
                </Perms>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!-- 图片预览组件 -->
        <el-image-viewer
          v-if="showImageViewer"
          :initial-index="previewIndex"
          :url-list="previewImages"
          show-progress
          @close="showImageViewer = false"
        />

        <!-- 页脚 -->
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :background="true"
          :page-sizes="[15, 50, 100]"
          :total="total"
          class="pagination"
          layout="total, prev, pager, next, sizes"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      <createPlaybookDialog
        ref="createPlaybookDialogRef"
        @apiGet="getPlaybookList({ page: currentPage })"
      />
      <updatePlaybookDialog ref="updatePlaybookDialogRef" />

      <!-- 权限分配dialog -->
      <permissionDialog ref="permissionDialogRef" />
      <clonePlaybookDialog
        ref="clonePlaybookDialogRef"
        @apiGet="getPlaybookList({})"
      />

      <!-- 剧本版本dialog -->
      <el-dialog v-model="isShowPlaybookVersions" top="5vh" width="1300px">
        <template #header>
          <div>【{{ playbookName }}】 历史版本</div>
        </template>
        <div>
          <el-scrollbar height="700px">
            <el-table :data="playbookVersionsTableData" border>
              <el-table-column
                align="center"
                label="序号"
                type="index"
                width="75px"
              />
              <el-table-column
                align="center"
                label="变更说明"
                prop="change_desc"
                show-overflow-tooltip
                width="200px"
              />
              <el-table-column
                align="center"
                label="状态"
                prop="status"
                width="100px"
              >
                <template #default="scope">
                  <el-tag
                    :type="scope.row.status == '1' ? 'success' : 'primary'"
                    >{{ scope.row.status == "1" ? "已发布" : "草稿" }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column
                :formatter="updatorFormatter"
                align="center"
                label="更新人"
                prop="updator"
                width="150px"
              />
              <el-table-column
                align="center"
                label="更新时间"
                prop="utime"
                width="auto"
              />
              <el-table-column
                :formatter="creatorFormatter"
                align="center"
                label="创建人"
                prop="creator"
                width="150px"
              />
              <el-table-column
                align="center"
                label="创建时间"
                prop="ctime"
                width="auto"
              />
              <el-table-column align="center" label="操作" width="auto">
                <template #default="{ row }">
                  <div>
                    <el-tooltip :hide-after="0" content="编辑" placement="top">
                      <el-button
                        :disabled="hasVersionPermission('u')"
                        link
                        type="primary"
                        @click="
                          toDetail(
                            {
                              flow_id: row.flow_id,
                              version_id: row.version_id,
                              status: row.status
                            },
                            'params'
                          )
                        "
                      >
                        <IconifyIconOffline
                          height="20"
                          icon="mingcute:edit-line"
                          width="20"
                        />
                      </el-button>
                    </el-tooltip>

                    <el-tooltip :hide-after="0" content="预览" placement="top">
                      <el-button
                        link
                        type="primary"
                        @click="viewLogicflowData(row)"
                      >
                        <IconifyIconOffline
                          height="20"
                          icon="icon-park-outline:preview-open"
                          width="20"
                        />
                      </el-button>
                    </el-tooltip>
                    <Perms :value="['workflow:d']">
                      <el-tooltip
                        :hide-after="0"
                        content="删除"
                        placement="top"
                      >
                        <span class="ml-3">
                          <el-popconfirm
                            cancel-button-text="取消"
                            confirm-button-text="确认"
                            title="确认要删除吗？"
                            @confirm="deletePlaybookVersion(row)"
                          >
                            <template #reference>
                              <el-button
                                :disabled="hasVersionPermission('d')"
                                link
                                type="danger"
                              >
                                <IconifyIconOffline
                                  height="20"
                                  icon="icon-park-outline:delete"
                                  width="20"
                                />
                              </el-button>
                            </template>
                          </el-popconfirm>
                        </span>
                      </el-tooltip>
                    </Perms>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-scrollbar>
        </div>
      </el-dialog>

      <!-- 流程图预览dialog -->
      <el-dialog
        v-model="isShowViewPlaybook"
        style="width: 1500px; height: 750px"
      >
        <template #header>
          <div>预览流程图</div>
        </template>
        <div class="view-div">
          <div ref="viewLogicflowRef" class="view-logicflow" />
        </div>
      </el-dialog>
    </div>
  </el-card>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, onUnmounted, ref } from "vue";
import { ElMessage, type FormInstance, type InputInstance } from "element-plus";
import createPlaybookDialog from "@/views/playbook/components/playbookDialog/createPlaybookDialog.vue";
import updatePlaybookDialog from "@/views/playbook/components/playbookDialog/updatePlaybookDialog.vue";
import permissionDialog from "@/views/playbook/components/playbookDialog/PermissionDialog.vue";
import clonePlaybookDialog from "@/views/playbook/components/playbookDialog/clonePlaybookDialog.vue";
import { useDetail } from "@/views/playbook/hooks";
import {
  apiCreatePlaybook,
  apiDeletePlaybook,
  apiDeletePlaybookVersion,
  apiGetPlaybookList,
  apiGetPlaybookPermissionsCurrent,
  apiGetPlaybookTagList,
  apiGetPlaybookVersionsList,
  apiPlaybookTagNew,
  apiPlaybookTagRemove
} from "@/api/playbook";
import router from "@/router";
import usePlaybookStore from "@/store/modules/playbook";
import LogicFlow from "@logicflow/core";
import "@logicflow/core/lib/style/index.css";
import "@logicflow/extension/lib/style/index.css";
import { registerCustomElement } from "./playbookEdit/node";
import IconifyIconOffline from "@/components/ReIcon/src/iconifyIconOffline";
//收集剧本信息
const playbook = ref({
  name: "",
  scene: "",
  status: "",
  type: "",
  user: ""
});

const inputValue = ref("");
const dynamicTags = ref([]);
const inputVisible = ref(false);
const InputRef = ref<InputInstance>();
const currentPage = ref(1); //当前页码
const pageSize = ref(15); //一页的数据量
const total = ref(0); //总数据量
const searchForm = ref<FormInstance>();
const createPlaybookDialogRef = ref();
const updatePlaybookDialogRef = ref();
const permissionDialogRef = ref();
const clonePlaybookDialogRef = ref();
const FlowChartVisible = ref(false);
const { toDetail } = useDetail();
const playbookStore = usePlaybookStore();
const isShowPlaybookVersions = ref(false);
ref(false);
const viewLogicflowRef = ref();
const lf = ref<LogicFlow>();
const playbookDetail = ref();
const isShowViewPlaybook = ref(false);
const tableData = ref(); //表格数据
const playbookVersionsTableData = ref();
const selectedPlaybookList = ref([]);
const isDisabledExport = ref(true);
const fileInput = ref();
const importedData = ref(null);
// 响应式状态
const showImageViewer = ref(false);
const previewImages = ref([]);
const previewIndex = ref(0);
const playbookName = ref();
const selectTagValue = ref();
const permissions = ref();

//组件挂载时请求数据
onMounted(() => {
  getPlaybookTagList();
  getPlaybookList({});
});

//组件销毁时触发
onUnmounted(() => {
  FlowChartVisible.value = false;
});

// const playbookPermissions = async () => {
//   let res: any = await apiGetPlaybookPermissionsCurrent({});
//   console.log(res);
// };

//初始数据
let data = {
  nodes: [
    {
      type: "start-node",
      x: 100,
      y: 100
    },
    {
      type: "end-node",
      x: 1000,
      y: 100
    }
  ]
};

// 提取 Base64 图片数据
const extractBase64Images = html => {
  if (!html) return [];

  // 正则匹配所有 Base64 图片
  const base64Regex = /<img[^>]+src="(data:image\/[^;]+;base64,[^"]+)"/g;
  const matches = [];
  let match;

  while ((match = base64Regex.exec(html)) !== null) {
    matches.push(match[1]);
  }

  return matches;
};

// 处理图片点击事件
const handleImageClick = (event, html) => {
  if (event.target.tagName === "IMG") {
    const images = extractBase64Images(html);
    const clickedSrc = event.target.src;

    if (images.length > 0) {
      previewImages.value = images;
      previewIndex.value = images.indexOf(clickedSrc);
      showImageViewer.value = true;
    }
  }
};

//获取标签列表（需要剧本查看权限）
const getPlaybookTagList = async () => {
  let res: any = await apiGetPlaybookTagList({});
  dynamicTags.value = res.data;
};

//添加标签
const handleInputConfirm = async () => {
  if (inputValue.value) {
    let res: any = await apiPlaybookTagNew({ name: inputValue.value });
    if (res.code == 0) {
      dynamicTags.value.push(inputValue.value);
      ElMessage.success("新建标签成功");
    } else {
      ElMessage.error("新建标签失败");
    }
  }
  inputVisible.value = false;
  inputValue.value = "";
};

//从dynamicTags删除标签
const handleClose = async (tag: string) => {
  dynamicTags.value.splice(dynamicTags.value.indexOf(tag), 1);
  let res: any = await apiPlaybookTagRemove({ name: tag });
  if (res.code == 0) {
    ElMessage.success("删除标签成功");
  } else {
    ElMessage.error("删除标签失败");
  }
};

//点击标签进行筛选
const handleTagClick = async (tag: string) => {
  selectTagValue.value = tag;
  getPlaybookList({ tag: selectTagValue.value });
};

//显示输入框
const showInput = () => {
  inputVisible.value = true;
  nextTick(() => {
    InputRef.value!.input!.focus();
  });
};

//页面展示数据量改变时触发
const handleSizeChange = (val: number) => {
  getPlaybookList({ size: val });
};

//页脚改变时触发
const handleCurrentChange = (val: number) => {
  getPlaybookList({ page: val });
};

//请求剧本列表数据
const getPlaybookList = async ({ page = 1, size = 15, tag = "" }) => {
  currentPage.value = page;
  pageSize.value = size;
  selectTagValue.value = tag;
  let res1: any = await apiGetPlaybookList({
    page: currentPage.value,
    size: pageSize.value,
    keyword: playbook.value.name,
    tag: [selectTagValue.value]
  });
  tableData.value = res1.data.data;
  total.value = res1.data.total;

  //赋值权限
  let res2: any = await apiGetPlaybookPermissionsCurrent({});
  if (res2.code == 0) {
    playbookStore.playbookPermissions = Object.fromEntries(
      res2.data.map(item => [item.id, item.permissions])
    );
  }
  localStorage.setItem(
    "playbookPermissions",
    JSON.stringify(playbookStore.playbookPermissions)
  );
  // 将最新获取的数据存储到sessionStorage中
  sessionStorage.setItem("playbookList", JSON.stringify(res2.data.data));
};

//判断是否有权限
const hasPermissions = (id: string, permission: string) => {
  // 如果没有权限返回true（禁用按钮），有权限返回false（启用按钮）
  return !playbookStore.playbookPermissions[id]?.includes(permission);
};

// 历史版本权限检查辅助函数
const hasVersionPermission = (permission: string) => {
  const playbookId = (playbookStore.playbookData as any)?.id;
  if (!playbookId) {
    return true; // 禁用按钮
  }
  return hasPermissions(playbookId, permission);
};

//打开新建剧本dialog
const opencreatePlaybookDialog = (data: any) => {
  if (createPlaybookDialogRef.value) {
    createPlaybookDialogRef.value.openDialog();
  }
};
//打开编辑剧本dialog
const openUpdateDialog = row => {
  if (updatePlaybookDialogRef.value) {
    updatePlaybookDialogRef.value.openDialog(row);
  }
};
//打开权限分配dialog
const openPermissionDialog = row => {
  if (permissionDialogRef.value) {
    permissionDialogRef.value.openPermissionDialog(row);
  }
};
//打开克隆剧本dialog
const openCloneDialog = row => {
  if (clonePlaybookDialogRef.value) {
    clonePlaybookDialogRef.value.openDialog(row);
  }
};

// 打开场景
const addScene = () => {
  router.push({
    name: "playbookSceneView"
  });
};

//删除剧本
const deletePlaybook = async row => {
  await apiDeletePlaybook({ playbook_id: row.id });
  //删除剧本后，重新请求剧本列表
  getPlaybookList({
    page: tableData.value.length > 1 ? currentPage.value : currentPage.value - 1
  });
};

//点击剧本名称，自动跳转到该剧本的最新版本
const openLatestPlaybookVersion = async row => {
  let res: any = await apiGetPlaybookVersionsList({ playbook_id: row.id });
  toDetail(
    {
      flow_id: res.data[0].flow_id,
      version_id: res.data[0].version_id,
      status: res.data[0].status
    },
    "params"
  );
  //跳转时保存当前剧本的基本信息
  playbookStore.playbookData = row;
};

//清空搜索栏并刷新
const searchReset = () => {
  window.location.reload();
};

// //对表格数据remark列进行处理
// const remarkFormatter = row => {
//   // 获取当前行的 remark 数据
//   let remark = row.remark || "";
//   // 移除空的或只包含 <br> 的 <p> 标签
//   remark = remark.replace(/<p[^>]*>(\s|&nbsp;|<br[^>]*>)+<\/p>/g, "").trim();
//   // 移除所有其他 HTML 标签
//   remark = remark.replace(/<[^>]*>/g, "").trim();
//   // 过滤多余的空格和换行
//   remark = remark.replace(/[\s\u00A0]+/g, " ").trim();
//   return remark;
// };

//对表格数据updator列进行处理
const updatorFormatter = row => {
  return `${row.updator.display_name}(${row.updator.username})`;
};

//对表格creator列进行处理
const creatorFormatter = row => {
  return `${row.creator.display_name}(${row.creator.username})`;
};

//打开剧本版本dialog
const openPlaybookVersionsDialog = async row => {
  //请求剧本版本列表
  let res: any = await apiGetPlaybookVersionsList({ playbook_id: row.id });
  playbookVersionsTableData.value = res.data;
  isShowPlaybookVersions.value = true;
  //跳转时保存当前剧本的基本信息
  playbookStore.playbookData = row;
  playbookName.value = row.name;
};

//删除剧本版本
const deletePlaybookVersion = async row => {
  let res1: any = await apiDeletePlaybookVersion({ flow_id: row.version_id });
  if (res1.code == 0) {
    ElMessage({
      message: "删除成功",
      type: "success"
    });
  } else {
    ElMessage.error("删除失败");
  }
  //删除剧本版本后，重新请求剧本版本列表
  let res2: any = await apiGetPlaybookVersionsList({
    playbook_id: row.flow_id
  });
  playbookVersionsTableData.value = res2.data;
};

//打开预览流程图dialog并渲染流程图数据
const viewLogicflowData = row => {
  initLogicFlow(row);
  isShowViewPlaybook.value = true;
};
//渲染流程图数据
const initLogicFlow = async row => {
  //获取剧本的详细信息
  let res: any = await apiGetPlaybookVersionsList({
    playbook_id: row.flow_id
  });
  playbookDetail.value = res.data.find(
    item => item.version_id === row.version_id
  );
  lf.value = new LogicFlow({
    container: viewLogicflowRef.value,
    width: 1450,
    height: 650,
    grid: {
      visible: false,
      size: 15
    },
    isSilentMode: true //静默模式
  });
  registerCustomElement(lf.value); //注册自定义节点和边
  lf.value.setDefaultEdgeType("vue-edge"); //边的类型
  //如果是已有剧本，则渲染已有剧本的流程图信息，否则默认为初始数据
  if (Object.entries(playbookDetail.value.flow_json).length > 0) {
    playbookDetail.value.flow_json.nodes.forEach(node => {
      // //基于锚点的位置更新边的路径
      // lf.value.getNodeModelById(node.id).updateField();
      //在渲染流程图之前，先把初始化isWebSocket和scale的值
      if (node.properties && node.properties.isWebSocket !== undefined) {
        node.properties.isWebSocket = false;
        node.properties.scale = 1;
      }
    });
    lf.value.render(playbookDetail.value.flow_json);
  } else {
    lf.value.render(data);
  }
  lf.value.translateCenter(); // 将图形移动到画布中央
};

//保存用户选中的剧本列表
const handleSelectionChange = val => {
  selectedPlaybookList.value = val;
  isDisabledExport.value = selectedPlaybookList.value.length <= 0;
};

//导出剧本
const exportPlaybook = async () => {
  // 创建一个 Promise 数组，用于等待所有异步操作完成
  const promises = selectedPlaybookList.value.map(async item => {
    // 请求剧本版本列表
    let res: any = await apiGetPlaybookVersionsList({ playbook_id: item.id });
    item.data = res.data.map(dataItem => dataItem.flow_json);
    return item; // 返回更新后的项
  });

  // 等待所有异步操作完成
  await Promise.all(promises);

  // 将数据转换为 JSON 字符串
  const jsonData = JSON.stringify(selectedPlaybookList.value, null, 2);

  // 创建一个 Blob 对象，类型设置为 application/json
  const blob = new Blob([jsonData], { type: "application/json" });

  // 创建一个临时的 a 标签用于下载
  const link = document.createElement("a");
  link.href = URL.createObjectURL(blob);

  // 设置下载的文件名
  link.download = `剧本导出数据.json`;

  // 触发点击事件开始下载
  link.click();

  // 释放 URL 对象
  URL.revokeObjectURL(link.href);
};

// 触发导入文件选择
const handleImportClick = () => {
  fileInput.value.click();
};

// 处理文件选择
const handleFileChange = event => {
  const file = event.target.files[0];
  if (!file) {
    return;
  }

  const fileReader = new FileReader();
  fileReader.onload = e => {
    try {
      // 使用类型断言将 result 转换为 string
      const content = e.target.result as string;
      importedData.value = JSON.parse(content);

      // 在这里根据导入的数据执行你需要的操作
      processImportedData(importedData.value);
    } catch (error) {
      console.error("解析 JSON 文件出错:", error);
      ElMessage.error("解析 JSON 文件出错，请确保文件格式正确");
    }
  };
  fileReader.onerror = () => {
    console.error("读取文件出错");
    ElMessage.error("读取文件出错");
  };
  fileReader.readAsText(file);
};

// 处理解析后的数据
const processImportedData = async data => {
  // 将函数标记为 async
  // 使用 Promise.all 等待所有异步操作完成
  await Promise.all(
    data.map(async item => {
      // 使用 map 替代 forEach
      return await apiCreatePlaybook({
        name: `${item.name}_${Date.now()}`,
        remark: item.remark,
        tags: item.tags,
        scenes: item.scenes,
        flow_json_list: item.data
      }); // 确保返回值
    })
  );
  ElMessage.success("导入完成");
  // 刷新剧本表格数据
  getPlaybookList({});
};
</script>

<style lang="scss" scoped>
.remark-content img {
  max-width: 75px !important;
  max-height: 75px !important;
  width: auto;
  height: auto;
  object-fit: contain;
  cursor: pointer;
}

/* 为描述列添加换行样式 */
.el-table .remark-column .cell {
  white-space: normal !important;
  word-break: break-word;
  line-height: 1.5;
  text-align: left; /* 确保内容左对齐 */
}

.remark-content {
  text-align: left; /* 内容左对齐 */
}

.playbook {
  box-sizing: border-box;
  background-color: #fff;

  .top {
    .search-form {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-items: center;

      :deep(.el-form-item) {
        margin-bottom: 0;
      }
    }

    .button-group-wrapper {
      margin-left: auto;
    }
  }

  .tag {
    display: flex;
    align-items: center;
    margin: 15px 0;
    cursor: pointer;

    .text {
      padding-right: 10px;
      font-size: 13px;
    }

    .input-tag {
      width: 100px;
    }

    .button-new-tag {
      width: 75px;
      margin: 5px 10px 5px 0; // 增加标签的上下间距
    }

    :deep(.el-tag) {
      margin: 5px 0; // 增加标签的上下间距
    }
  }

  .bottom {
    width: 100%;

    .table {
      display: flex;
      width: 100%;
      margin: 10px 0;
      //overflow-x: auto;
    }

    .pagination {
      display: flex;
      justify-content: right;
    }
  }
}

.view-div {
  width: 100%;
  height: 100%;

  .view-logicflow {
    width: 100%;
    height: 75vh;
  }
}

.playbook-versions-name {
  display: flex;
  padding-bottom: 10px;

  .text {
    margin-left: 5px;
  }
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>

<style lang="scss">
/* 全局样式，不使用 scoped */
.remark-content img {
  max-width: 75px !important;
  max-height: 75px !important;
  width: auto;
  height: auto;
  object-fit: contain;
  cursor: pointer;
}
</style>
