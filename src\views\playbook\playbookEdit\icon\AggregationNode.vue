<template>
  <div class="vue-wrap">
    <div class="vue-html">
      <div class="vue-html-title">
        <div class="vue-html-title-left">
          <div class="icon" @click.stop="$_copyNodeId()">
            <svg
              class="icon"
              height="20"
              p-id="3795"
              t="1748480979251"
              version="1.1"
              viewBox="0 0 1024 1024"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M512 336c47 0 91.2 18.3 124.5 51.5C669.7 420.8 688 465 688 512s-18.3 91.2-51.5 124.5C603.2 669.7 559 688 512 688s-91.2-18.3-124.5-51.5C354.3 603.2 336 559 336 512s18.3-91.2 51.5-124.5C420.8 354.3 465 336 512 336z m0-64c-132.5 0-240 107.5-240 240s107.5 240 240 240 240-107.5 240-240-107.5-240-240-240zM316.4 717.4l-54.8 204.4c-1.6 5.9-9 7.9-13.4 3.6l-40.9-40.9c-6.2-6.2-16.4-6.2-22.6 0L51.1 1018.2c-3.1 3.1-8.2 3.1-11.3 0l-34-33.9c-3.1-3.1-3.1-8.2 0-11.3l133.7-133.7c6.2-6.2 6.2-16.4 0-22.6l-40.9-40.9c-4.4-4.4-2.4-11.8 3.6-13.4l204.4-54.8c6-1.6 11.4 3.8 9.8 9.8zM1018.2 984.3l-33.9 33.9c-3.1 3.1-8.2 3.1-11.3 0L839.2 884.5c-6.2-6.2-16.4-6.2-22.6 0l-40.9 40.9c-4.4 4.4-11.8 2.4-13.4-3.6l-54.8-204.4c-1.6-5.9 3.8-11.4 9.8-9.8l204.4 54.8c5.9 1.6 7.9 9 3.6 13.4l-40.9 40.9c-6.2 6.2-6.2 16.4 0 22.6L1018.1 973c3.2 3.1 3.2 8.1 0.1 11.3zM98.6 248.1l40.9-40.9c6.2-6.2 6.2-16.4 0-22.6L5.8 50.9c-3.1-3.1-3.1-8.2 0-11.3L39.7 5.7c3.1-3.1 8.2-3.1 11.3 0l133.7 133.7c6.2 6.2 16.4 6.2 22.6 0l40.9-40.9c4.4-4.4 11.8-2.4 13.4 3.6l54.8 204.4c1.6 5.9-3.8 11.4-9.8 9.8l-204.4-54.7c-5.9-1.7-7.9-9.1-3.6-13.5zM884.5 207.3l40.9 40.9c4.4 4.4 2.4 11.8-3.6 13.4l-204.4 54.7c-5.9 1.6-11.4-3.8-9.8-9.8l54.8-204.4c1.6-5.9 9-7.9 13.4-3.6l40.9 40.9c6.2 6.2 16.4 6.2 22.6 0L972.9 5.7c3.1-3.1 8.2-3.1 11.3 0l33.9 33.9c3.1 3.1 3.1 8.2 0 11.3L884.5 184.6c-6.3 6.3-6.3 16.4 0 22.7z"
                fill="#2c2c2c"
                p-id="3796"
              />
              <path
                d="M512 480c17.6 0 32 14.4 32 32s-14.4 32-32 32-32-14.4-32-32 14.4-32 32-32z m0-64c-53 0-96 43-96 96s43 96 96 96 96-43 96-96-43-96-96-96z"
                fill="#2c2c2c"
                p-id="3797"
              />
            </svg>
          </div>

          <div class="text">聚合</div>
        </div>
        <div class="vue-html-title-right">
          <div class="copy" @click.stop="$_copyNode()">
            <svg
              class="icon"
              height="20"
              p-id="2839"
              t="1748240616953"
              version="1.1"
              viewBox="0 0 1024 1024"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M720 192h-544A80.096 80.096 0 0 0 96 272v608C96 924.128 131.904 960 176 960h544c44.128 0 80-35.872 80-80v-608C800 227.904 764.128 192 720 192z m16 688c0 8.8-7.2 16-16 16h-544a16 16 0 0 1-16-16v-608a16 16 0 0 1 16-16h544a16 16 0 0 1 16 16v608z"
                fill="#2c2c2c"
                p-id="2840"
              />
              <path
                d="M848 64h-544a32 32 0 0 0 0 64h544a16 16 0 0 1 16 16v608a32 32 0 1 0 64 0v-608C928 99.904 892.128 64 848 64z"
                fill="#2c2c2c"
                p-id="2841"
              />
              <path
                d="M608 360H288a32 32 0 0 0 0 64h320a32 32 0 1 0 0-64zM608 520H288a32 32 0 1 0 0 64h320a32 32 0 1 0 0-64zM480 678.656H288a32 32 0 1 0 0 64h192a32 32 0 1 0 0-64z"
                fill="#2c2c2c"
                p-id="2842"
              />
            </svg>
          </div>
          <el-popconfirm
            v-if="!props.graphModel.editConfigModel.isSilentMode"
            :hide-after="0"
            cancel-button-text="取消"
            confirm-button-text="确认"
            title="确认删除该节点吗？"
            width="auto"
            @confirm.stop="$_deleteNode()"
          >
            <template #reference>
              <div class="delete" @click.stop>
                <svg
                  class="icon"
                  height="20"
                  p-id="3915"
                  t="1748240739833"
                  version="1.1"
                  viewBox="0 0 1024 1024"
                  width="20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M106.666667 213.333333h810.666666v42.666667H106.666667z"
                    fill="#d81e06"
                    p-id="3916"
                  />
                  <path
                    d="M640 128v42.666667h42.666667V128c0-23.573333-19.093333-42.666667-42.538667-42.666667H383.872A42.496 42.496 0 0 0 341.333333 128v42.666667h42.666667V128h256z"
                    fill="#d81e06"
                    p-id="3917"
                  />
                  <path
                    d="M213.333333 896V256H170.666667v639.957333C170.666667 919.552 189.653333 938.666667 213.376 938.666667h597.248C834.218667 938.666667 853.333333 919.68 853.333333 895.957333V256h-42.666666v640H213.333333z"
                    fill="#d81e06"
                    p-id="3918"
                  />
                  <path
                    d="M320 341.333333h42.666667v384h-42.666667zM490.666667 341.333333h42.666666v384h-42.666666zM661.333333 341.333333h42.666667v384h-42.666667z"
                    fill="#d81e06"
                    p-id="3919"
                  />
                </svg>
              </div>
            </template>
          </el-popconfirm>
          <div v-else class="delete" @click.stop>
            <svg
              class="icon"
              height="20"
              p-id="3915"
              t="1748240739833"
              version="1.1"
              viewBox="0 0 1024 1024"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M106.666667 213.333333h810.666666v42.666667H106.666667z"
                fill="#d81e06"
                p-id="3916"
              />
              <path
                d="M640 128v42.666667h42.666667V128c0-23.573333-19.093333-42.666667-42.538667-42.666667H383.872A42.496 42.496 0 0 0 341.333333 128v42.666667h42.666667V128h256z"
                fill="#d81e06"
                p-id="3917"
              />
              <path
                d="M213.333333 896V256H170.666667v639.957333C170.666667 919.552 189.653333 938.666667 213.376 938.666667h597.248C834.218667 938.666667 853.333333 919.68 853.333333 895.957333V256h-42.666666v640H213.333333z"
                fill="#d81e06"
                p-id="3918"
              />
              <path
                d="M320 341.333333h42.666667v384h-42.666667zM490.666667 341.333333h42.666666v384h-42.666666zM661.333333 341.333333h42.666667v384h-42.666667z"
                fill="#d81e06"
                p-id="3919"
              />
            </svg>
          </div>
        </div>
      </div>
      <div class="vue-html-container">
        <el-form>
          <el-form-item>
            <div class="container-text">
              {{ props.properties.node_name || "未命名节点" }}
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>

  <div v-if="props.properties.isWebSocket" class="webSocket">
    <div class="webSocket-result">
      <div class="webSocket-result-true" @click.stop="openDialog()">
        <div class="webSocket-result-left">
          <el-icon class="webSocket-result-icon">
            <CircleCheckFilled />
          </el-icon>
          <div class="webSocket-result-text">运行成功</div>
          <div class="webSocket-result-time">1s</div>
        </div>
        <div
          class="webSocket-result-right"
          @click.stop="$_closePlaybookStart()"
        >
          <el-icon>
            <Close />
          </el-icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElIcon,
  ElPopconfirm
} from "element-plus";
import usePlaybookStore from "@/store/modules/playbook";
import { CircleCheckFilled, Close } from "@element-plus/icons-vue";

const props = defineProps({
  id: {
    type: String
  },
  properties: {
    type: Object,
    required: true
  },
  model: {
    type: Object
  },
  graphModel: {
    type: Object
  },
  text: {
    type: String
  },
  onBtnCopyClick: Function,
  onBtnDelClick: Function,
  onBtnWebSocket: Function,
  onBtnCloseClick: Function
});

const playbookStore = usePlaybookStore();

//打开webSocket通信结果dialog
const openDialog = () => {
  props.onBtnWebSocket();
};

//复制节点
const $_copyNode = () => {
  props.onBtnCopyClick();
  console.log(props.graphModel.nodes);
  console.log(props.model.properties);
};

//删除节点
const $_deleteNode = () => {
  props.onBtnDelClick();
  console.log(props.graphModel.nodes);
  console.log(props.model.properties);
};

//清空剧本执行结果
const $_closePlaybookStart = () => {
  props.onBtnCloseClick();
};

// 复制节点ID
const $_copyNodeId = async () => {
  try {
    // 使用现代Clipboard API替代已废弃的execCommand
    await navigator.clipboard.writeText(`\$\{${props.id}\}`);
  } catch (err) {
    console.error("复制失败:", err);
    // 添加失败反馈（可根据项目需求替换为错误提示）
  }
};
</script>

<style lang="scss" scoped>
.vue-wrap {
  display: flex;
  align-items: center;
  justify-content: center;

  .vue-html {
    width: 190px;
    height: 90px;
    overflow: hidden;
    border: 1px solid rgba(223, 225, 229, 0.8);
    border-radius: 6px;
    box-shadow:
      0 4px 8px rgba(0, 0, 0, 0.1),
      0 2px 4px rgba(0, 0, 0, 0.1);

    .vue-html-title {
      display: flex;
      justify-content: space-between;
      height: 30px;
      color: black;
      padding: 3px 5px;
      background: linear-gradient(#f2f2ff 0%, rgba(252, 252, 255, 1) 100%);

      .vue-html-title-left {
        display: flex;

        .icon {
          cursor: pointer;
        }

        .text {
          padding-left: 5px;
          font-size: 13px;
        }
      }

      .vue-html-title-right {
        display: flex;

        .copy {
          cursor: pointer;
        }

        .delete {
          cursor: pointer;
          margin-left: 10px;
        }
      }
    }

    .vue-html-container {
      display: flex;
      justify-content: center;
      height: 70px;
      background: white;
      padding-top: 10px;

      .container-text {
        font-size: 13px;
      }
    }
  }
}

.webSocket {
  width: 200px;
  height: auto;
  color: white;
  margin-top: 5px;

  .webSocket-result {
    display: flex;
    justify-content: center;

    .webSocket-result-true {
      border: 1px solid rgba(223, 225, 229, 0.8);
      border-radius: 6px;
      /* 核心阴影 */
      box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.1),
        0 2px 4px rgba(0, 0, 0, 0.1);
      width: 190px;
      height: 30px;
      background: rgba(241, 248, 244, 0.7);
      color: black;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;

      .webSocket-result-left {
        display: flex;
        align-items: center;
        padding-left: 5px;

        .webSocket-result-icon {
          width: 20px;
          height: 20px;
          background: linear-gradient(
            145deg,
            var(--primary-green) 0%,
            var(--success-dark) 100%
          );
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #009624;
          font-size: 24px;
          box-shadow: 0 4px 8px rgba(0, 200, 83, 0.3);
          animation: pulse 2s infinite ease-in-out;
        }

        .webSocket-result-text {
          padding-left: 5px;
          font-size: 13px;
          font-weight: 500;
          color: #202124;
          letter-spacing: -0.3px;
        }

        .webSocket-result-time {
          padding-left: 5px;
          font-size: 13px;
          color: #00c853;
          font-weight: 500;
        }
      }

      .webSocket-result-right {
        padding-right: 5px;
        display: flex;
        align-items: center;
      }
    }

    .el-button {
      padding: 0px 0px;
      margin: 0px 0px;
      border: 1px solid rgba(223, 225, 229, 0.8);
      box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.08),
        0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .el-button:hover {
      box-shadow: 0 6px 16px rgba(0, 200, 83, 0.12);
      transform: translateY(-2px);
    }
  }
}
</style>
