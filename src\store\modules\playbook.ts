import { ElMessage } from "element-plus";
import { defineStore } from "pinia";

const usePlaybookStore = defineStore("", {
  state: () => ({
    playbookPermissions: {},
    sseController: null as AbortController | null, // sse控制器
    isPlaybookStarting: false, //是否正在执行剧本
    flowInput: {}, //用户输入(用于剧本SSE通信)
    startFlow_input: [], //在执行剧本时，用户填写的开始节点参数
    startNodeParams: [],
    flow_id: "", //剧本ID
    version_id: "", //版本ID
    status: "", //剧本状态
    allToolList: [], //全部应用列表
    playbookList: [], //剧本列表
    playbookName: "", //当前剧本名称
    startNodeId: "", //开始节点ID
    flow_inputName: [], //用户输入(用于收集信息)
    flow_input: {}, //用户输入(用于webSocket)
    webSocketMsg: [], //webSocket通信信息
    isShowFlowInputDialog: false, //显示/隐藏用户输入dialog
    flowId: [],
    isFlowChanged: false,
    isPlaybookStart: false,
    flow_json: {}, //流程图数据
    selectedScene: "", //当前选中的场景
    playbookDialog: false,
    playbookData: {}, //流程图当前剧本的基本信息
    SSEResult: []
  }),
  getters: {},
  actions: {
    addFlowInputName(data) {
      this.flow_inputName.push(data);
      this.flow_input = this.flow_inputName.reduce((acc, curr) => {
        return Object.assign(acc, curr);
      }, {});
    },
    addFlowId(data) {
      this.flowId.push(data);
    },
    addAllToolList(data) {
      this.allToolList = [...this.allToolList, ...data];
    },
    addPlaybookList(data) {
      this.playbookList.push(data);
    },
    setController(controller: AbortController) {
      this.sseController = controller;
    },
    abortConnection(isTimeout = false) {
      if (this.sseController) {
        this.sseController.abort();
        this.sseController = null; // 清理引用
        this.isPlaybookStarting = false; // 确保执行状态被重置
        if (isTimeout) {
          ElMessage.error("执行超时已取消");
        } else {
          // ElMessage.warning("剧本执行已手动中止。");
        }
        console.log("已取消sse连接");
      }
    },
    // 处理超时情况
    handleTimeout() {
      console.log("handleTimeout被调用，当前状态:", this.isPlaybookStarting);
      this.abortConnection(true); // 传入true表示是超时中止
      console.log("SSE连接超时，已清理状态，新状态:", this.isPlaybookStarting);
    }
  }
});

export default usePlaybookStore;
