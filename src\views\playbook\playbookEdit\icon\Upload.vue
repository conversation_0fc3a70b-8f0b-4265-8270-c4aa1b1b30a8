<template>
  <div>
    <svg
      :height="size"
      :width="size"
      class="icon"
      p-id="9800"
      t="1744800726925"
      version="1.1"
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M864 352H640v64h160v448H224V416h160v-64H160v576h704V352z"
        fill="#333333"
        p-id="9801"
      />
      <path
        d="M544 672V219.84l64.64 64 45.6-45.28-140.96-141.12-141.28 141.12 44.64 45.28 63.36-64V672h64z"
        fill="#333333"
        p-id="9802"
      />
    </svg>
  </div>
</template>

<script lang="ts" setup>
defineProps({
  size: {
    type: String,
    default: "25"
  }
});
</script>
