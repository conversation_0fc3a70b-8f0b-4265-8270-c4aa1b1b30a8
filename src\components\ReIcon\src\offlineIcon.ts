// 这里存放本地图标，在 src/layout/index.vue 文件中加载，避免在首启动加载
import { addIcon } from "@iconify/vue/dist/offline";

// 本地菜单图标，后端在路由的 icon 中返回对应的图标字符串并且前端在此处使用 addIcon 添加即可渲染菜单图标
// @iconify-icons/ep
import Lollipop from "@iconify-icons/ep/lollipop";
import HomeFilled from "@iconify-icons/ep/home-filled";
// @iconify-icons/ri
import Search from "@iconify-icons/ri/search-line";
import InformationLine from "@iconify-icons/ri/information-line";

//手动注册
const tablerAi = {
  body: '<path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16v-6a2 2 0 1 1 4 0v6m-4-3h4m4-5v8"/>',
  width: 24,
  height: 24
};
const action = {
  body: '<path fill="currentColor" d="M21 2H11c-5 0-9 4-9 9v10c0 5 4 9 9 9h10c5 0 9-4 9-9V11c0-5-4-9-9-9m7 19c0 3.9-3.1 7-7 7H11c-3.9 0-7-3.1-7-7V11c0-3.9 3.1-7 7-7h10c3.9 0 7 3.1 7 7zm-4-10h-6c-1.1 0-2 .9-2 2v6c0 1.1.9 2 2 2h6c1.1 0 2-.9 2-2v-6c0-1.1-.9-2-2-2m-6 8v-6h6v6zm-7-8l5 5l-5 5l-1.5-1.4l2.7-2.6H6v-2h6.2l-2.7-2.6z"/>',
  width: 32,
  height: 32
};
const playbook = {
  body: '<path fill="currentColor" d="M6.707 8.707a1 1 0 0 1-1.32.083l-.094-.083l-3-3a1 1 0 0 1-.083-1.32l.083-.094l3-3a1 1 0 0 1 1.32-.083l.094.083L9.415 4h9.54l.22.005a5.045 5.045 0 0 1 0 10.08l-.22.004l-2.629-.001l-2.619 2.62a1 1 0 0 1-1.32.082l-.094-.083l-2.62-2.619H6.956l-.174.006a2.956 2.956 0 0 0 0 5.901l.174.005H19v-2c0-.852.986-1.297 1.623-.783l.084.076l3 3a1 1 0 0 1 .083 1.32l-.083.094l-3 3c-.602.603-1.614.22-1.701-.593L19 24v-2H6.956l-.215-.005a4.956 4.956 0 0 1 0-9.902l.215-.004l2.54-.001l2.797-2.795a1 1 0 0 1 1.32-.083l.094.083l2.796 2.795h2.453l.178-.004a3.045 3.045 0 0 0 0-6.079L18.956 6H9.414z"/>',
  width: 26,
  height: 26
};
const computer = {
  body: '<path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 13h18l-2 4H5zm0 0V7a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v6M8 21h8"/>',
  width: 24,
  height: 24
};
const exitOutline = {
  body: '<path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32" d="M320 176v-40a40 40 0 0 0-40-40H88a40 40 0 0 0-40 40v240a40 40 0 0 0 40 40h192a40 40 0 0 0 40-40v-40m64-160l80 80l-80 80m-193-80h273"/>',
  width: 512,
  height: 512
};
const message_security = {
  body: '<g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="4"><path d="M25.5 37H21l-10 5v-5H4V7h40v11"/><path d="M29 25.2c0-1.067 7-3.2 7-3.2s7 2.133 7 3.2c0 8.533-7 12.8-7 12.8s-7-4.267-7-12.8M12 15h6m-6 6h12"/></g>',
  width: 48,
  height: 48
};
const back = {
  body: '<path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l-7-7m0 0l7-7m-7 7h18"/>',
  width: 24,
  height: 24
};
const notes = {
  body: '<g fill="none" stroke="currentColor" stroke-linejoin="round" stroke-width="4"><path d="M8 6a2 2 0 0 1 2-2h20l10 10v28a2 2 0 0 1-2 2H10a2 2 0 0 1-2-2z"/><path stroke-linecap="round" d="M16 20h16m-16 8h16"/></g>',
  width: 48,
  height: 48
};
const find = {
  body: '<path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 1 1-14 0a7 7 0 0 1 14 0z"/>',
  width: 24,
  height: 24
};
const setting_laptop = {
  body: '<path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0"/><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"/><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"/><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"/><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"/><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"/><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"/><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"/><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"/>',
  width: 24,
  height: 24
};
const user_business = {
  body: '<path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0"/><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"/><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"/>',
  width: 24,
  height: 24
};
const preview_close_one = {
  body: '<path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0"/><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"/><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"/>',
  width: 24,
  height: 24
};
const notification_add = {
  body: '<path fill="currentColor" fill-rule="evenodd" d="M15 18v1a3 3 0 0 1-6 0v-1H5c-.55 0-1-.45-1-1s.45-1 1-1h1v-6a6 6 0 0 1 5-5.917V3a1 1 0 0 1 2 0v1.083c2.838.476 5 2.944 5 5.917v6h1c.55 0 1 .45 1 1s-.45 1-1 1zm-3 2a1 1 0 0 0 1-1v-1h-2v1a1 1 0 0 0 1 1m1-6v-2h2a1 1 0 0 0 0-2h-2V8a1 1 0 0 0-2 0v2H9a1 1 0 0 0 0 2h2v2a1 1 0 0 0 2 0"/>',
  width: 24,
  height: 24
};
const file_none = {
  body: '<path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0"/><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"/><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"/>',
  width: 24,
  height: 24
};
const deleteIcon = {
  body: '<g fill="none" stroke="currentColor" stroke-linejoin="round" stroke-width="4"><path d="M9 10v34h30V10z"/><path stroke-linecap="round" d="M20 20v13m8-13v13M4 10h40"/><path d="m16 10l3.289-6h9.488L32 10z"/></g>',
  width: 48,
  height: 48
};
const closeIcon = {
  body: '<path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="4" d="m8 8l32 32M8 40L40 8"/>',
  width: 48,
  height: 48
};
const confirmIcon = {
  body: '<g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><circle cx="12" cy="12" r="9" fill="currentColor"/><path stroke="#fff" d="M8 12l3 3l5-5"/></g>',
  width: 24,
  height: 24
};
const arrowDownIcon = {
  body: '<path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="4" d="M36 18L24 30L12 18"/>',
  width: 48,
  height: 48
};
const starOffIcon = {
  body: '<path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="m18.504 14.5l2.644-2.607c1.468-1.472.989-2.964-1.049-3.307l-3.186-.535c-.54-.09-1.179-.564-1.418-1.058l-1.758-3.549c-.95-1.925-2.507-1.925-3.466 0L9.5 5M7.296 8q-.105.034-.204.051L3.9 8.586c-2.041.343-2.511 1.835-1.05 3.307l2.48 2.5c.421.423.651 1.24.521 1.825l-.71 3.095c-.56 2.44.74 3.397 2.882 2.117l2.991-1.785c.55-.322 1.441-.322 1.982 0l2.991 1.785c2.152 1.28 3.443.332 2.882-2.117L18.797 19M2 2l20 20" color="currentColor"/>',
  width: 24,
  height: 24
};
const plusIcon = {
  body: '<path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="4" d="m24.06 10l-.036 28M10 24h28"/>',
  width: 48,
  height: 48
};
const arrowRightIcon = {
  body: '<path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="4" d="m19 12l12 12l-12 12"/>',
  width: 48,
  height: 48
};
const wifiIcon = {
  body: '<g fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="4" d="M4 18.965a29 29 0 0 1 1.817-1.586C17.037 8.374 33.382 8.903 44 18.965"/><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="4" d="M38 25.799c-7.732-7.732-20.268-7.732-28 0m22 6.515c-4.418-4.419-11.582-4.419-16 0"/><path fill="currentColor" fill-rule="evenodd" d="M24 40a2.5 2.5 0 1 0 0-5a2.5 2.5 0 0 0 0 5" clip-rule="evenodd"/></g>',
  width: 48,
  height: 48
};
const wifiOffIcon = {
  body: '<g fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="4" d="M44 18.965c-6.775-6.42-15.881-8.96-24.5-7.617M38 25.799a19.7 19.7 0 0 0-9.5-5.284M10 25.799a19.8 19.8 0 0 1 4.36-3.299M16 32.314a11.26 11.26 0 0 1 5-2.91"/><path fill="currentColor" fill-rule="evenodd" d="M24 40a2.5 2.5 0 1 0 0-5a2.5 2.5 0 0 0 0 5" clip-rule="evenodd"/><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="4" d="M40 40L8 8M4 18.965a29 29 0 0 1 3.5-2.84"/></g>',
  width: 48,
  height: 48
};
const arrowLeftIcon = {
  body: '<path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="4" d="M5.799 24h36m-24 12l-12-12l12-12"/>',
  width: 48,
  height: 48
};
const monitorIcon = {
  body: '<path fill="currentColor" d="M20 2H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h6v2H7v2h10v-2h-3v-2h6a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2m0 14H4V4h16zm-3.25-3a1.75 1.75 0 0 1-3.5 0L10 11.36a1.71 1.71 0 1 1 0-2.71L13.25 7a1.77 1.77 0 1 1 .68 1.37L10.71 10l3.22 1.61A1.74 1.74 0 0 1 16.75 13"/>',
  width: 24,
  height: 24
};
const checkMarkIcon = {
  body: '<path fill="none" stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M19.548 7.267a2 2 0 1 0-3.096-2.533L8.666 14.25L6.2 12.4a2 2 0 0 0-2.4 3.2l3.233 2.425a3 3 0 0 0 4.122-.5z"/>',
  width: 24,
  height: 24
};
const checkCircleIcon = {
  body: '<g fill="none" stroke="currentColor" stroke-linecap="round" stroke-width="2"><path d="m9 10l3.258 2.444a1 1 0 0 0 1.353-.142L20 5"/><path d="M21 12a9 9 0 1 1-6.67-8.693"/></g>',
  width: 24,
  height: 24
};
const externalLinkIcon = {
  body: '<g fill="none"><path d="m12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035q-.016-.005-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427q-.004-.016-.017-.018m.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093q.019.005.029-.008l.004-.014l-.034-.614q-.005-.018-.02-.022m-.715.002a.02.02 0 0 0-.027.006l-.006.014l-.034.614q.001.018.017.024l.015-.002l.201-.093l.01-.008l.003-.011l.018-.43l-.003-.012l-.01-.01z"/><path fill="currentColor" d="M13 3a1 1 0 0 1 .117 1.993L13 5H5v14h14v-8a1 1 0 0 1 1.993-.117L21 11v8a2 2 0 0 1-1.85 1.995L19 21H5a2 2 0 0 1-1.995-1.85L3 19V5a2 2 0 0 1 1.85-1.995L5 3zm6.243.343a1 1 0 0 1 1.497 1.32l-.083.095l-9.9 9.899a1 1 0 0 1-1.497-1.32l.083-.094z"/></g>',
  width: 24,
  height: 24
};
const hamburgerIcon = {
  body: '<path fill="currentColor" d="M2 8a1 1 0 0 1 1-1h18a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1m0 4a1 1 0 0 1 1-1h18a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1m1 3a1 1 0 1 0 0 2h12a1 1 0 1 0 0-2z"/>',
  width: 24,
  height: 24
};
const eyeIcon = {
  body: '<path fill="currentColor" d="M12 9a3 3 0 0 0-3 3a3 3 0 0 0 3 3a3 3 0 0 0 3-3a3 3 0 0 0-3-3m0 8a5 5 0 0 1-5-5a5 5 0 0 1 5-5a5 5 0 0 1 5 5a5 5 0 0 1-5 5m0-12.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5"/>',
  width: 24,
  height: 24
};
const taskListIcon = {
  body: '<path fill="currentColor" d="M280.768 753.728L691.456 167.04a32 32 0 1 1 52.416 36.672L314.24 817.472a32 32 0 0 1-45.44 7.296l-230.4-172.8a32 32 0 0 1 38.4-51.2zM736 448a32 32 0 1 1 0-64h192a32 32 0 1 1 0 64zM608 640a32 32 0 0 1 0-64h319.936a32 32 0 1 1 0 64zM480 832a32 32 0 1 1 0-64h447.936a32 32 0 1 1 0 64z"/>',
  width: 1024,
  height: 1024
};
const lockIcon = {
  body: '<path fill="none" stroke="currentColor" d="M12.5 8.5v-1a1 1 0 0 0-1-1h-10a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1v-1m0-4h-4a2 2 0 1 0 0 4h4m0-4a2 2 0 1 1 0 4m-9-6v-3a3 3 0 0 1 6 0v3m2.5 4h1m-3 0h1m-3 0h1" stroke-width="1"/>',
  width: 15,
  height: 15
};
const dashboardIcon = {
  body: '<path fill="currentColor" d="m25.18 12.32l-5.91 5.81a3 3 0 1 0 1.41 1.42l5.92-5.81Z"/><path fill="currentColor" d="M18 4.25A16.49 16.49 0 0 0 5.4 31.4l.3.35h24.6l.3-.35A16.49 16.49 0 0 0 18 4.25m11.34 25.5H6.66a14.43 14.43 0 0 1-3.11-7.84H7v-2H3.55A14.4 14.4 0 0 1 7 11.29l2.45 2.45l1.41-1.41l-2.43-2.46A14.4 14.4 0 0 1 17 6.29v3.5h2V6.3a14.47 14.47 0 0 1 13.4 13.61h-3.48v2h3.53a14.43 14.43 0 0 1-3.11 7.84"/>',
  width: 36,
  height: 36
};
const settingsIcon = {
  body: '<path fill="currentColor" d="m10.135 21l-.362-2.892q-.479-.145-1.035-.454q-.557-.31-.947-.664l-2.668 1.135l-1.865-3.25l2.306-1.739q-.045-.27-.073-.558q-.03-.288-.03-.559q0-.252.03-.53q.028-.278.073-.626L3.258 9.126l1.865-3.212L7.771 7.03q.448-.373.97-.673q.52-.3 1.013-.464L10.134 3h3.732l.361 2.912q.575.202 1.016.463t.909.654l2.725-1.115l1.865 3.211l-2.382 1.796q.082.31.092.569t.01.51q0 .233-.02.491q-.019.259-.088.626l2.344 1.758l-1.865 3.25l-2.681-1.154q-.467.393-.94.673t-.985.445L13.866 21zm1.838-6.5q1.046 0 1.773-.727T14.473 12t-.727-1.773t-1.773-.727q-1.052 0-1.776.727T9.473 12t.724 1.773t1.776.727"/>',
  width: 24,
  height: 24
};
const userGroupIcon = {
  body: '<path fill="currentColor" d="M12 5a3.5 3.5 0 0 0-3.5 3.5A3.5 3.5 0 0 0 12 12a3.5 3.5 0 0 0 3.5-3.5A3.5 3.5 0 0 0 12 5m0 2a1.5 1.5 0 0 1 1.5 1.5A1.5 1.5 0 0 1 12 10a1.5 1.5 0 0 1-1.5-1.5A1.5 1.5 0 0 1 12 7M5.5 8A2.5 2.5 0 0 0 3 10.5c0 .94.53 1.75 1.29 2.18c.36.2.77.32 1.21.32s.85-.12 1.21-.32c.37-.21.68-.51.91-.87A5.42 5.42 0 0 1 6.5 8.5v-.28c-.3-.14-.64-.22-1-.22m13 0c-.36 0-.7.08-1 .22v.28c0 1.2-.39 2.36-1.12 3.31c.12.19.25.34.4.49a2.48 2.48 0 0 0 1.72.7c.44 0 .85-.12 1.21-.32c.76-.43 1.29-1.24 1.29-2.18A2.5 2.5 0 0 0 18.5 8M12 14c-2.34 0-7 1.17-7 3.5V19h14v-1.5c0-2.33-4.66-3.5-7-3.5m-7.29.55C2.78 14.78 0 15.76 0 17.5V19h3v-1.93c0-1.01.69-1.85 1.71-2.52m14.58 0c1.02.67 1.71 1.51 1.71 2.52V19h3v-1.5c0-1.74-2.78-2.72-4.71-2.95M12 16c1.53 0 3.24.5 4.23 1H7.77c.99-.5 2.7-1 4.23-1"/>',
  width: 24,
  height: 24
};
const userIcon = {
  body: '<path fill="currentColor" d="M7.5 6.5C7.5 8.981 9.519 11 12 11s4.5-2.019 4.5-4.5S14.481 2 12 2S7.5 4.019 7.5 6.5M20 21h1v-1c0-3.859-3.141-7-7-7h-4c-3.86 0-7 3.141-7 7v1z"/>',
  width: 24,
  height: 24
};
const serverIcon = {
  body: '<path fill="currentColor" d="M2 4.6v4.8c0 .9.5 1.6 1.2 1.6h17.7c.6 0 1.2-.7 1.2-1.6V4.6C22 3.7 21.5 3 20.8 3H3.2C2.5 3 2 3.7 2 4.6M10 8V6H9v2zM5 8h2V6H5zm15 1H4V5h16zM2 14.6v4.8c0 .9.5 1.6 1.2 1.6h17.7c.6 0 1.2-.7 1.2-1.6v-4.8c0-.9-.5-1.6-1.2-1.6H3.2c-.7 0-1.2.7-1.2 1.6m8 3.4v-2H9v2zm-5 0h2v-2H5zm15 1H4v-4h16z"/>',
  width: 24,
  height: 24
};
const adjustIcon = {
  body: '<g fill="none" fill-rule="evenodd"><path d="m12.594 23.258l-.012.002l-.071.035l-.02.004l-.014-.004l-.071-.036q-.016-.004-.024.006l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427q-.004-.016-.016-.018m.264-.113l-.014.002l-.184.093l-.01.01l-.003.011l.018.43l.005.012l.008.008l.201.092q.019.005.029-.008l.004-.014l-.034-.614q-.005-.019-.02-.022m-.715.002a.02.02 0 0 0-.027.006l-.006.014l-.034.614q.001.018.017.024l.015-.002l.201-.093l.01-.008l.003-.011l.018-.43l-.003-.012l-.01-.01z"/><path fill="currentColor" d="M16 15c1.306 0 2.418.835 2.83 2H20a1 1 0 1 1 0 2h-1.17a3.001 3.001 0 0 1-5.66 0H4a1 1 0 1 1 0-2h9.17A3 3 0 0 1 16 15m0 2a1 1 0 1 0 0 2a1 1 0 0 0 0-2M8 9a3 3 0 0 1 2.762 1.828l.067.172H20a1 1 0 0 1 .117 1.993L20 13h-9.17a3.001 3.001 0 0 1-5.592.172L5.17 13H4a1 1 0 0 1-.117-1.993L4 11h1.17A3 3 0 0 1 8 9m0 2a1 1 0 1 0 0 2a1 1 0 0 0 0-2m8-8c1.306 0 2.418.835 2.83 2H20a1 1 0 1 1 0 2h-1.17a3.001 3.001 0 0 1-5.66 0H4a1 1 0 0 1 0-2h9.17A3 3 0 0 1 16 3m0 2a1 1 0 1 0 0 2a1 1 0 0 0 0-2"/></g>',
  width: 24,
  height: 24
};
const badgeIcon = {
  body: '<path fill="currentColor" d="M9 10a3.04 3.04 0 0 1 3-3a3.04 3.04 0 0 1 3 3a3.04 3.04 0 0 1-3 3a3.04 3.04 0 0 1-3-3m3 9l4 1v-3.08A7.54 7.54 0 0 1 12 18a7.54 7.54 0 0 1-4-1.08V20m4-16a5.78 5.78 0 0 0-4.24 1.74A5.78 5.78 0 0 0 6 10a5.78 5.78 0 0 0 1.76 4.23A5.78 5.78 0 0 0 12 16a5.78 5.78 0 0 0 4.24-1.77A5.78 5.78 0 0 0 18 10a5.78 5.78 0 0 0-1.76-4.26A5.78 5.78 0 0 0 12 4m8 6a8 8 0 0 1-.57 2.8A7.8 7.8 0 0 1 18 15.28V23l-6-2l-6 2v-7.72A7.9 7.9 0 0 1 4 10a7.68 7.68 0 0 1 2.33-5.64A7.73 7.73 0 0 1 12 2a7.73 7.73 0 0 1 5.67 2.36A7.68 7.68 0 0 1 20 10"/>',
  width: 24,
  height: 24
};
const jpgFileIcon = {
  body: '<path fill="currentColor" fill-rule="evenodd" d="M321.923 42.667H87.256v234.667h42.667v-192h174.293l81.707 81.706v110.294h42.666v-128zM85.573 448V320.028h28.81v105.394h55V448zm153.17-130.23q30.165 0 46.24 19.146q15.444 18.334 15.443 47.143q0 31.519-18.243 50.124q-15.714 16.075-43.44 16.075q-30.165 0-46.24-19.146q-15.443-18.334-15.443-47.866q0-30.887 18.243-49.491q15.804-15.985 43.44-15.985m-.09 22.578q-15.624 0-24.114 13.005q-7.676 11.74-7.676 30.164q0 21.315 9.121 33.055q8.58 11.108 22.759 11.108q15.534 0 24.204-13.095q7.676-11.56 7.676-30.526q0-20.862-9.121-32.603q-8.58-11.108-22.85-11.108m190.83 36.035v65.295q-11.018 3.704-15.534 4.877q-13.998 3.703-30.074 3.703q-31.61 0-48.136-15.895q-18.334-17.52-18.334-48.859q0-36.035 22.759-54.368q16.527-13.365 44.614-13.366q24.024 0 44.705 8.76l-9.844 22.488q-9.754-4.876-17.07-6.819q-7.315-1.941-16.075-1.941q-20.952 0-30.887 13.637q-8.399 11.559-8.399 30.435q0 22.669 12.644 34.138q10.115 9.212 25.107 9.212q8.76 0 16.617-2.98v-25.74H379.54v-22.577z"/>',
  width: 512,
  height: 512
};
const layoutIcon = {
  body: '<path fill="currentColor" d="M16 21q-.825 0-1.412-.587T14 19v-4q0-.825.588-1.412T16 13h4q.825 0 1.413.588T22 15v4q0 .825-.587 1.413T20 21zm0-2h4v-4h-4zM2 18v-2h9v2zm14-7q-.825 0-1.412-.587T14 9V5q0-.825.588-1.412T16 3h4q.825 0 1.413.588T22 5v4q0 .825-.587 1.413T20 11zm0-2h4V5h-4zM2 8V6h9v2zm16-1"/>',
  width: 24,
  height: 24
};
const cartIcon = {
  body: '<path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20a2 2 0 1 0 4 0a2 2 0 1 0-4 0m0 0H4m10 0h6m-8-5l-2-2H7a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1h-3z"/>',
  width: 24,
  height: 24
};
const formulaIcon = {
  body: '<g fill="currentColor"><path d="M5.387 11.523a.402.402 0 0 1 .593-.367q.087.047.157.102t.125.101a.18.18 0 0 0 .117.047q.078 0 .203-.117t.273-.313q.15-.195.305-.414t.29-.445l.226-.39q.093-.165.133-.266a15 15 0 0 0-.133-.524a15 15 0 0 1-.133-.523a4 4 0 0 0-.133-.422a1 1 0 0 0-.187-.313a.66.66 0 0 0-.266-.187a1.4 1.4 0 0 0-.375-.07a1.6 1.6 0 0 0-.328.031v-.195L7.69 7a2.4 2.4 0 0 1 .461.734q.079.195.133.399q.056.202.117.445q.117-.172.29-.438a4.5 4.5 0 0 1 .398-.523q.226-.258.476-.43A1 1 0 0 1 10.089 7q.195 0 .351.101q.157.102.157.32q0 .423-.422.423a.6.6 0 0 1-.29-.07a.6.6 0 0 0-.288-.071q-.149 0-.313.148a2.3 2.3 0 0 0-.312.352a10 10 0 0 0-.485.734l.446 1.852a1.6 1.6 0 0 0 .093.344a.7.7 0 0 0 .094.171a.18.18 0 0 0 .125.079a.37.37 0 0 0 .211-.086a2.1 2.1 0 0 0 .43-.47q.077-.116.125-.218l.187.094a2 2 0 0 1-.219.367a4 4 0 0 1-.351.422a3.4 3.4 0 0 1-.406.36q-.212.156-.383.148a.4.4 0 0 1-.281-.102a1.5 1.5 0 0 1-.204-.234a1.6 1.6 0 0 1-.132-.36a8 8 0 0 1-.118-.507a34 34 0 0 1-.101-.532a2.2 2.2 0 0 0-.11-.414l-.203.375a5 5 0 0 1-.28.453q-.165.236-.337.477a2.5 2.5 0 0 1-.375.422q-.202.18-.39.305A.66.66 0 0 1 5.91 12a.54.54 0 0 1-.36-.133a.45.45 0 0 1-.163-.344m6.11.477q.42-.54.648-1.164a3.9 3.9 0 0 0 .226-1.32q0-.704-.226-1.329A4.6 4.6 0 0 0 11.495 7h.734a3.77 3.77 0 0 1 .922 2.515q0 .711-.218 1.329q-.219.616-.704 1.156h-.734zM3.77 12a3.4 3.4 0 0 1-.704-1.149a4 4 0 0 1-.218-1.336q0-1.43.922-2.515h.726a4.1 4.1 0 0 0-.64 1.18a4.2 4.2 0 0 0-.227 1.335A3.93 3.93 0 0 0 4.496 12z"/><path d="M15.5 1H.5l-.5.5v13l.5.5h15l.5-.5v-13zM15 14H1V5h14zm0-10H1V2h14z"/></g>',
  width: 16,
  height: 16
};
const accessibilityIcon = {
  body: '<path fill="currentColor" d="M26 30h-2v-5a5.006 5.006 0 0 0-5-5h-6a5.006 5.006 0 0 0-5 5v5H6v-5a7.01 7.01 0 0 1 7-7h6a7.01 7.01 0 0 1 7 7zM22 6v4c0 1.103-.897 2-2 2h-1a1 1 0 0 0 0 2h1c2.206 0 4-1.794 4-4V6zm-6 10c-3.86 0-7-3.14-7-7s3.14-7 7-7c1.989 0 3.89.85 5.217 2.333l-1.49 1.334A5 5 0 0 0 16 4c-2.757 0-5 2.243-5 5s2.243 5 5 5z"/>',
  width: 32,
  height: 32
};
const gridIcon = {
  body: '<path fill="currentColor" stroke="currentColor" stroke-linejoin="round" stroke-width="4" d="M18 6H8a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2Zm0 22H8a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V30a2 2 0 0 0-2-2ZM40 6H30a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2Zm0 22H30a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V30a2 2 0 0 0-2-2Z"/>',
  width: 48,
  height: 48
};
const documentIcon = {
  body: '<path fill="currentColor" d="M15 20a1 1 0 0 0 1-1V4H8a1 1 0 0 0-1 1v11H5V5a3 3 0 0 1 3-3h11a3 3 0 0 1 3 3v1h-2V5a1 1 0 0 0-1-1a1 1 0 0 0-1 1v14a3 3 0 0 1-3 3H5a3 3 0 0 1-3-3v-1h11a2 2 0 0 0 2 2M9 6h5v2H9zm0 4h5v2H9zm0 4h5v2H9z"/>',
  width: 24,
  height: 24
};
const clockIcon = {
  body: '<path fill="currentColor" d="M12 20a8 8 0 0 0 8-8a8 8 0 0 0-8-8a8 8 0 0 0-8 8a8 8 0 0 0 8 8m0-18a10 10 0 0 1 10 10a10 10 0 0 1-10 10C6.47 22 2 17.5 2 12A10 10 0 0 1 12 2m.5 5v5.25l4.5 2.67l-.75 1.23L11 13V7z"/>',
  width: 24,
  height: 24
};
const helpIcon = {
  body: '<path fill="currentColor" fill-rule="evenodd" d="M.877 7.5a6.623 6.623 0 1 1 13.246 0a6.623 6.623 0 0 1-13.246 0M7.5 1.827a5.673 5.673 0 1 0 0 11.346a5.673 5.673 0 0 0 0-11.346m.75 8.673a.75.75 0 1 1-1.5 0a.75.75 0 0 1 1.5 0m-2.2-4.25c0-.678.585-1.325 1.45-1.325s1.45.647 1.45 1.325c0 .491-.27.742-.736 1.025l-.176.104a5 5 0 0 0-.564.36c-.242.188-.524.493-.524.961a.55.55 0 0 0 1.1.004a.4.4 0 0 1 .1-.098c.102-.079.215-.144.366-.232q.116-.067.27-.159c.534-.325 1.264-.861 1.264-1.965c0-1.322-1.115-2.425-2.55-2.425S4.95 4.928 4.95 6.25a.55.55 0 0 0 1.1 0" clip-rule="evenodd"/>',
  width: 15,
  height: 15
};
const aiTranslateIcon = {
  body: '<path fill="currentColor" d="m15 19l-1.414 1.414L17.172 24H4V11H2v13a2 2 0 0 0 2 2h13.172l-3.586 3.586L15 31l6-6zm9-1v-2h2V4h-2V2h6v2h-2v12h2v2z"/><path fill="currentColor" d="M21 18h2L17.5 2l-3 .009L9 18h2l1.333-4h7.334zm-8-6l3-9l3 9z"/>',
  width: 32,
  height: 32
};
const aiChatIcon = {
  body: '<g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" color="currentColor"><path d="M14.17 20.89c4.184-.277 7.516-3.657 7.79-7.9c.053-.83.053-1.69 0-2.52c-.274-4.242-3.606-7.62-7.79-7.899a33 33 0 0 0-4.34 0c-4.184.278-7.516 3.657-7.79 7.9a20 20 0 0 0 0 2.52c.1 1.545.783 2.976 1.588 4.184c.467.845.159 1.9-.328 2.823c-.35.665-.526.997-.385 1.237c.14.24.455.248 1.084.263c1.245.03 2.084-.322 2.75-.813c.377-.279.566-.418.696-.434s.387.09.899.3c.46.19.995.307 1.485.34c1.425.094 2.914.094 4.342 0"/><path d="m7.5 15l1.842-5.526a.694.694 0 0 1 1.316 0L12.5 15m3-6v6m-7-2h3"/></g>',
  width: 24,
  height: 24
};
const history = {
  body: '<path fill="currentColor" d="M13.5 8H12v5l4.28 2.54l.72-1.21l-3.5-2.08zM13 3a9 9 0 0 0-9 9H1l3.96 4.03L9 12H6a7 7 0 0 1 7-7a7 7 0 0 1 7 7a7 7 0 0 1-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42A8.9 8.9 0 0 0 13 21a9 9 0 0 0 9-9a9 9 0 0 0-9-9"/>',
  width: 24,
  height: 24
};

const copyIcon = {
  body: '<path fill="currentColor" d="M472 16H160a24.027 24.027 0 0 0-24 24v312a24.027 24.027 0 0 0 24 24h312a24.027 24.027 0 0 0 24-24V40a24.027 24.027 0 0 0-24-24m-8 328H168V48h296Z"/><path fill="currentColor" d="M344 464H48V168h56v-32H40a24.027 24.027 0 0 0-24 24v312a24.027 24.027 0 0 0 24 24h312a24.027 24.027 0 0 0 24-24v-64h-32Z"/>',
  width: 512,
  height: 512
};

const userSearchIcon = {
  body: '<g fill="none" stroke="currentColor" stroke-linecap="round" stroke-width="4"><path stroke-linejoin="round" d="M20 10H6a2 2 0 0 0-2 2v26a2 2 0 0 0 2 2h36a2 2 0 0 0 2-2v-2.5"/><path d="M10 23h8m-8 8h24"/><circle cx="34" cy="16" r="6" stroke-linejoin="round"/><path stroke-linejoin="round" d="M44 28.419C42.047 24.602 38 22 34 22s-5.993 1.133-8.05 3"/></g>',
  width: 48,
  height: 48
};

const retryIcon = {
  body: '<path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M4.513 19.487c2.512 2.392 5.503 1.435 6.7.466c.618-.501.897-.825 1.136-1.065c.837-.777.784-1.555.24-2.177c-.219-.249-1.616-1.591-2.956-2.967c-.694-.694-1.172-1.184-1.582-1.58c-.547-.546-1.026-1.172-1.172-1.58c-.547-.546-1.026-1.172-1.744-1.154c-.658 0-1.136.58-1.735 1.179c-.688.688-1.196 1.555-1.375 2.333c-.539 2.273.299 3.888 1.316 4.965m0 0L2 21.999M19.487 4.515c-2.513-2.394-5.494-1.42-6.69-.45c-.62.502-.898.826-1.138 1.066c-.837.778-.784 1.556-.239 2.178c.078.09.31.32.635.644m7.432-3.438c1.017 1.077 1.866 2.71 1.327 4.985c-.18.778-.688 1.645-1.376 2.334c-.598.598-1.077 1.179-1.735 1.179c-.718.018-1.09-.502-1.639-1.048m3.423-7.45L22 2m-5.936 9.964c-.41-.395-.994-.993-1.688-1.687c-.858-.882-1.74-1.75-2.321-2.325m4.009 4.012l-1.562 1.524m-3.99-3.983l1.543-1.553" color="currentColor"/>',
  width: 24,
  height: 24
};

const docListIcon = {
  body: '<path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="4" d="M10 44h28a2 2 0 0 0 2-2V14H30V4H10a2 2 0 0 0-2 2v36a2 2 0 0 0 2 2M30 4l10 10M17 25h14m-14 6h14m0-6l-5-5m-4 16l-5-5"/>',
  width: 48,
  height: 48
};

const uploadIcon = {
  body: '<path fill="currentColor" d="M11 16V7.85l-2.6 2.6L7 9l5-5l5 5l-1.4 1.45l-2.6-2.6V16zm-5 4q-.825 0-1.412-.587T4 18v-3h2v3h12v-3h2v3q0 .825-.587 1.413T18 20z"/>',
  width: 24,
  height: 24
};

const saveIcon = {
  body: '<path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M16.25 21v-4.765a1.59 1.59 0 0 0-1.594-1.588H9.344a1.59 1.59 0 0 0-1.594 1.588V21m8.5-17.715v2.362a1.59 1.59 0 0 1-1.594 1.588H9.344A1.59 1.59 0 0 1 7.75 5.647V3m8.5.285A3.2 3.2 0 0 0 14.93 3H7.75m8.5.285c.344.156.661.374.934.645l2.382 2.375A3.17 3.17 0 0 1 20.5 8.55v9.272A3.18 3.18 0 0 1 17.313 21H6.688A3.18 3.18 0 0 1 3.5 17.823V6.176A3.18 3.18 0 0 1 6.688 3H7.75"/>',
  width: 24,
  height: 24
};

const customSvgIcon = {
  body: `
    <path fill="currentColor" d="m9.11.427l6.14 4.98c.993.805.993 2.37 0 3.18l-3.76 3.05q.005-.069.005-.139c0-.349-.09-.677-.246-.962l3.35-2.74a1.035 1.035 0 0 0 0-1.59l-6.08-4.98c-.633-.514-1.52-.043-1.52.794v2.55a2 2 0 0 0-1 0V2.02c0-1.67 1.85-2.62 3.11-1.59z"/>
    <path fill="currentColor" fill-rule="evenodd" d="m5.66 7.06l.394-.788a.5.5 0 1 1 .895.447l-.343.685a3.36 3.36 0 0 1 1.411 1.48l1.21-.804a.5.5 0 1 1 .554.832l-1.46.972l.012.094l.113 1.02h1.06a.5.5 0 0 1 0 1h-.944l.003.024q.046.421-.003.824a3.5 3.5 0 0 1-.469 1.386l1.64.818a.5.5 0 0 1-.447.895l-1.83-.914c-.637.598-1.5.967-2.45.967s-1.81-.37-2.45-.967l-1.83.914a.5.5 0 1 1-.447-.895l1.64-.818a3.56 3.56 0 0 1-.472-2.21l.003-.025H.506l-.06-.003a.5.5 0 0 1 .06-.996h1.06l.114-1.02l.011-.094l-1.46-.972a.5.5 0 1 1 .554-.832l1.21.804q.203-.414.503-.755c.258-.292.565-.538.908-.725l-.342-.685a.5.5 0 1 1 .894-.447l.394.788a3.4 3.4 0 0 1 1.318 0zm-1.12.981l-.349.07l-.313.17a2.36 2.36 0 0 0-.991 1.04l-.161.333l-.051.369l-.009.066l-.227 2.04a2.55 2.55 0 0 0 .34 1.59l.188.32l.273.257a2.573 2.573 0 0 0 3.52 0l.273-.256l.19-.321c.271-.461.403-1.01.338-1.59l-.227-2.04l-.008-.066l-.051-.37l-.161-.331a2.37 2.37 0 0 0-.991-1.04L5.81 8.11l-.35-.07a2.4 2.4 0 0 0-.925 0z" clip-rule="evenodd"/>
  `,
  width: 16,
  height: 16
};

const arrowRightIcon2 = {
  body: '<g fill="none"><path d="M24 0v24H0V0zM12.594 23.258l-.012.002l-.071.035l-.02.004l-.014-.004l-.071-.036q-.016-.004-.024.006l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427q-.004-.016-.016-.018m.264-.113l-.014.002l-.184.093l-.01.01l-.003.011l.018.43l.005.012l.008.008l.201.092q.019.005.029-.008l.004-.014l-.034-.614q-.005-.018-.02-.022m-.715.002a.02.02 0 0 0-.027.006l-.006.014l-.034.614q.001.018.017.024l.015-.002l.201-.093l.01-.008l.003-.011l.018-.43l-.003-.012l-.01-.01z"/><path fill="currentColor" d="M18.5 19a1.5 1.5 0 0 0 3 0V5a1.5 1.5 0 0 0-3 0zM10.944 6.697a1.5 1.5 0 0 0 0 2.121l1.682 1.682H4a1.5 1.5 0 0 0 0 3h8.626l-1.682 1.682a1.5 1.5 0 1 0 2.121 2.121l4.243-4.242a1.5 1.5 0 0 0 0-2.121l-4.243-4.243a1.5 1.5 0 0 0-2.121 0"/></g>',
  width: 24,
  height: 24
};

const arrowLeftIcon2 = {
  body: '<g fill="none"><path d="M24 0v24H0V0zM12.594 23.258l-.012.002l-.071.035l-.02.004l-.014-.004l-.071-.036q-.016-.004-.024.006l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427q-.004-.016-.016-.018m.264-.113l-.014.002l-.184.093l-.01.01l-.003.011l.018.43l.005.012l.008.008l.201.092q.019.005.029-.008l.004-.014l-.034-.614q-.005-.018-.02-.022m-.715.002a.02.02 0 0 0-.027.006l-.006.014l-.034.614q.001.018.017.024l.015-.002l.201-.093l.01-.008l.003-.011l.018-.43l-.003-.012l-.01-.01z"/><path fill="currentColor" d="M9.056 17.303a1.5 1.5 0 0 0 0-2.121L7.375 13.5H15a1.5 1.5 0 0 0 0-3H7.374l1.682-1.682a1.5 1.5 0 1 0-2.121-2.122L2.692 10.94a1.5 1.5 0 0 0 0 2.121l4.243 4.243a1.5 1.5 0 0 0 2.121 0ZM21.5 6a1.5 1.5 0 0 0-3 0v12a1.5 1.5 0 0 0 3 0z"/></g>',
  width: 24,
  height: 24
};

const oauthIcon = {
  body: '<g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="M2 12a10 10 0 1 0 20 0a10 10 0 1 0-20 0"/><path d="M12.556 6c.65 0 1.235.373 1.508.947l2.839 7.848a1.646 1.646 0 0 1-1.01 2.108a1.673 1.673 0 0 1-2.068-.851L13.365 15h-2.73l-.398.905A1.67 1.67 0 0 1 8.26 16.95l-.153-.047a1.647 1.647 0 0 1-1.056-1.956l2.824-7.852a1.66 1.66 0 0 1 1.409-1.087z"/></g>',
  width: 24,
  height: 24
};

const filterIcon = {
  body: '<g fill="none" fill-rule="evenodd"><path d="m12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035q-.016-.005-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427q-.004-.016-.017-.018m.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093q.019.005.029-.008l.004-.014l-.034-.614q-.005-.018-.02-.022m-.715.002a.02.02 0 0 0-.027.006l-.006.014l-.034.614q.001.018.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01z"/><path fill="currentColor" d="M3 4.5A1.5 1.5 0 0 1 4.5 3h15A1.5 1.5 0 0 1 21 4.5v2.086A2 2 0 0 1 20.414 8L15 13.414v7.424a1.1 1.1 0 0 1-1.592.984l-3.717-1.858A1.25 1.25 0 0 1 9 18.846v-5.432L3.586 8A2 2 0 0 1 3 6.586zM5 5v1.586l5.56 5.56a1.5 1.5 0 0 1 .44 1.061v5.175l2 1v-6.175c0-.398.158-.78.44-1.06L19 6.585V5z"/></g>',
  width: 24,
  height: 24
};

addIcon("ep:lollipop", Lollipop);
addIcon("ep:home-filled", HomeFilled);

addIcon("ri:search-line", Search);
addIcon("ri:information-line", InformationLine);

addIcon("tabler:ai", tablerAi);
addIcon("tabler:action", action);
addIcon("tabler:playbook", playbook);
addIcon("icon-park-outline:computer", computer);
addIcon("ion:exit-outline", exitOutline);
addIcon("icon-park-outline:message-security", message_security);
addIcon("icon-park-outline:back", back);
addIcon("icon-park-outline:notes", notes);
addIcon("icon-park-outline:find", find);
addIcon("icon-park-outline:setting-laptop", setting_laptop);
addIcon("icon-park-outline:user-business", user_business);
addIcon("icon-park-outline:preview-close-one", preview_close_one);
addIcon("fe:notice-on", notification_add);
addIcon("mingcute:announcement-fill", file_none);
addIcon("icon-park-outline:delete", deleteIcon);
addIcon("icon-park-outline:close", closeIcon);
addIcon("icon-park-outline:confirm", confirmIcon);
addIcon("icon-park-outline:arrow-down", arrowDownIcon);
addIcon("hugeicons:star-off", starOffIcon);
addIcon("icon-park-outline:plus", plusIcon);
addIcon("icon-park-outline:right", arrowRightIcon);
addIcon("icon-park-outline:wifi", wifiIcon);
addIcon("icon-park-outline:close-wifi", wifiOffIcon);
addIcon("icon-park-outline:left", arrowLeftIcon);
addIcon("gridicons:share-computer", monitorIcon);
addIcon("lets-icons:done-round-fill", checkMarkIcon);
addIcon("lets-icons:done-ring-round", checkCircleIcon);
addIcon("mingcute:edit-line", externalLinkIcon);
addIcon("gg:details-more", hamburgerIcon);
addIcon("icon-park-outline:preview-open", eyeIcon);
addIcon("ep:finished", taskListIcon);
addIcon("teenyicons:password-outline", lockIcon);
addIcon("clarity:dashboard-line", dashboardIcon);
addIcon("material-symbols-light:settings", settingsIcon);
addIcon("mdi:account-group-outline", userGroupIcon);
addIcon("bxs:user", userIcon);
addIcon("mdi:server-outline", serverIcon);
addIcon("mingcute:settings-6-line", adjustIcon);
addIcon("mdi:license", badgeIcon);
addIcon("ix:log", jpgFileIcon);
addIcon("material-symbols:event-list-outline", layoutIcon);
addIcon("tabler:timeline-event", cartIcon);
addIcon("codicon:variable-group", formulaIcon);
addIcon("carbon:customer-service", accessibilityIcon);
addIcon("icon-park-solid:all-application", gridIcon);
addIcon("mdi:script-text-outline", documentIcon);
addIcon("mdi:clock-outline", clockIcon);
addIcon("radix-icons:question-mark-circled", helpIcon);
addIcon("carbon:ai-launch", aiTranslateIcon);
addIcon("hugeicons:ai-chat-02", aiChatIcon);
addIcon("mdi:history", history);
addIcon("cil:clone", copyIcon);
addIcon("icon-park-outline:permissions", userSearchIcon);
addIcon("hugeicons:connect", retryIcon);
addIcon("icon-park-outline:file-conversion", docListIcon);
addIcon("mdi:upload", uploadIcon);
addIcon("icon-park-outline:save", saveIcon);
addIcon("qlementine-icons:run-debug-16", customSvgIcon);
addIcon("mingcute:arrow-to-right-fill", arrowRightIcon2);
addIcon("mingcute:arrow-to-left-fill", arrowLeftIcon2);
addIcon("tabler:brand-oauth", oauthIcon);
addIcon("mingcute:filter-line", filterIcon);
