.wave {
    position: fixed;
    height: 100%;
    width: 100%;
    left: 0;
    bottom: 0;
    z-index: -1;
}

.login-container {
    width: 100vw;
    height: 100vh;
    max-width: 100%;
    display: grid;
    grid-template-columns: 1.5fr 1fr;
    /* grid-gap: 18rem; */
    /* padding: 0 2rem; */
}

.img {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    overflow: hidden;
}

.img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    overflow: hidden;
}

.login-box {
    /* background: #F0F5FA; */
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    overflow: hidden;
}

.login-form {
    width: 400px;
    padding: 30px 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease-in-out;
}

.login-form:hover {
    box-shadow: 0 12px 28px rgba(0, 0, 0, 0.12);
}

.login-form h2 {
    text-transform: uppercase;
    margin: 15px 0 10px 0;
    color: #333;
    font: bold 200% Consolas, Monaco, monospace;
}

.login-form span {
    font-size: 15px;
    color: #999;
    margin-bottom: 15px;
    display: block;
}

.login-form .el-form {
    width: 100%;
}

.login-form .el-input__wrapper {
    border-radius: 4px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
}

.login-form .el-input__wrapper:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.06);
}

.login-form .el-button {
    height: 40px;
    font-size: 16px;
    font-weight: 500;
    letter-spacing: 1px;
    border-radius: 4px;
    margin-top: 20px;
    transition: all 0.3s ease;
}

.login-form .el-button:hover {
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.avatar {
    width: 350px;
    height: 80px;
}

@media screen and (max-width: 1180px) {
    .login-container {
        grid-gap: 9rem;
    }

    .login-form {
        width: 290px;
    }

    .login-form h2 {
        font-size: 2.4rem;
        margin: 8px 0;
    }

    .img img {
        width: 360px;
    }

    .avatar {
        width: 280px;
        height: 80px;
    }
}

@media screen and (max-width: 968px) {
    .wave {
        display: none;
    }

    .img {
        display: none;
    }

    .login-container {
        grid-template-columns: 1fr;
    }

    .login-box {
        justify-content: center;
    }
}
