import { computed, nextTick, ref } from "vue";
import { useUserStore } from "./modules/user";
import {
  type Announcement,
  type Message,
  MessageType,
  type Room,
  type User
} from "@/directives/warroom/warChat";
import {
  getMembers,
  getPrivateRoom,
  getStarRooms,
  getUserRooms,
  queryMessages
} from "@/api/warroom";
import { ElMessage } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import { defineStore } from "pinia";
import {
  // getToken,
  type DataInfo,
  userKey,
  multipleTabsKey
} from "@/utils/auth";
import { storageLocal } from "@pureadmin/utils";
import Cookies from "js-cookie";

export const useChatStore = defineStore("chat", () => {
  const userStore = useUserStore();
  const route = useRoute();
  const router = useRouter();
  const ws = ref<WebSocket | null>(null);
  const heartbeatInterval = ref<number | null>(null); // 心跳计时器
  const heartbeatTimeout = ref<number | null>(null); // 心跳超时计时器
  const reconnectAttempts = ref(0); // 重连次数
  const maxReconnectAttempts = ref(5); // 最大重连次数
  const isManualClose = ref(false); // 是否手动关闭连接
  const chatType = ref<"public" | "private" | "ai">();
  const messageList = ref();
  const publicMessages = ref<Message[]>([]);
  const privateMessages = ref<{ [key: string]: Message[] }>({});
  const hasLoadedPublicHistory = ref(false);
  const users = ref<User[]>([]);
  const onlineUserIds = ref<Set<string>>(new Set());
  const selectedUser = ref<User | null>(null);
  const currentUserId = ref<string>(
    userStore.$state.id ? String(userStore.$state.id) : ""
  );
  const rooms = ref<Room[]>([]);
  const userRooms = ref<string[]>([]);
  const currentRoomId = ref<string>((route.params.roomId as string) || "");
  const historyLimit = ref(20);
  const searchKeyword = ref("");
  const searchResults = ref<{ message_id: string; highlight: boolean }[]>([]);
  const currentSearchIndex = ref(-1);
  const messageSearchResults = ref<any[]>([]);
  const messageSearchTotal = ref(0);
  const messageSearchLoading = ref(false);
  const isFetchingRooms = ref(false);
  const isLoadingMessages = ref(false);
  const announcements = ref<Announcement[]>([]);
  const announcementsTotal = ref(0);
  const isLoadingAnnouncements = ref(false);

  // =========================================================================================
  // 1. websocket相关
  const connectWebSocket = () => {
    console.log("连接打开");
    if (
      ws.value &&
      (ws.value.readyState === WebSocket.OPEN ||
        ws.value.readyState === WebSocket.CONNECTING)
    ) {
      return;
    }
    if (ws.value) {
      try {
        ws.value.close();
      } catch (e) {
        console.error("关闭旧连接出错", e);
      }
    }
    const protocol = window.location.protocol === "https:" ? "wss" : "ws";

    ws.value = new WebSocket(
      `${protocol}://${window.location.host}${import.meta.env.VITE_LION_MGMT_HOST_PATH}/warroom/ws`
    );
    ws.value.onopen = handleWebSocketOpen;
    ws.value.onclose = handleWebSocketClose;

    ws.value.onmessage = event => {
      try {
        const data = JSON.parse(event.data);
        handleWebSocketMessage(data);
      } catch (error) {
        console.log(error);
      }
    };

    ws.value.onerror = () => {
      handleWebSocketError();
    };
  };

  const handleWebSocketOpen = () => {
    // 连接成功，重置重连计数器
    reconnectAttempts.value = 0; // 重连次数
    isManualClose.value = false; // 是否手动关闭连接

    // 初次握手将用户id传给后端
    // if (ws.value && ws.value.readyState === WebSocket.OPEN) {
    //   const authData = {
    //     auth: getToken()?.accessToken
    //   };
    //   ws.value.send(JSON.stringify(authData));
    // }
    startHeartbeat();
    fetchRooms();

    if (chatType.value === "public" && currentRoomId.value) {
      fetchRoomUsers(currentRoomId.value);
      requestOnlineUsers(currentRoomId.value);
    }
  };

  const handleWebSocketClose = () => {
    console.log("连接关闭");
    onlineUserIds.value.clear();
    cleanupResources(); // 清除心跳计时器和心跳超时计时器

    // 如果不是手动关闭且未达到最大重连次数，则尝试重连
    if (
      !isManualClose.value &&
      reconnectAttempts.value < maxReconnectAttempts.value
    ) {
      const delay = Math.min(
        1000 * Math.pow(2, reconnectAttempts.value),
        30000
      );
      console.log(
        `WebSocket连接断开，${delay}ms后尝试重连 (${reconnectAttempts.value + 1}/${maxReconnectAttempts.value})`
      );

      setTimeout(() => {
        reconnectAttempts.value++;
        connectWebSocket();
      }, delay);
    }
  };
  // websocket消息都通过该函数处理
  const handleWebSocketMessage = (data: any) => {
    try {
      // 接收到任何消息都说明连接正常，重置心跳计时器
      resetHeartbeat();

      const completeMessage = {
        ...data,
        message_id: data.message_id || Date.now().toString(),
        sender_id: data.sender_id || userStore.$state.id,
        timestamp: data.timestamp || new Date().toISOString(),
        is_recalled: data.is_recalled || false
      };
      // 处理不同类型的消息
      switch (data.type) {
        case "heartbeat_response":
          // 收到心跳响应，清除超时计时器
          if (heartbeatTimeout.value) {
            clearTimeout(heartbeatTimeout.value);
            heartbeatTimeout.value = null;
          }
          break;
        case "user_status":
          updateUserOnlineStatus(data.user_id, data.online);
          break;
        case "online_users_list":
          if (data.users && Array.isArray(data.users)) {
            onlineUserIds.value.clear();
            data.users.forEach(user => {
              if (user.online) {
                onlineUserIds.value.add(user.user_id);
              }
              // 同时更新users数组中对应用户的online_status
              updateUserInList(user.user_id, user.online);
            });
          }
          break;
        case "online_users_update":
          if (data.online_users && Array.isArray(data.online_users)) {
            onlineUserIds.value.clear();
            data.online_users.forEach(userId => {
              onlineUserIds.value.add(userId);
              updateUserInList(userId, true);
            });
            // 将不在列表中的用户设为离线
            users.value.forEach(user => {
              if (!onlineUserIds.value.has(user.id)) {
                user.online_status = false;
              }
            });
          }
          break;
        case "public":
          publicMessages.value.push(completeMessage);
          if (chatType.value === "public") {
            scrollToBottom();
          }
          break;
        case "private":
          const roomId = data.room_id;
          if (!roomId) break;

          if (!privateMessages.value[roomId]) {
            privateMessages.value[roomId] = [];
          }

          const existingMsg = privateMessages.value[roomId]?.find(
            m => m.message_id === data.message_id
          );
          if (existingMsg) {
            break;
          }

          privateMessages.value[roomId].push(completeMessage);

          if (chatType.value === "private" && currentRoomId.value === roomId) {
            scrollToBottom();
          }
          break;
        case "system":
          if (chatType.value === "public") {
            publicMessages.value.push({
              ...data,
              message_id: Date.now().toString(),
              type: "system"
            });
            scrollToBottom();
          }
          break;
        case "recall":
          updateRecalledMessage(data.message_id);
          break;
        case "recall_success":
          updateRecalledMessage(data.message_id);
          ElMessage.success({
            message: "消息已撤回",
            duration: 1000
          });
          break;
        case "recall_error":
          ElMessage.error(data.message || "撤回消息失败");
          break;
        case "new_announcement":
          // 将公告添加到消息列表作为一条特殊消息
          const announcementMessage = {
            message_id: data.id,
            type: MessageType.ANNOUNCEMENT,
            content: data.content,
            sender_id: data.sender_id || "",
            sender_name: data.sender_name,
            timestamp: data.timestamp,
            room_id: data.room_id,
            image_content: "",
            is_recalled: false,
            is_pinned: true
          };

          publicMessages.value.push(announcementMessage as Message);
          scrollToBottom();

          // 如果不是自己发送的公告，才显示消息提示
          if (
            data.sender_id !== currentUserId.value &&
            String(data.sender_id) !== String(currentUserId.value)
          ) {
            ElMessage.success({
              message: "收到新公告",
              duration: 3000
            });
          }

          // 如果当前正在查看公告列表，自动刷新
          if (currentRoomId.value === data.room_id) {
            fetchAnnouncements(currentRoomId.value);
          }
          break;
        case "announcement_deleted":
          // 如果正在查看公告列表，自动刷新
          if (currentRoomId.value === data.room_id) {
            fetchAnnouncements(currentRoomId.value);
          }
          break;
        case "delete_announcement_success":
          ElMessage.success({
            message: "公告已删除",
            duration: 1000
          });
          break;
        case "runlog": {
          // 查找是否已存在 run_id 和 message_id 都一致的 runlog 消息
          const existingIndex = publicMessages.value.findIndex(
            msg =>
              msg.type === "runlog" &&
              (msg.content as any)?.run_id === (data.content as any)?.run_id &&
              msg.message_id === data.message_id
          );
          if (existingIndex !== -1) {
            const oldStatus = (
              publicMessages.value[existingIndex].content as any
            )?.status;
            const newStatus = (data.content as any)?.status;
            if (oldStatus !== newStatus) {
              // status 不一样，整体替换为最新消息
              publicMessages.value[existingIndex] = completeMessage;
            } else {
              // 其它字段有变化，直接追加
              publicMessages.value.push(completeMessage);
            }
          } else {
            // 没有找到，直接追加
            publicMessages.value.push(completeMessage);
          }
          break;
        }
        case "ai": {
          // 查找本地 AI 消息（优先 message_id 匹配，其次 temp_id 匹配）
          let idx = publicMessages.value.findIndex(
            m =>
              (m.type as any) === "ai" &&
              (m.message_id === completeMessage.message_id ||
                (completeMessage.temp_id &&
                  m.message_id === completeMessage.temp_id))
          );
          if (idx !== -1) {
            // 已有本地消息，追加内容
            publicMessages.value[idx].content += completeMessage.content;
          } else {
            // 没有本地消息，直接插入（兼容后端主动推送）
            publicMessages.value.push({
              ...completeMessage,
              loading: !completeMessage.is_end // 第一个分片一般不是结束
            });
          }
          scrollToBottom();
          break;
        }
      }

      console.log("publicMessages:", publicMessages.value);
    } catch (err) {
      console.error("处理WebSocket消息时出错:", err);
    }
  };

  const handleWebSocketError = () => {
    ws.value?.close();
  };

  const startHeartbeat = () => {
    cleanupResources();
    heartbeatInterval.value = window.setInterval(() => {
      if (ws.value?.readyState === WebSocket.OPEN) {
        // 发送心跳包
        ws.value.send(JSON.stringify({ type: "heartbeat" }));

        // 设置心跳响应超时检测（20秒）
        heartbeatTimeout.value = window.setTimeout(() => {
          console.log("心跳响应超时，连接可能异常，尝试重连");
          handleConnectionError();
        }, 20000);
      }
    }, 30000);
  };

  const resetHeartbeat = () => {
    // 清除现有的心跳计时器
    if (heartbeatInterval.value) {
      clearInterval(heartbeatInterval.value);
      heartbeatInterval.value = null;
    }
    // 清除心跳超时计时器
    if (heartbeatTimeout.value) {
      clearTimeout(heartbeatTimeout.value);
      heartbeatTimeout.value = null;
    }
    // 重新开始心跳
    startHeartbeat();
  };

  const handleConnectionError = () => {
    isManualClose.value = false;
    ws.value?.close();

    // 如果重连次数未达到上限，尝试重连
    if (reconnectAttempts.value < maxReconnectAttempts.value) {
      const delay = Math.min(
        1000 * Math.pow(2, reconnectAttempts.value),
        30000
      );
      console.log(
        `WebSocket连接异常，${delay}ms后尝试重连 (${reconnectAttempts.value + 1}/${maxReconnectAttempts.value})`
      );

      setTimeout(() => {
        reconnectAttempts.value++;
        connectWebSocket();
      }, delay);
    } else {
      console.log("达到最大重连次数，停止重连");
      ElMessage.error("网络连接已断开，请刷新页面重试");
    }
  };

  const cleanupResources = () => {
    if (heartbeatInterval.value) {
      window.clearInterval(heartbeatInterval.value);
      heartbeatInterval.value = null;
    }
    if (heartbeatTimeout.value) {
      clearTimeout(heartbeatTimeout.value);
      heartbeatTimeout.value = null;
    }
  };

  // 关闭WebSocket连接
  const closeWebSocket = () => {
    if (ws.value) {
      isManualClose.value = true; // 标记为手动关闭，防止自动重连
      cleanupResources(); // 清理定时器
      try {
        ws.value.close();
      } catch (e) {
        console.error("关闭WebSocket连接出错", e);
      }
      ws.value = null;
    }
    // 清理相关状态
    onlineUserIds.value.clear();
    rooms.value = [];
    userRooms.value = [];
    publicMessages.value = [];
    privateMessages.value = {};
    users.value = [];
    currentRoomId.value = "";
    selectedUser.value = null;
    console.log("WebSocket连接已关闭");
  };

  // =========================================================================================
  // 在线状态相关
  // 更新单个用户的在线状态
  const updateUserOnlineStatus = (userId: string, isOnline: boolean) => {
    if (isOnline) {
      onlineUserIds.value.add(userId);
    } else {
      onlineUserIds.value.delete(userId);
    }
    updateUserInList(userId, isOnline);
  };
  // 更新界面显示的用户列表中指定用户的在线状态
  const updateUserInList = (userId: string, isOnline: boolean) => {
    const userIndex = users.value.findIndex(u => u.id === userId);
    if (userIndex !== -1) {
      users.value[userIndex].online_status = isOnline;
    }
  };
  // 向后端请求某一作战室中用户列表的在线状态
  const requestOnlineUsers = (roomId: string) => {
    if (!ws.value || ws.value.readyState !== WebSocket.OPEN) {
      return;
    }

    try {
      ws.value.send(
        JSON.stringify({
          type: "request_online_users",
          room_id: roomId
        })
      );
    } catch {}
  };

  // =========================================================================================
  // 2. 消息相关
  const messages = computed(() => {
    if (chatType.value === "public") {
      return publicMessages.value;
    } else if (chatType.value === "private") {
      return privateMessages.value[currentRoomId.value] || [];
    } else {
      return [];
    }
  });

  // 初代版本聊天室里用户私聊是不单独算一个房间的. 这个过滤看起来有点多余, 这里要改
  const filteredMessages = computed(() => {
    return messages.value.filter(
      msg => chatType.value === "private" || msg.room_id === currentRoomId.value
    );
  });

  // 消息发送处理
  const handleSendMessage = (
    content: string,
    imageContent?: string,
    type?: string
  ) => {
    if (!ws.value || ws.value.readyState !== WebSocket.OPEN) {
      ElMessage.error("WebSocket连接未就绪，请刷新页面重试");
      return;
    }

    try {
      if (type === "ai" || chatType.value === "ai") {
        const messageData = {
          type: "ai",
          content,
          image_content: imageContent || "",
          room_id: currentRoomId.value
        };
        ws.value.send(JSON.stringify(messageData));
      } else if (chatType.value === "public") {
        const messageData = {
          type: "public",
          content,
          image_content: imageContent || "",
          room_id: currentRoomId.value
        };
        ws.value.send(JSON.stringify(messageData));
      } else if (chatType.value === "private" && selectedUser.value) {
        const messageData = {
          type: "private",
          content,
          image_content: imageContent || "",
          receiver_id: selectedUser.value.id,
          room_id: currentRoomId.value
        };
        ws.value.send(JSON.stringify(messageData));
      }
    } catch {
      ElMessage.error("发送消息失败，请重试");
    }
  };

  const requestPublicHistory = (
    limit?: number,
    search?: string,
    roomId?: string
  ) => {
    const targetRoomId = roomId || currentRoomId.value;
    if (hasLoadedPublicHistory.value) return;

    isLoadingMessages.value = true;

    queryMessages({
      query_type: "public_history",
      room_id: targetRoomId,
      limit: limit || historyLimit.value,
      keyword: search || searchKeyword.value
    })
      .then((response: any) => {
        if (response.code === 0 && response.data) {
          publicMessages.value = response.data.messages.map((msg: any) => ({
            ...msg,
            is_recalled: msg.is_recalled || false
          }));
          hasLoadedPublicHistory.value = true;
          if (chatType.value === "public") {
            scrollToBottom();
          }
        } else {
          ElMessage.error("获取聊天历史失败");
        }
      })
      .catch(() => {
        ElMessage.error("获取聊天历史失败，请刷新页面重试");
      })
      .finally(() => {
        isLoadingMessages.value = false;
      });
  };

  const requestPrivateHistory = (
    limit?: number,
    search?: string,
    roomId?: string
  ) => {
    const targetRoomId = roomId || currentRoomId.value;
    if (
      privateMessages.value[targetRoomId] &&
      privateMessages.value[targetRoomId].length > 0
    ) {
      return;
    }

    isLoadingMessages.value = true;

    queryMessages({
      query_type: "private_history",
      room_id: targetRoomId,
      limit: limit || historyLimit.value,
      keyword: search || searchKeyword.value
    })
      .then((response: any) => {
        if (response.code === 0 && response.data) {
          privateMessages.value[targetRoomId] = response.data.messages.map(
            (msg: any) => ({
              ...msg,
              is_recalled: msg.is_recalled || false
            })
          );
          if (
            chatType.value === "private" &&
            currentRoomId.value === targetRoomId
          ) {
            scrollToBottom();
          }
        } else {
          ElMessage.error("获取私聊历史失败");
        }
      })
      .catch(() => {
        ElMessage.error("获取私聊历史失败，请刷新页面重试");
      })
      .finally(() => {
        isLoadingMessages.value = false;
      });
  };

  // 设置历史消息限制数量
  const setHistoryLimit = (limit: number) => {
    historyLimit.value = limit;
    hasLoadedPublicHistory.value = false;
    isLoadingMessages.value = true;

    if (chatType.value === "public") {
      publicMessages.value = [];
      requestPublicHistory(limit);
    } else if (selectedUser.value) {
      const partnerId = selectedUser.value.id;
      privateMessages.value[partnerId] = [];
      requestPrivateHistory(limit, undefined, partnerId);
    }
  };

  const searchHistory = (keyword: string) => {
    searchKeyword.value = keyword;
    searchResults.value = [];
    currentSearchIndex.value = -1;

    if (keyword.trim() === "") return;
    const messages =
      chatType.value === "public"
        ? publicMessages.value
        : selectedUser.value
          ? privateMessages.value[selectedUser.value.id] || []
          : [];

    messages.forEach(msg => {
      if (msg.content.includes(keyword)) {
        searchResults.value.push({
          message_id: msg.message_id,
          highlight: false
        });
      }
    });

    if (searchResults.value.length > 0) {
      navigateToSearchResult(0);
    }
  };
  // 本来是打算弄成搜索聊天记录, 在消息列表里高亮显示的, 后边单独做成一个搜索聊天记录的弹窗了. 哥们看着删吧
  const navigateToSearchResult = (index: number) => {
    if (index < 0 || index >= searchResults.value.length) return;

    if (currentSearchIndex.value >= 0) {
      searchResults.value[currentSearchIndex.value].highlight = false;
    }

    currentSearchIndex.value = index;
    searchResults.value[index].highlight = true;

    scrollToMessage(searchResults.value[index].message_id);
  };

  const scrollToMessage = (messageId: string) => {
    nextTick(() => {
      const messageElement = document.querySelector(
        `[data-message-id="${messageId}"]`
      );
      if (messageElement) {
        messageElement.scrollIntoView({ behavior: "smooth", block: "center" });
        messageElement.classList.add("search-highlight");
        setTimeout(() => {
          messageElement.classList.remove("search-highlight");
        }, 2000);
      }
    });
  };

  const searchMessages = async (params: {
    keyword: string;
    page?: number;
    pageSize?: number;
    roomId?: string;
    partnerId?: string;
  }) => {
    messageSearchLoading.value = true;

    const queryType =
      chatType.value === "private" ? "private_history" : "public_history";

    try {
      const response = (await queryMessages({
        query_type: queryType,
        room_id: params.roomId,
        keyword: params.keyword,
        limit: params.pageSize || 20,
        offset: ((params.page || 1) - 1) * (params.pageSize || 20)
      })) as any;

      if (response.code === 0) {
        messageSearchResults.value = response.data.messages;
        messageSearchTotal.value = response.data.pagination.total;
      } else {
        ElMessage.error("搜索消息失败");
      }
    } catch {
      ElMessage.error("搜索消息失败，请稍后重试");
    } finally {
      messageSearchLoading.value = false;
    }
  };

  const updateRecalledMessage = (messageId: string) => {
    const publicMsgIndex = publicMessages.value.findIndex(
      m => m.message_id === messageId
    );
    if (publicMsgIndex !== -1) {
      publicMessages.value[publicMsgIndex].is_recalled = true;
      publicMessages.value[publicMsgIndex].content = "[已撤回]";
      publicMessages.value[publicMsgIndex].image_content = undefined;
      return;
    }
    for (const partnerId in privateMessages.value) {
      const messages = privateMessages.value[partnerId];
      const msgIndex = messages.findIndex(m => m.message_id === messageId);
      if (msgIndex !== -1) {
        messages[msgIndex].is_recalled = true;
        messages[msgIndex].content = "[已撤回]";
        messages[msgIndex].image_content = undefined;
        return;
      }
    }
  };

  const handleRecall = (messageId: string) => {
    if (!ws.value || ws.value.readyState !== WebSocket.OPEN) {
      ElMessage.error("连接已断开，无法撤回消息");
      connectWebSocket();
      return;
    }
    try {
      ws.value.send(
        JSON.stringify({
          type: "recall",
          message_id: messageId
        })
      );
    } catch {
      ElMessage.error("撤回请求发送失败");
      connectWebSocket();
    }
  };

  const clearRoomMessages = () => {
    publicMessages.value = [];
    hasLoadedPublicHistory.value = false;
  };

  // =========================================================================================
  // 3. 用户相关
  const handleSelectUser = async (user: User) => {
    if (chatType.value === "private" && selectedUser.value?.id === user.id) {
      return;
    }

    const savedRoomId = currentRoomId.value;
    chatType.value = "private";
    selectedUser.value = user;

    try {
      const response = (await getPrivateRoom({
        partner_id: user.id
      })) as any;

      if (response.code === 0 && response.data) {
        const privateRoomId = response.data.room_id;
        // const privateName = response.data.name;

        if (privateRoomId) {
          if (privateRoomId === currentRoomId.value) {
            return;
          }
          if (router.currentRoute.value.params.roomId !== privateRoomId) {
            router.push(`/warroom/index/${privateRoomId}`);
          }
          currentRoomId.value = privateRoomId;
          requestPrivateHistory(historyLimit.value, undefined, privateRoomId);
        } else {
          currentRoomId.value = savedRoomId;
          ElMessage.error("无法创建私聊房间，请稍后再试");
        }
      } else {
        currentRoomId.value = savedRoomId;
        ElMessage.error(response.message || "获取私聊房间失败");
      }
    } catch {
      currentRoomId.value = savedRoomId;
      chatType.value = "public";
      selectedUser.value = null;
      ElMessage.error("获取私聊房间失败，请稍后再试");
      router.push(`/warroom/index/${savedRoomId}`);
      return;
    }
    if (!privateMessages.value[user.id]) {
      privateMessages.value[user.id] = [];
    }

    await nextTick();
    scrollToBottom();
  };

  // =========================================================================================
  // 4. 房间相关
  const fetchRooms = async () => {
    if (isFetchingRooms.value) {
      return;
    }

    // 检查用户是否已登录，如果未登录则不调用API
    const userInfo = storageLocal().getItem<DataInfo<number>>(userKey);
    if (!Cookies.get(multipleTabsKey) || !userInfo) {
      console.log("用户未登录，跳过获取作战室列表");
      return;
    }

    isFetchingRooms.value = true;
    try {
      const responseAll = (await getStarRooms({})) as any;
      if (responseAll.code === 0) {
        console.log("getRoomsList", responseAll);
        rooms.value = responseAll.data.star_rooms;
      }

      const responseUserRooms = (await getUserRooms()) as any;
      if (
        responseUserRooms.code === 0 &&
        responseUserRooms.data &&
        responseUserRooms.data.rooms
      ) {
        userRooms.value = responseUserRooms.data.rooms.map(
          (room: Room) => room.id
        );
      }
    } catch {
      ElMessage.error({
        message: "获取作战室列表失败",
        duration: 1000
      });
    } finally {
      isFetchingRooms.value = false;
    }
  };

  const isUserInRoom = (roomId: string): boolean => {
    return userRooms.value.includes(roomId);
  };

  const fetchRoomUsers = async (roomId: string) => {
    try {
      const response = (await getMembers({
        id: roomId
      })) as any;
      if (response.code === 0 && response.data && response.data.members) {
        users.value = response.data.members;
        requestOnlineUsers(roomId);
      }
    } catch {
      ElMessage.error("获取房间用户列表失败");
    }
  };

  const handleRoomChanged = async (roomId: string) => {
    if (router.currentRoute.value.params.roomId !== roomId) {
      router.push(`/warroom/index/${roomId}`);
    }
    currentRoomId.value = roomId;
    selectedUser.value = null;
    clearRoomMessages();

    const room = rooms.value.find(r => r.id === roomId);

    await fetchRoomUsers(roomId);

    if (room?.name?.includes("私聊")) {
      chatType.value = "private";
      const otherUsers = users.value.filter(u => u.id !== userStore.$state.id);
      if (otherUsers.length > 0) {
        selectedUser.value = otherUsers[0];
      }
      requestPrivateHistory(historyLimit.value, undefined, roomId);
    } else {
      chatType.value = "public";
      hasLoadedPublicHistory.value = false;
      requestPublicHistory(historyLimit.value, undefined, roomId);
    }

    // 切换房间后请求新房间的在线用户列表
    if (ws.value?.readyState === WebSocket.OPEN) {
      requestOnlineUsers(roomId);
    }
  };

  // =========================================================================================
  // 5. 其他
  const scrollToBottom = () => {
    setTimeout(() => {
      messageList.value?.scrollToBottom?.();
    }, 0);
  };

  const backToWelcomePage = () => {
    router.push(`/warroom/index`);
    chatType.value = undefined;
    selectedUser.value = null;
    currentRoomId.value = "";
    clearRoomMessages();
  };

  // 获取群公告列表
  const fetchAnnouncements = async (roomId: string, page = 1, limit = 10) => {
    if (!roomId) return;

    isLoadingAnnouncements.value = true;

    try {
      const response = (await queryMessages({
        query_type: "announcements",
        room_id: roomId,
        limit: limit,
        offset: (page - 1) * limit
      })) as any;

      if (response.code === 0 && response.data) {
        announcements.value = response.data.announcements;
        announcementsTotal.value = response.data.pagination.total;
      } else {
        ElMessage.error("获取公告列表失败");
      }
    } catch {
      ElMessage.error("获取公告列表失败，请稍后重试");
    } finally {
      isLoadingAnnouncements.value = false;
    }
  };

  const publishAnnouncement = async (
    content: string,
    roomId: string,
    imageContent?: string
  ) => {
    if (!ws.value || ws.value.readyState !== WebSocket.OPEN) {
      ElMessage.error("WebSocket连接未就绪，请刷新页面重试");
      return;
    }

    if (!content && !imageContent) {
      ElMessage.error("公告内容不能为空");
      return;
    }

    try {
      ws.value.send(
        JSON.stringify({
          type: "announcement",
          content: content,
          image_content: imageContent || "",
          room_id: roomId
        })
      );
    } catch {
      ElMessage.error("发布公告失败，请稍后重试");
    }
  };

  const deleteAnnouncement = async (announcementId: string) => {
    if (!ws.value || ws.value.readyState !== WebSocket.OPEN) {
      ElMessage.error("WebSocket连接未就绪，请刷新页面重试");
      return;
    }

    try {
      ws.value.send(
        JSON.stringify({
          type: "delete_announcement",
          announcement_id: announcementId
        })
      );
    } catch {
      ElMessage.error("删除公告失败，请稍后重试");
    }
  };

  return {
    ws,
    rooms,
    userRooms,
    currentRoomId,
    chatType,
    users,
    selectedUser,
    publicMessages,
    privateMessages,
    messages,
    filteredMessages,
    onlineUserIds,
    connectWebSocket,
    closeWebSocket, // 关闭WebSocket连接
    cleanupResources,
    fetchRooms,
    fetchRoomUsers,
    handleWebSocketMessage,
    startHeartbeat,
    handleSendMessage,
    handleRecall,
    handleSelectUser,
    handleRoomChanged,
    clearRoomMessages,
    setHistoryLimit,
    searchHistory,
    searchResults,
    currentSearchIndex,
    searchKeyword,
    navigateToSearchResult,
    scrollToMessage,
    historyLimit,
    messageSearchResults,
    messageSearchTotal,
    messageSearchLoading,
    searchMessages,
    isUserInRoom,
    backToWelcomePage,
    requestOnlineUsers,
    updateUserOnlineStatus,
    requestPublicHistory,
    requestPrivateHistory,
    isLoadingMessages,
    announcements,
    announcementsTotal,
    isLoadingAnnouncements,
    fetchAnnouncements,
    publishAnnouncement,
    deleteAnnouncement
  };
});
