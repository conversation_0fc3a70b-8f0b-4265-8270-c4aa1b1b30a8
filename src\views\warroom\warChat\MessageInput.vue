<template>
  <div class="message-input">
    <div class="toolbar">
      <!-- 执行动作 -->
      <Perms :value="['warroom:r']">
        <el-tooltip
          :hide-after="0"
          :show-after="50"
          content="执行动作"
          effect="light"
          placement="top"
        >
          <el-button class="tool-button" type="primary" @click="handleAction">
            <IconifyIconOffline
              class="icon"
              height="30px"
              icon="tabler:action"
              idth="30px"
            />
          </el-button>
        </el-tooltip>
      </Perms>
      <!-- 执行剧本 -->
      <Perms :value="['warroom:r']">
        <el-tooltip
          :hide-after="0"
          :show-after="50"
          content="执行剧本"
          effect="light"
          placement="top"
        >
          <el-button class="tool-button" type="primary" @click="handlePlaybook">
            <IconifyIconOffline
              class="icon"
              height="30px"
              icon="tabler:playbook"
              width="30px"
            />
          </el-button>
        </el-tooltip>
      </Perms>
      <el-tooltip
        :show-after="50"
        content="问AI"
        effect="light"
        placement="top"
      >
        <el-button class="tool-button" type="primary" @click="sendAi">
          <IconifyIconOffline
            class="icon"
            height="30px"
            icon="hugeicons:ai-chat-02"
            width="30px"
          />
        </el-button>
      </el-tooltip>
    </div>

    <!-- 富文本编辑器区域 -->
    <div class="editor-container">
      <!-- 富文本编辑器 -->
      <div class="editor-area">
        <Toolbar
          :defaultConfig="toolbarConfig"
          :editor="editorRef"
          :mode="mode"
          class="editor-toolbar-inner"
          style="display: none; height: 0"
        />
        <div class="editor-content-wrapper" @click="hideCommandMenu">
          <Editor
            v-model="valueHtml"
            :defaultConfig="editorConfig"
            :mode="mode"
            @onChange="handleChange"
            @onCreated="handleCreated"
          />

          <!-- 快捷指令菜单 -->
          <div
            v-show="showCommandMenu"
            :style="commandMenuStyle"
            class="command-menu"
          >
            <div
              v-for="(cmd, index) in filteredCommands"
              :key="cmd.command"
              :class="{ active: selectedCommandIndex === index }"
              class="command-item"
              @click="selectCommand(cmd)"
              @mouseover="selectedCommandIndex = index"
            >
              <div class="command-icon">
                <IconifyIconOffline
                  :icon="cmd.icon"
                  class="icon"
                  height="18px"
                  width="18px"
                />
              </div>
              <div class="command-content">
                <div class="command-line">
                  <span class="command-text">{{ cmd.command }}</span>
                  <span class="command-desc">{{ cmd.description }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 发送按钮内嵌到编辑器内部 -->
          <div class="send-button-container">
            <!-- <div
              class="ai-toggle"
              :class="{ selected: isAiSelected }"
              @click="handleAiToggle"
            >
              AI问答
            </div> -->
            <el-button type="primary" @click="sendMessage">发送</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 快捷指令帮助对话框 -->
    <el-dialog
      v-model="showCommandsHelp"
      :style="{ top: '5vh' }"
      destroy-on-close
      title="快捷指令帮助"
      width="400px"
    >
      <div class="commands-help-content">
        <p class="help-intro">在输入框中输入以下指令可快速触发相应功能</p>
        <p class="help-intro"><strong>Tip1:</strong>&nbsp;支持模糊匹配</p>
        <p class="help-intro">
          <strong>Tip2:</strong>&nbsp;支持按键 ↑ ↓ Enter 选中
        </p>
        <el-scrollbar class="commands-help-scrollbar">
          <div
            v-for="cmd in commands"
            :key="cmd.command"
            class="help-command-item"
          >
            <div class="help-command-icon">
              <IconifyIconOffline :icon="cmd.icon" height="20px" width="20px" />
            </div>
            <div class="help-command-text">
              <span class="help-command">{{ cmd.command }}</span>
              <span class="help-desc">{{ cmd.description }}</span>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </el-dialog>

    <!-- 动作抽屉 -->
    <ActionDrawer v-model="actionDrawerVisible" />
    <!-- 剧本抽屉 -->
    <playbook-drawer v-model="playbookDrawerVisible" />
    <!-- AI配置抽屉 -->
    <ai-config-drawer v-model="aiDrawerVisible" />
  </div>
</template>

<script lang="ts" setup>
import {
  onBeforeUnmount,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  shallowRef,
  watch
} from "vue";
import { ElLoading, ElMessage } from "element-plus";
import "@wangeditor/editor/dist/css/style.css";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import ActionDrawer from "@/views/warroom/drawer/actionDrawer.vue";
import playbookDrawer from "@/views/warroom/drawer/playbookDrawer.vue";
import aiConfigDrawer from "@/views/warroom/drawer/aiConfigDrawer.vue";
import IconifyIconOffline from "@/components/ReIcon/src/iconifyIconOffline";

const props = defineProps<{
  chatType: "public" | "private" | "ai";
  partnerId?: string;
  isAnnouncement?: boolean;
  roomId?: string;
}>();

const emit = defineEmits<{
  (e: "send", content: string, imageContent?: string, type?: string): void;
  (e: "update:chatType", value: "public" | "private" | "ai"): void;
}>();

const message = ref("");
const imageContent = ref("");

// 快捷指令相关
const showCommandMenu = ref(false);
const selectedCommandIndex = ref(0);
const commandMenuStyle = reactive({
  left: "20px",
  top: "50px"
});
const showCommandsHelp = ref(false);
const filteredCommands = ref([]);

const commands = ref([
  {
    command: "/ai",
    description: "问AI",
    icon: "tabler:ai",
    action: () => {}
  },
  {
    command: "/action",
    description: "执行动作",
    icon: "tabler:action",
    action: () => {
      actionDrawerVisible.value = true;
    }
  },
  {
    command: "/book",
    description: "执行剧本",
    icon: "tabler:playbook",
    action: () => {
      playbookDrawerVisible.value = true;
    }
  }
]);

// 初始化过滤后的命令列表
filteredCommands.value = [...commands.value];

// 富文本编辑器配置
const mode = "default";
const editorRef = shallowRef();
const valueHtml = ref("<p></p>");

const imgRegEx = /<img.*?>/gi;

// 工具栏配置，只保留图片上传功能
const toolbarConfig = {
  toolbarKeys: [],
  hideKeys: [
    "fullScreen",
    "insertTable",
    "codeBlock",
    "insertLink",
    "todo",
    "emotion",
    "fontFamily",
    "fontSize",
    "lineHeight",
    "bulletedList",
    "numberedList",
    "blockquote",
    "code",
    "clearStyle",
    "divider",
    "redo",
    "undo",
    "through",
    "italic",
    "bold",
    "color",
    "backgroundColor",
    "justifyCenter",
    "justifyLeft",
    "justifyRight",
    "indent",
    "delIndent",
    "header1",
    "header2",
    "header3",
    "uploadImage"
  ],
  excludeKeys: ["*"]
};

// 图片压缩方法
const compressImage = async (file: File): Promise<File> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = URL.createObjectURL(file);
    img.onload = () => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      const MAX_WIDTH = 1600;
      const MAX_HEIGHT = 1200;
      let { width, height } = img;

      if (width > height) {
        if (width > MAX_WIDTH) {
          height *= MAX_WIDTH / width;
          width = MAX_WIDTH;
        }
      } else {
        if (height > MAX_HEIGHT) {
          width *= MAX_HEIGHT / height;
          height = MAX_HEIGHT;
        }
      }

      canvas.width = width;
      canvas.height = height;
      ctx?.drawImage(img, 0, 0, width, height);

      const fileType = file.type || "image/jpeg";
      const mimeType = fileType === "image/png" ? "image/png" : "image/jpeg";

      canvas.toBlob(
        blob => {
          if (blob) {
            const newFile = new File([blob], file.name, {
              type: mimeType,
              lastModified: Date.now()
            });
            resolve(newFile);
          } else {
            reject(new Error("图片压缩失败"));
          }
        },
        mimeType,
        0.9
      );
    };
    img.onerror = () => reject(new Error("图片加载失败"));
  });
};

const editorConfig = {
  hoverbarKeys: {
    text: []
  },
  MENU_CONF: {
    uploadImage: {
      // server: "/api/upload/image",
      fieldName: "file",
      maxFileSize: 10 * 1024 * 1024,
      maxNumberOfFiles: 10,
      allowedFileTypes: ["image/*"],
      metaWithUrl: true,
      customUpload: async (file, insertFn) => {
        const loading = ElLoading.service({
          lock: true,
          text: "图片处理中...",
          background: "rgba(0, 0, 0, 0.7)"
        });

        try {
          const compressedFile = await compressImage(file);
          const reader = new FileReader();
          reader.onload = () => {
            const base64 = reader.result as string;
            imageContent.value = base64;
            insertFn(base64, file.name, base64);
            loading.close();
          };
          reader.onerror = () => {
            ElMessage.error("图片读取失败");
            loading.close();
          };
          reader.readAsDataURL(compressedFile);
        } catch (error) {
          ElMessage.error("图片处理失败");
          loading.close();
        }
      }
    }
  }
};

// 快捷指令相关方法
const toggleCommandsHelp = () => {
  showCommandsHelp.value = !showCommandsHelp.value;
};

const showMenuAtCursor = () => {
  try {
    const editor = editorRef.value;
    if (!editor) return;

    const selection = window.getSelection();
    if (!selection?.rangeCount) return;

    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();
    const editorRect = editor.getEditableContainer().getBoundingClientRect();

    // 计算相对于编辑器的位置 - 调整垂直位置
    commandMenuStyle.left = rect.left - editorRect.left + "px";
    commandMenuStyle.top = rect.top - editorRect.top - 220 + "px";
    // 确保菜单在可见区域内
    const menuElement = document.querySelector(".command-menu") as HTMLElement;
    if (menuElement) {
      const editorWidth = editorRect.width;
      const menuWidth = menuElement.offsetWidth;
      // 防止菜单超出右边界
      if (parseInt(commandMenuStyle.left) + menuWidth > editorWidth - 20) {
        commandMenuStyle.left = editorWidth - menuWidth - 20 + "px";
      }
      // 防止菜单超出上边界
      if (parseInt(commandMenuStyle.top) < 10) {
        commandMenuStyle.top = "10px";
      }
    }
  } catch (e) {
    console.error("定位菜单失败", e);
    commandMenuStyle.left = "20px";
    commandMenuStyle.top = "50px";
  }
};

const hideCommandMenu = () => {
  showCommandMenu.value = false;
  selectedCommandIndex.value = 0;
  // 重置过滤后的命令列表
  filteredCommands.value = [...commands.value];
};

const selectCommand = command => {
  // 如果命令存在且有动作函数, 则执行
  if (command && typeof command.action === "function") {
    command.action();
  }
  hideCommandMenu();
  editorRef.value?.focus();
};

const handleKeyboardNavigation = event => {
  if (!showCommandMenu.value) return;

  // 阻止导航相关键的默认行为
  const navigationKeys = ["Enter", "ArrowUp", "ArrowDown"];
  if (navigationKeys.includes(event.key)) {
    event.preventDefault();
  }
  // 键盘 ↑ ↓ enter esc 回退, 控制
  switch (event.key) {
    case "ArrowUp":
      selectedCommandIndex.value =
        (selectedCommandIndex.value - 1 + filteredCommands.value.length) %
        filteredCommands.value.length;
      break;
    case "ArrowDown":
      selectedCommandIndex.value =
        (selectedCommandIndex.value + 1) % filteredCommands.value.length;
      break;
    case "Enter":
      selectCommand(filteredCommands.value[selectedCommandIndex.value]);
      break;
    case "Escape":
      hideCommandMenu();
      break;
    case "Backspace":
      // 处理删除键 - 如果删除了"/"则关闭菜单
      setTimeout(() => {
        const text = editorRef.value?.getText() || "";
        if (!text.includes("/")) {
          hideCommandMenu();
        }
      }, 10);
      break;
    default:
      break;
  }
};

const handleCreated = editor => {
  editorRef.value = editor;

  // 添加配置，强制长文本自动换行
  editor.getConfig().MENU_CONF = {
    ...(editor.getConfig().MENU_CONF || {}),
    insertText: {
      wrap: true
    }
  };

  // 禁用选中文本时弹出的浮动工具栏
  editor.getConfig().hoverbarKeys = {
    text: []
  };

  editor.on("selectionChange", () => {
    const selection = editor.getSelectionText();
    if (selection) {
      // 当有选中文本时,确保视图自动滚动到视野内
      const elem = editor.getSelectionContainerElem()?.elems[0];
      if (elem) {
        elem.scrollIntoView({ behavior: "smooth", block: "center" });
      }
    }
  });

  const textArea = editor.getEditableContainer();
  if (textArea) {
    textArea.addEventListener("keydown", event => {
      // 如果命令菜单显示，并且按下的是导航键，交给handleKeyboardNavigation处理
      if (
        showCommandMenu.value &&
        ["Enter", "Escape", "ArrowUp", "ArrowDown"].includes(event.key)
      ) {
        handleKeyboardNavigation(event);
        return;
      }

      // 处理Enter发送消息
      if (event.key === "Enter") {
        if (
          event.ctrlKey ||
          (!event.shiftKey && props.isAnnouncement !== true)
        ) {
          event.preventDefault();
          sendMessage();
        }
      }
    });
  }
};

const handleChange = editor => {
  const text = editor.getText();
  message.value = text;

  // 处理"/"快捷指令菜单
  // if (text.includes("/")) {
  //   const lastSlashIndex = text.lastIndexOf("/");
  //   const prefix = text.substring(lastSlashIndex);
  //
  //   // 根据前缀过滤命令
  //   filteredCommands.value = commands.value.filter(cmd =>
  //     cmd.command.startsWith(prefix)
  //   );
  //
  //   if (filteredCommands.value.length > 0) {
  //     // 有匹配的命令，显示菜单
  //     showCommandMenu.value = true;
  //     selectedCommandIndex.value = 0;
  //     setTimeout(() => {
  //       showMenuAtCursor();
  //     }, 10);
  //   } else if (prefix === "/") {
  //     // 如果只输入了"/",显示所有命令
  //     filteredCommands.value = [...commands.value];
  //     showCommandMenu.value = true;
  //     selectedCommandIndex.value = 0;
  //     setTimeout(() => {
  //       showMenuAtCursor();
  //     }, 10);
  //   } else {
  //     // 没有匹配的命令，隐藏菜单
  //     hideCommandMenu();
  //   }
  // } else {
  //   // 删除"/"，隐藏菜单
  //   hideCommandMenu();
  // }

  const richText = valueHtml.value;
  valueHtml.value = richText.replace(imgRegEx, match => {
    return match.replace(/<img/, '<img style="width:15%;"');
  });
};

const handleGlobalKeyDown = event => {
  if (event.key === "Enter" && event.ctrlKey) {
    event.preventDefault();
    sendMessage();
  }

  // 添加ESC键处理
  if (event.key === "Escape" && showCommandMenu.value) {
    hideCommandMenu();
  }
};

onMounted(() => {
  document.addEventListener("keydown", handleGlobalKeyDown);
  document.addEventListener("click", e => {
    // 点击外部区域关闭命令菜单
    const commandMenu = document.querySelector(".command-menu");
    if (
      showCommandMenu.value &&
      commandMenu &&
      !commandMenu.contains(e.target as Node)
    ) {
      hideCommandMenu();
    }
  });
});

onUnmounted(() => {
  document.removeEventListener("keydown", handleGlobalKeyDown);
});

onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});

const isAiSelected = ref(props.chatType === "ai");
let lastChatType: "public" | "private" =
  props.chatType === "ai" ? "public" : props.chatType;

watch(
  () => props.chatType,
  val => {
    if (val === "ai") {
      isAiSelected.value = true;
    } else {
      isAiSelected.value = false;
      lastChatType = val as "public" | "private";
    }
  }
);

const handleAiToggle = () => {
  isAiSelected.value = !isAiSelected.value;
  if (isAiSelected.value) {
    emit("update:chatType", "ai");
  } else {
    emit("update:chatType", lastChatType);
  }
};

const sendMessage = async () => {
  const editorContent = editorRef.value?.getText() || "";

  if (!editorContent.trim() && !imageContent.value) {
    ElMessage.warning("消息内容不能为空");
    return;
  }

  try {
    emit(
      "send",
      editorContent.trim(),
      imageContent.value,
      isAiSelected.value ? "ai" : props.chatType
    );
    editorRef.value?.clear();
    valueHtml.value = "<p></p>";
    message.value = "";
    imageContent.value = "";
  } catch (error) {
    ElMessage.error(
      "发送失败: " + (error instanceof Error ? error.message : "未知错误")
    );
  }
};
// 发送AI消息
const sendAi = () => {
  const editorContent = editorRef.value?.getText() || "";

  if (!editorContent.trim() && !imageContent.value) {
    ElMessage.warning("消息内容不能为空");
    return;
  }

  try {
    emit("send", editorContent.trim(), imageContent.value, "ai");
    editorRef.value?.clear();
    valueHtml.value = "<p></p>";
    message.value = "";
    imageContent.value = "";
  } catch (error) {
    ElMessage.error(
      "发送失败: " + (error instanceof Error ? error.message : "未知错误")
    );
  }
};

// 添加抽屉状态
const actionDrawerVisible = ref(false);
const playbookDrawerVisible = ref(false);
const aiDrawerVisible = ref(false);

// 预留函数接口
const handleAction = () => {
  actionDrawerVisible.value = true;
};

const handlePlaybook = () => {
  playbookDrawerVisible.value = true;
};
</script>

<style scoped>
.message-input {
  background: #fff;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.toolbar {
  display: flex;
  gap: 18px;
  padding: 6px;
  padding-left: 20px;
  z-index: 1;
  justify-content: flex-start;
  background-color: white;
  border-bottom: none;
}

.toolbar .tool-button {
  padding: 0;
  width: 36px;
  height: 36px;
  background-color: white;
  border-radius: 4px;
  transition: all 0.3s;
  margin: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 0;
  color: #2c7fe4 !important;
}

.toolbar .tool-button:hover,
.toolbar .el-button.tool-button:focus,
.toolbar .el-button.tool-button:active {
  background-color: #396eec !important;
  color: white !important;
  border-color: #396eec !important;
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.toolbar .el-button--primary.is-plain:hover,
.toolbar .el-button--primary.is-plain:focus {
  background-color: #396eec !important;
  border-color: #396eec !important;
  color: white !important;
}

:deep(.iconify) {
  color: #396eec !important; /* 默认图标色 */
}

.toolbar .tool-button:hover .iconify,
.toolbar .tool-button:focus .iconify,
.toolbar .tool-button:active .iconify {
  color: #fff !important; /* hover/active 时变白 */
}

/* 快捷指令菜单样式 */
.command-menu {
  position: absolute;
  z-index: 1000;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  width: 250px;
  max-height: 20vh;
  overflow-y: auto;
  padding: 8px 0;
  border: 1px solid #ebeef5;
}

.command-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.command-item:hover,
.command-item.active {
  background-color: #f5f7fa;
}

.command-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: #2c7fe4;
  width: 24px;
  height: 24px;
}

.command-content {
  flex: 1;
}

.command-line {
  display: flex;
  align-items: center;
  gap: 8px;
}

.command-text {
  font-weight: 500;
  color: #303133;
  white-space: nowrap;
}

.command-desc {
  font-size: 12px;
  color: #909399;
}

/* 快捷指令帮助弹窗样式 */
.commands-help-content {
  padding: 10px;
}

.help-intro {
  font-size: 15px;
  color: #606266;
  margin-bottom: 10px;
}

.help-command-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.help-command-item:hover {
  background-color: #f5f7fa;
}

.help-command-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: #2c7fe4;
  width: 28px;
  height: 28px;
}

.help-command-text {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
}

.help-command {
  font-weight: bold;
  color: #303133;
  white-space: nowrap;
}

.help-desc {
  font-size: 13px;
  color: #606266;
}

.commands-help-scrollbar {
  margin-top: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.commands-help-scrollbar :deep(.el-scrollbar__wrap) {
  padding: 10px;
}

.commands-help-scrollbar :deep(.el-scrollbar__bar) {
  opacity: 0.6;
}

.commands-help-scrollbar :deep(.el-scrollbar__bar:hover) {
  opacity: 1;
}

.editor-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.editor-area {
  flex: 1;
  margin: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.editor-toolbar-inner {
  border-bottom: none !important;
  background-color: #f5f7fa;
  padding: 0;
  margin: 0;
}

:deep(.w-e-scroll::-webkit-scrollbar) {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

:deep(.w-e-scroll) {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
  overflow: auto !important;
}

:deep(*::-webkit-scrollbar) {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

:deep(*) {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

:deep(.w-e-hoverbar) {
  display: none !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

:deep(.w-e-bar):before {
  content: "";
  display: block;
  width: 12px;
  height: 1px;
}

:deep(.w-e-toolbar),
:deep(.editor-toolbar-inner),
:deep([data-slate-editor]) ~ div {
  display: none !important;
  height: 0 !important;
  min-height: 0 !important;
  visibility: hidden !important;
  opacity: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
}

:deep(.w-e-bar) {
  padding: 0 !important;
  margin: 0 !important;
  justify-content: flex-start !important;
}

:deep(.w-e-bar-item) {
  padding: 5px !important;
  margin-top: 0 !important;
}

.editor-content-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  margin: 0;
  border: none;
  overflow: hidden;
  width: 100%;
  padding-right: 45px;
  box-sizing: border-box;
}

.send-button-container {
  position: absolute;
  bottom: 15px;
  right: 15px;
  z-index: 100;
  pointer-events: auto;
  background-color: transparent;
  margin: 0;
  padding: 0;
  border-radius: 4px;
  display: flex;
  text-align: center;
  justify-content: center;
}

.send-button-container .el-button {
  background-color: #396eec !important;
  border-color: #396eec !important;
  color: #fff !important;
  transition: all 0.3s;
  white-space: nowrap !important;
  min-width: 80px !important;
  font-size: 15px !important;
  display: inline-block !important;
  text-align: center !important;
}

.send-button-container .el-button:hover,
.send-button-container .el-button:focus,
.send-button-container .el-button:active {
  background-color: #5684f0 !important;
  border-color: #5684f0 !important;
  color: white !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.w-e-text-container) {
  padding-right: 70px !important;
  font-family: "微软雅黑" !important;
  font-size: 16px !important;
  border: none !important;
  margin: 0 !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  height: 100% !important; /* 确保占满高度 */
}

:deep(.w-e-text) {
  word-break: break-word !important;
  word-wrap: break-word !important;
  white-space: pre-wrap !important;
  overflow-wrap: break-word !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

:deep(.w-e-text-container[contenteditable="true"]) {
  white-space: pre-wrap !important; /* 允许空格和换行 */
  word-break: break-all !important;
  overflow-wrap: break-word !important; /* 防止空白截断 */
}

:deep(.w-e-text p) {
  max-width: 100% !important;
  overflow-x: hidden !important;
  white-space: pre-wrap !important; /* 保留空白符 */
}

.is-selected {
  background-color: #2c7fe4 !important;
  color: #fff !important;
}

.icon {
  color: black !important; /* 始终黑色 */
}

.toolbar .tool-button:hover .icon,
.toolbar .tool-button:focus .icon {
  color: #fff !important; /* 悬停时也不变色 */
}
</style>
