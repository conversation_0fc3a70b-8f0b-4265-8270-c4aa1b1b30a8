<template>
  <div class="binding-container">
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleClose"
      :destroy-on-close="true"
      :lock-scroll="true"
      class="binding-dialog"
      title="事件生成与绑定剧本"
      top="4vh"
      width="86vw"
    >
      <div v-loading="loading" style="min-height: 200px">
        <div class="binding-main-layout">
          <div class="binding-edit-area">
            <el-scrollbar style="height: 100%">
              <div class="dialog-content-multi">
                <div
                  v-for="(formData, idx) in formDataList"
                  :key="idx"
                  class="row-group"
                >
                  <!-- 左侧表单 -->
                  <div class="form-col">
                    <el-form
                      ref="ruleForm"
                      :model="formData"
                      :rules="rules"
                      class="form-left"
                      label-width="140px"
                    >
                      <el-form-item label="事件名称">
                        <el-tooltip
                          :hide-after="0"
                          content="选择字段或输入自定义内容来构建事件名称"
                          placement="top"
                        >
                          <el-icon style="margin-right: 8px">
                            <QuestionFilled />
                          </el-icon>
                        </el-tooltip>
                        <el-select
                          v-model="formData.selectedFields"
                          allow-create
                          default-first-option
                          filterable
                          multiple
                          placeholder="请选择字段或输入自定义内容"
                          style="width: 400px"
                        >
                          <el-option
                            v-for="field in getLogFields()"
                            :key="field"
                            :label="field"
                            :value="field"
                          >
                            <template #default>
                              <span>{{ field }}</span>
                              <el-tooltip
                                :content="getLogFieldValue(field)"
                                placement="top"
                                :show-after="500"
                              >
                                <span
                                  style="
                                    float: right;
                                    color: #888;
                                    font-size: 12px;
                                    max-width: 200px;
                                    overflow: hidden;
                                    text-overflow: ellipsis;
                                    white-space: nowrap;
                                  "
                                >
                                  {{ getLogFieldValue(field) }}
                                </span>
                              </el-tooltip>
                            </template>
                          </el-option>
                        </el-select>
                      </el-form-item>
                      <!-- 事件名称预览 -->
                      <el-form-item label="事件名称预览：">
                        <div class="custom-name-preview-block">
                          <div class="custom-name-preview-content">
                            <span
                              v-if="formData.selectedFields.length"
                              class="custom-name-preview-code"
                            >
                              {{
                                formData.selectedFields
                                  .map(f =>
                                    getLogFields().includes(f)
                                      ? getLogFieldValue(f)
                                      : f
                                  )
                                  .join("")
                              }}
                            </span>
                            <span v-else class="custom-name-preview-empty"
                              >暂无自定义名称</span
                            >
                          </div>
                        </div>
                      </el-form-item>

                      <!-- 判断条件 -->
                      <el-form-item label="判断条件：" prop="judge">
                        <div class="condition-builder">
                          <div class="conditions-container">
                            <!-- 条件列表 -->
                            <div
                              v-if="
                                Array.isArray(formData.conditions) &&
                                formData.conditions.length > 0
                              "
                              class="condition-section"
                            >
                              <div
                                v-for="(
                                  condition, index
                                ) in formData.conditions"
                                :key="index"
                                class="condition-item"
                              >
                                <div class="condition-row">
                                  <!-- 只有 index > 0 时显示 connect_type 下拉 -->
                                  <el-select
                                    v-if="index > 0"
                                    v-model="condition.connect_type"
                                    class="condition-connect"
                                    placeholder="选择判断条件"
                                  >
                                    <el-option
                                      v-for="option in getConnectTypes()"
                                      :key="option.value"
                                      :label="option.label"
                                      :value="option.value"
                                    />
                                  </el-select>
                                  <div v-else style="width: 100px" />

                                  <div class="condition-name">
                                    <el-input
                                      v-model="condition.name"
                                      placeholder="字段名称"
                                    />
                                  </div>

                                  <el-select
                                    v-model="condition.type"
                                    class="condition-type"
                                    placeholder="选择条件类型"
                                    @change="onConditionTypeChange(condition)"
                                  >
                                    <el-option
                                      v-for="option in getConditionTypes()"
                                      :key="option.value"
                                      :label="option.label"
                                      :value="option.value"
                                    />
                                  </el-select>

                                  <template
                                    v-if="
                                      condition.type === 'includes' ||
                                      condition.type === '!includes'
                                    "
                                  >
                                    <div class="condition-value">
                                      <el-tooltip
                                        :hide-after="0"
                                        content='请输入集合，如 ["abc", "lmd"]'
                                        placement="bottom"
                                      >
                                        <el-input
                                          v-model="condition.value"
                                          :prop="`conditions.${index}.value`"
                                          placeholder=""
                                          @blur="
                                            validateIncludesValue(condition)
                                          "
                                        />
                                      </el-tooltip>
                                    </div>
                                  </template>
                                  <template
                                    v-else-if="condition.type === 'match'"
                                  >
                                    <div class="condition-value">
                                      <el-tooltip
                                        :hide-after="0"
                                        content="请输入正则表达式，如 ^abc.*$"
                                        placement="bottom"
                                      >
                                        <el-input
                                          v-model="condition.value"
                                          @blur="validateMatchValue(condition)"
                                        />
                                      </el-tooltip>
                                    </div>
                                  </template>
                                  <template v-else>
                                    <div class="condition-value">
                                      <el-input
                                        v-model="condition.value"
                                        :disabled="
                                          condition.type === 'is_empty' ||
                                          condition.type === '!is_empty' ||
                                          condition.type === 'exists' ||
                                          condition.type === '!exists'
                                        "
                                        :type="
                                          ['>', '<', '>=', '<='].includes(
                                            condition.type
                                          )
                                            ? 'number'
                                            : 'text'
                                        "
                                        :placeholder="
                                          condition.type === 'is_empty' ||
                                          condition.type === '!is_empty' ||
                                          condition.type === 'exists' ||
                                          condition.type === '!exists'
                                            ? '当前条件类型无需参数'
                                            : '请输入参数'
                                        "
                                      />
                                    </div>
                                  </template>

                                  <el-button
                                    circle
                                    type="danger"
                                    @click="removeCondition(idx, index)"
                                  >
                                    <el-icon>
                                      <Delete />
                                    </el-icon>
                                  </el-button>
                                </div>
                              </div>
                            </div>
                            <!-- 添加条件按钮 -->
                            <div class="add-condition-wrapper">
                              <el-button
                                plain
                                type="primary"
                                @click="handleAddCondition(idx)"
                              >
                                <el-icon>
                                  <Plus />
                                </el-icon>
                                添加判断条件
                              </el-button>
                            </div>
                          </div>
                        </div>
                      </el-form-item>
                      <!-- 自动执行剧本 -->
                      <el-form-item label="自动执行剧本">
                        <el-tooltip
                          :hide-after="0"
                          content="开启后可配置剧本数据"
                          placement="top"
                        >
                          <el-icon style="margin-right: 8px">
                            <QuestionFilled />
                          </el-icon>
                        </el-tooltip>
                        <el-switch
                          v-model="formData.linkedExecution"
                          :active-value="1"
                          :inactive-value="0"
                          active-text="开启"
                          inactive-text="关闭"
                          inline-prompt
                          style="margin-right: 8px"
                        />
                      </el-form-item>
                      <!-- 剧本ID -->
                      <el-form-item
                        v-if="formData.linkedExecution === 1"
                        label="选择剧本："
                      >
                        <el-select
                          v-model="formData.playbooks"
                          filterable
                          multiple
                          placeholder="请指定剧本"
                          style="width: 100%"
                          @change="getPlaybookversions(formData.playbooks)"
                        >
                          <el-option
                            v-for="item in scriptOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                          <!-- 加载更多按钮 -->
                          <template #footer>
                            <div
                              v-if="bookPage < bookTotalPages"
                              style="text-align: center; padding: 8px 0"
                            >
                              <el-button
                                :loading="isBookLoading"
                                size="small"
                                style="width: 90%"
                                @click.stop="loadMorePlaybooks"
                                >点击加载更多
                              </el-button>
                            </div>
                            <div
                              v-else
                              style="
                                text-align: center;
                                color: #aaa;
                                padding: 8px 0;
                              "
                            >
                              没有更多了
                            </div>
                          </template>
                        </el-select>
                      </el-form-item>
                      <!-- 剧本版本 -->
                      <el-form-item
                        v-if="formData.linkedExecution === 1"
                        label="选择版本："
                      >
                        <el-select
                          v-model="formData.scriptIds"
                          filterable
                          multiple
                          placeholder="请指定剧本的版本"
                          style="width: 100%"
                        >
                          <el-option
                            v-for="item in playbookversions"
                            :key="item.value"
                            :disabled="
                              getDisabledScriptIds(idx).includes(item.value)
                            "
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                      <!-- 去重部分 -->
                      <el-form-item label="去重：">
                        <el-tooltip
                          :hide-after="0"
                          content="开启后可配置去重条数和字段"
                          placement="top"
                        >
                          <el-icon style="margin-right: 8px">
                            <QuestionFilled />
                          </el-icon>
                        </el-tooltip>
                        <el-switch
                          v-model="formData.deduplicationEnabled"
                          :active-value="true"
                          :inactive-value="false"
                          active-text="开启"
                          inactive-text="关闭"
                          inline-prompt
                          style="margin-right: 8px"
                        />
                      </el-form-item>
                      <!-- 去重条数配置 -->
                      <el-form-item
                        v-if="formData.deduplicationEnabled"
                        label="去重条数："
                      >
                        <el-input-number
                          v-model="formData.deduplicationNumber"
                          :max="10000"
                          :min="1"
                          style="width: 200px"
                        />
                      </el-form-item>
                      <!-- 去重字段配置 -->
                      <el-form-item
                        v-if="formData.deduplicationEnabled"
                        label="去重字段："
                      >
                        <el-select
                          v-model="formData.deduplicationFields"
                          filterable
                          multiple
                          placeholder="请选择去重字段"
                          style="width: 400px"
                        >
                          <el-option
                            v-for="field in getLogFields()"
                            :key="field"
                            :label="field"
                            :value="field"
                          >
                            <template #default>
                              <span>{{ field }}</span>
                              <el-tooltip
                                :content="getLogFieldValue(field)"
                                placement="top"
                                :show-after="500"
                              >
                                <span
                                  style="
                                    float: right;
                                    color: #888;
                                    font-size: 12px;
                                    max-width: 200px;
                                    overflow: hidden;
                                    text-overflow: ellipsis;
                                    white-space: nowrap;
                                  "
                                >
                                  {{ getLogFieldValue(field) }}
                                </span>
                              </el-tooltip>
                            </template>
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-form>
                    <!-- 只在最后一组下方显示添加按钮 -->
                    <el-button
                      v-if="idx === formDataList.length - 1"
                      plain
                      style="margin-top: 8px"
                      type="primary"
                      @click="addGroup"
                      >添加分组
                    </el-button>
                  </div>
                  <!-- 右侧删除按钮 -->
                  <div class="action-col">
                    <el-button
                      v-if="formDataList.length > 1"
                      style="margin-top: 8px"
                      type="danger"
                      @click.stop="removeGroup(idx)"
                      >删除本组
                    </el-button>
                  </div>
                </div>
              </div>
            </el-scrollbar>
          </div>
          <!-- 右侧预览 -->
          <div class="preview-col">
            <div class="condition-preview">
              <h4>解析后的数据预览：</h4>
              <pre class="condition-preview-content">{{
                getLogPreviewContent()
              }}</pre>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="danger" @click="confirmUnbind">解绑剧本</el-button>
          <el-button @click="handleClose">取消</el-button>
          <el-button
            :disabled="!logs || logs.length === 0"
            type="primary"
            @click="handleConfirm"
            >确认
          </el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="showFieldDialog" title="选择字段" width="400px">
      <el-checkbox-group v-model="selectedFields">
        <el-checkbox v-for="field in getLogFields()" :key="field" :label="field"
          >{{ field }}
        </el-checkbox>
      </el-checkbox-group>
      <template #footer>
        <el-button @click="showFieldDialog = false">取消</el-button>
        <el-button type="primary" @click="showFieldDialog = false"
          >确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { defineEmits, defineProps, ref, watch } from "vue";
import { Delete, Plus, QuestionFilled } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox, type FormRules } from "element-plus";
import {
  eventIngestionBindScript,
  eventIngestionEditDetail,
  eventIngestionOriginalLog,
  eventIngestionUnbindScript
} from "@/api/event";
import { apiGetPlaybookList, apiGetPlaybookVersionsList } from "@/api/playbook";
import "../css/binding.scss";
import { RegExpParser } from "regexpp";

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  eventId: {
    type: [String, Number],
    default: ""
  },
  eventName: {
    type: String,
    default: ""
  }
});

// 定义组件事件
const emit = defineEmits(["update:visible", "confirm"]);

// 对话框可见性
const dialogVisible = ref(false);

// 剧本相关数据
const scriptOptions = ref([]);
const playbookversions = ref([]);
const bookPage = ref(1);
const bookPageSize = ref(100);
const bookTotal = ref(0);
const bookTotalPages = ref(1);
const isBookLoading = ref(false);
const searchQuery = ref("");

// 日志数据
const logData = ref([]);

// 保存datetime_str
const datetimeStr = ref("");

// 事件接入配置列表
const EditDetails = ref([]);

// 表单数据
const formDataList = ref([
  {
    conditions: [{ type: "", name: "", value: "", connect_type: "&&" }],
    eventIngestionId: "",
    scriptIds: [],
    selectedFields: [],
    logs: [],
    playbooks: [],
    linkedExecution: 0, // 新增：关联执行状态，0-关闭，1-开启
    id: "", // 新增：每组的ID
    deduplicationEnabled: false, // 新增：去重开关
    deduplicationNumber: 5000, // 新增：去重条数
    deduplicationFields: [] // 新增：去重字段
  }
]);

// 表单验证规则
const rules: FormRules = {
  eventIngestionId: [
    { required: true, message: "请输入事件接入ID", trigger: "blur" }
  ]
};

// 获取条件类型选项
const getConditionTypes = () => {
  return [
    { label: "包含字符串", value: "contains" },
    { label: "不包含字符串", value: "!contains" },
    { label: "存在集合", value: "includes" },
    { label: "不存在集合", value: "!includes" },
    { label: "存在", value: "exists" },
    { label: "不存在", value: "!exists" },
    { label: "为空 ", value: "is_empty" },
    { label: "不为空", value: "!is_empty" },
    { label: "正则表达式", value: "match" },
    { label: "等于", value: "==" },
    { label: "不等于", value: "!=" },
    { label: "大于", value: ">" },
    { label: "小于", value: "<" },
    { label: "大于等于", value: ">=" },
    { label: "小于等于", value: "<=" }
  ];
};

// 获取判断条件类型选项
const getConnectTypes = () => {
  return [
    { label: "且", value: "&&" },
    { label: "或", value: "||" }
  ];
};

// 添加条件
const handleAddCondition = idx => {
  formDataList.value[idx].conditions.push({
    type: "",
    name: "",
    value: "",
    connect_type: "&&"
  });
};

// 移除条件
const removeCondition = (idx, index) => {
  const conditions = formDataList.value[idx].conditions;
  if (conditions.length > 1) {
    conditions.splice(index, 1);
    // 删除后只剩一条，确保第一条没有 connect_type
    if (conditions.length === 1 && "connect_type" in conditions[0]) {
      delete conditions[0].connect_type;
    }
  }
};

// 获取日志字段
const getLogFields = () => {
  if (!logs.value || !logs.value[0]) return [];
  let logObj;
  if (typeof logs.value[0] === "string") {
    try {
      logObj = JSON.parse(logs.value[0]);
    } catch (error) {
      // console.warn("日志数据不是有效的JSON格式，使用原始数据:", logs.value[0]);
      // 如果不是JSON格式，直接返回原始字符串作为字段
      return ["raw_data"];
    }
  } else {
    logObj = logs.value[0];
  }

  // 如果存在log字段，则使用log字段的键
  if ("log" in logObj && logObj.log) {
    return Object.keys(logObj.log);
  }
  // 如果不存在log字段，则返回空数组（无数据）
  return [];
};

// 获取字段值（字符串化，避免对象/数组显示异常）
const getLogFieldValue = (field: string) => {
  if (!logs.value || !logs.value[0]) return "";
  let logObj;
  if (typeof logs.value[0] === "string") {
    try {
      logObj = JSON.parse(logs.value[0]);
    } catch (error) {
      // console.warn("日志数据不是有效的JSON格式，使用原始数据:", logs.value[0]);
      // 如果不是JSON格式，当字段是raw_data时返回原始数据
      if (field === "raw_data") {
        return logs.value[0];
      }
      return "";
    }
  } else {
    logObj = logs.value[0];
  }

  let val;
  // 如果存在log字段，则从log字段中获取值
  if ("log" in logObj && logObj.log) {
    val = logObj.log[field];
  } else {
    // 如果不存在log字段，则从原始对象中获取值
    val = logObj[field];
  }

  if (val === undefined || val === null) return "";
  if (typeof val === "object") return JSON.stringify(val);
  return String(val);
};

// 监听visible属性变化
watch(
  () => props.visible,
  newVal => {
    dialogVisible.value = newVal;
    if (newVal) {
      getLogs();
      getEditDetails();
    }
  }
);

// 重置表单
const resetForm = () => {
  // 构建默认事件名称："事件名称" + "_" + to_string!(.datetime_str)
  let defaultSelectedFields = [];
  if (
    props.eventName &&
    logs.value &&
    logs.value.length > 0 &&
    typeof logs.value[0] !== "string"
  ) {
    // 添加事件名称字段
    defaultSelectedFields.push(props.eventName);
    // 添加下划线分隔符
    defaultSelectedFields.push("_");
    // 添加时间戳字段 datetime_str
    defaultSelectedFields.push("datetime_str");
  }

  formDataList.value[0] = {
    conditions: [
      {
        type: "",
        name: "",
        value: "",
        connect_type: ""
      }
    ],
    eventIngestionId: String(props.eventId),
    scriptIds: [],
    selectedFields: defaultSelectedFields,
    logs: [],
    playbooks: [],
    linkedExecution: 0,
    id: "",
    deduplicationEnabled: false,
    deduplicationNumber: 5000,
    deduplicationFields: []
  };
  getPlaybookList();
};

// 监听对话框可见性变化
watch(
  () => dialogVisible.value,
  newVal => {
    emit("update:visible", newVal);
  }
);

// 关闭对话框
const handleClose = () => {
  // 重置绑定组，只保留一组初始数据
  formDataList.value = [
    {
      conditions: [
        {
          type: "",
          name: "",
          value: "",
          connect_type: ""
        }
      ],
      eventIngestionId: "",
      scriptIds: [],
      selectedFields: [],
      logs: [],
      playbooks: [],
      linkedExecution: 0,
      id: "",
      deduplicationEnabled: false,
      deduplicationNumber: 5000,
      deduplicationFields: []
    }
  ];
  // 其他需要重置的内容
  scriptOptions.value = [];
  // 重置datetime_str
  datetimeStr.value = "";
  // 关闭弹窗
  dialogVisible.value = false;
};

const handleConfirm = async () => {
  if (!logs.value || logs.value.length === 0) {
    ElMessage.warning("当前不存在日志数据，请检查数据解析配置或等待数据接入");
    return;
  }
  // 如果日志数据是字符串（如"当前不存在日志"），提示用户
  if (logs.value.length === 1 && typeof logs.value[0] === "string") {
    ElMessage.warning("当前没有可用的日志数据，无法进行绑定操作");
    return;
  }
  // 检查是否有开启状态的自动执行剧本
  const hasLinkedExecution = formDataList.value.some(
    formData => formData.linkedExecution === 1
  );

  if (hasLinkedExecution) {
    try {
      await ElMessageBox.confirm(
        "检测到存在开启状态的自动执行剧本，保存后自动执行剧本将关闭，确认继续吗？",
        "自动执行剧本确认",
        {
          confirmButtonText: "确认保存",
          cancelButtonText: "取消",
          type: "warning"
        }
      );
    } catch {
      // 用户取消，直接返回
      return;
    }
  }

  loading.value = true; // 新增loading状态
  try {
    for (const formData of formDataList.value) {
      for (const condition of formData.conditions) {
        // 1. 校验类型必选
        if (!condition.type || String(condition.type).trim() === "") {
          ElMessage.error(`判断条件"${condition.name || ""}"的类型必须选择`);
          loading.value = false;
          return;
        }
        // 2. 除了is_empty、!is_empty、exists和!exists，value必填
        if (
          condition.type !== "is_empty" &&
          condition.type !== "!is_empty" &&
          condition.type !== "exists" &&
          condition.type !== "!exists" &&
          (condition.value === undefined ||
            condition.value === null ||
            String(condition.value).trim() === "")
        ) {
          ElMessage.error(`判断条件"${condition.name || ""}"的参数不能为空`);
          loading.value = false;
          return;
        }
        // 3. match类型正则校验（原有逻辑）
        if (condition.type === "match") {
          if (!condition.value || !condition.value.trim()) {
            ElMessage.error("正则表达式不能为空");
            loading.value = false; // 校验失败也要重置loading
            return;
          }
          try {
            const parser = new RegExpParser();
            parser.parsePattern(condition.value);
          } catch (e) {
            ElMessage.error("请输入合法的正则表达式");
            loading.value = false;
            return;
          }
        }
      }
    }
    const binding_data = formDataList.value.map(formData => {
      const rawConditions = formData.conditions;
      let newConditions = [];
      if (rawConditions.length === 1) {
        let { type, name, value } = rawConditions[0] as {
          type: string;
          name: string;
          value: string | number;
        };
        if (type === "includes" || type === "!includes") {
          try {
            const arr = JSON.parse(value as string);
            value = JSON.stringify(arr);
          } catch (e) {
            // 保持原样
          }
        }
        newConditions = [{ type, name, value }];
      } else {
        for (let i = 0; i < rawConditions.length; i++) {
          let { type, name, value } = rawConditions[i] as {
            type: string;
            name: string;
            value: string | number;
          };
          if (type === "includes" || type === "!includes") {
            try {
              const arr = JSON.parse(value as string);
              value = JSON.stringify(arr);
            } catch (e) {
              // 保持原样
            }
          }
          let condition = {
            type,
            name,
            value,
            ...(i < rawConditions.length - 1
              ? { connect_type: rawConditions[i + 1].connect_type }
              : {})
          };
          newConditions.push(condition);
        }
      }
      const group: any = {
        conditions: newConditions,
        event_name:
          formData.selectedFields.length > 0
            ? formData.selectedFields
                .map(f => {
                  // 检查是否是默认的事件名称（props.eventName）
                  if (f === props.eventName) {
                    return `"${f.replace(/"/g, '\\"')}"`;
                  }
                  // 检查是否是下划线分隔符
                  else if (f === "_") {
                    return `"_"`;
                  }
                  // 检查是否是时间戳字段 datetime_str
                  else if (f === "datetime_str") {
                    return `to_string!(.datetime_str)`;
                  }
                  // 检查是否是日志字段 abc
                  else if (f === "abc") {
                    return `to_string!(.log.abc)`;
                  }
                  // 其他日志字段统一加 .log 前缀
                  else if (getLogFields().includes(f)) {
                    return `to_string!(.log.${f})`;
                  }
                  // 自定义内容
                  else {
                    return `"${f.replace(/"/g, '\\"')}"`;
                  }
                })
                .join(" + ")
            : "",
        script_ids: formData.scriptIds || [],
        linked_execution: formData.linkedExecution || 0
      };

      // 如果开启了去重，添加去重配置
      if (formData.deduplicationEnabled) {
        group.numbers = formData.deduplicationNumber;
        group.fields = [...formData.deduplicationFields]; // 使用展开运算符转换为纯数组
      }
      return group;
    });
    const submitData = {
      event_ingestion_id: props.eventId,
      binding_data
    };
    console.log(submitData);
    // const res: any = await eventIngestionBindScript(submitData);
    // if (res.code === 0) {
    //   ElMessage.success("绑定成功");
    //   // 重新获取数据，更新自动执行剧本的显示状态
    //   await getEditDetails();
    // } else {
    //   ElMessage.error(res.message);
    // }
  } finally {
    loading.value = false; // 无论成功失败都重置loading
  }
};

// 解绑剧本
const confirmUnbind = () => {
  ElMessageBox.confirm("确定要解绑该剧本吗？解绑后不可恢复！", "解绑确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      handleUnbind();
    })
    .catch(() => {
      // 用户取消，无需处理
    });
};
const handleUnbind = async () => {
  // 合并所有组的剧本版本id
  const allScriptIds = formDataList.value
    .map(formData => formData.scriptIds)
    .flat();
  console.log("所有选中的剧本版本ID:", allScriptIds);
  //解绑接口调用示例
  const res: any = await eventIngestionUnbindScript({
    event_ingestion_id: props.eventId,
    script_ids: allScriptIds
  });
  if (res.code === 0) {
    ElMessage.success("解绑成功");
    // 重新获取数据，更新自动执行剧本的显示状态
    await getEditDetails();
  } else {
    ElMessage.error(res.message);
  }
};

// 获取剧本列表
const getPlaybookList = async (isLoadMore = false) => {
  isBookLoading.value = true;
  try {
    const res: any = await apiGetPlaybookList({
      page: bookPage.value,
      size: bookPageSize.value,
      keyword: searchQuery.value
    });
    if (res?.data?.data) {
      const newOptions = res.data.data.map(item => ({
        label: item.name,
        value: item.id
      }));
      console.log(res.data);
      // 如果是加载更多，则追加数据，否则替换数据
      if (isLoadMore) {
        scriptOptions.value = [...scriptOptions.value, ...newOptions];
      } else {
        scriptOptions.value = newOptions;
      }
      if (res?.data?.total) {
        bookTotal.value = res.data.total;
      }
      if (res?.data?.total_pages) {
        bookTotalPages.value = res.data.total_pages;
      }
    }
  } catch (error) {
    console.error("获取剧本列表失败:", error);
  } finally {
    isBookLoading.value = false;
  }
};

// 剧本版本
const getPlaybookversions = async ids => {
  if (!ids || ids.length === 0) {
    playbookversions.value = [];
    return;
  }
  let allVersions = [];
  for (const id of ids) {
    try {
      const res = (await apiGetPlaybookVersionsList({
        playbook_id: id
      })) as any;
      if (res && res.code === 0 && Array.isArray(res.data)) {
        // 只取status为1的数据
        const filtered = res.data
          .filter(item => item.status === 1)
          .map(item => ({
            label: `${item.name} - ${item.version}`, // 添加剧本名称
            value: item.version_id
          }));
        allVersions = allVersions.concat(filtered);
      }
    } catch (e) {
      console.error("获取剧本版本内容失败", e);
    }
  }
  playbookversions.value = allVersions;
};

// 搜索剧本
const handleSearchPlaybooks = query => {
  searchQuery.value = query;
  bookPage.value = 1;
  getPlaybookList();
};

// 加载更多剧本
const loadMorePlaybooks = () => {
  // 如果当前已加载的数量小于总数，则加载更多
  if (scriptOptions.value.length < bookTotal.value) {
    bookPage.value += 1;
    getPlaybookList(true);
  }
};

// 添加绑定组
const addGroup = () => {
  formDataList.value.push({
    conditions: [
      {
        type: "",
        name: "",
        value: "",
        connect_type: ""
      }
    ],
    eventIngestionId: "",
    scriptIds: [],
    selectedFields: [],
    logs: [],
    playbooks: [],
    linkedExecution: 0,
    id: "",
    deduplicationEnabled: false,
    deduplicationNumber: 5000,
    deduplicationFields: []
  });
};
// 移除绑定组
const removeGroup = idx => {
  if (formDataList.value.length > 1) formDataList.value.splice(idx, 1);
};

const showFieldDialog = ref(false);
const selectedFields = ref<string[]>([]);

// 获取日志
const logs = ref<any[]>([]);
const getLogs = async () => {
  if (!props.eventId) return;
  const res: any = await eventIngestionOriginalLog({
    id: props.eventId,
    size: 1
  });
  // 处理响应数据，如果data是字符串，直接使用该字符串
  if (typeof res.data === "string") {
    logs.value = [res.data];
    datetimeStr.value = ""; // 重置datetime_str
  } else {
    logs.value = res.data || [];
    // 从日志数据中提取datetime_str
    if (res.data && res.data.length > 0 && res.data[0].datetime_str) {
      datetimeStr.value = res.data[0].datetime_str;
    } else {
      datetimeStr.value = "";
    }
  }
};

// 外部定义
const playbooksIds = ref<string[]>([]);
const autoExecutionShow = ref(false);
const getEditDetails = async () => {
  loading.value = true;
  try {
    await getLogs(); // 先获取日志，保证 logFields 有数据

    // 如果没有日志数据，也要设置默认事件名称
    if (!logs.value || logs.value.length === 0) {
      autoExecutionShow.value = false;
      resetForm();
      return;
    }

    // 如果日志数据是字符串（如"当前不存在日志"），也要设置默认事件名称
    if (logs.value.length === 1 && typeof logs.value[0] === "string") {
      autoExecutionShow.value = false;
      resetForm();
      return;
    }
    const Editref = (await eventIngestionEditDetail({
      id: props.eventId
    })) as any;

    if (
      Editref.code === 0 &&
      Array.isArray(Editref.data) &&
      Editref.data.length > 0
    ) {
      await getPlaybookList();
      const logFields = getLogFields();
      autoExecutionShow.value = true;
      formDataList.value = Editref.data.map(item => {
        // 保存playbooks到外部数组
        playbooksIds.value = Array.isArray(item.playbooks)
          ? item.playbooks
          : [];
        let scriptIds = Array.isArray(item.script_id) ? item.script_id : [];
        let conditions = convertConditionConnectType(item.condition);
        let eventName = normalizeEventName(item.event_name);
        let selectedFields = [];
        // 解析事件名称字段
        if (eventName && eventName.trim() !== "") {
          // 用新的解析方式
          selectedFields = parseEventNameFields(eventName);
        } else {
          // 如果没有现有的事件名称，且日志数据存在，使用默认的事件名称（"事件名称" + "_" + datetime_str）
          let defaultSelectedFields = [];
          if (
            props.eventName &&
            logs.value &&
            logs.value.length > 0 &&
            typeof logs.value[0] !== "string"
          ) {
            // 添加事件名称字段
            defaultSelectedFields.push(props.eventName);
            // 添加下划线分隔符
            defaultSelectedFields.push("_");
            // 添加时间戳字段 datetime_str
            defaultSelectedFields.push("datetime_str");
            // 多字段情况：添加日志字段 abc（后续可以继续添加更多字段）
            defaultSelectedFields.push("abc");
          }
          selectedFields = defaultSelectedFields;
        }
        let playbooks = Array.isArray(item.playbooks) ? item.playbooks : [];
        // 赋值后立即打印
        getPlaybookversions(playbooks);
        // 判断是否开启了去重：如果有 numbers 或 fields 字段且不为null，则认为开启了去重
        const deduplicationEnabled = !!(
          (item.numbers !== null && item.numbers !== undefined) ||
          (item.fields !== null &&
            item.fields !== undefined &&
            Array.isArray(item.fields) &&
            item.fields.length > 0)
        );
        return {
          id: item.id,
          conditions,
          eventIngestionId: item.event_ingestion_id,
          scriptIds,
          selectedFields,
          logs: [],
          event_name: eventName,
          playbooks,
          linkedExecution: item.status || 0, // 根据接口返回的status字段设置关联执行状态
          deduplicationEnabled, // 根据接口数据判断是否开启去重
          deduplicationNumber: item.numbers || 5000, // 从接口获取去重条数，默认为5000
          deduplicationFields: item.fields || [] // 从接口获取去重字段，默认为空数组
        };
      });
    } else {
      autoExecutionShow.value = false;
      resetForm();
    }
  } finally {
    loading.value = false;
  }
};

// 新增：右侧预览json美化且显示log字段
function getLogPreviewContent() {
  if (!logs.value || !logs.value[0]) return "";
  let logObj;
  if (typeof logs.value[0] === "string") {
    try {
      logObj = JSON.parse(logs.value[0]);
    } catch (error) {
      // console.warn("日志数据不是有效的JSON格式，显示原始数据:", logs.value[0]);
      // 如果不是JSON格式，直接显示原始数据
      return `${logs.value[0]}`;
    }
  } else {
    logObj = logs.value[0];
  }
  // 如果存在log字段，则显示log字段的内容
  if ("log" in logObj && logObj.log) {
    logData.value = logObj.log;
    return `log${JSON.stringify(logObj.log, null, 2)}`;
  }
  return "无数据";
}

function onConditionTypeChange(condition) {
  if (
    condition.type === "is_empty" ||
    condition.type === "!is_empty" ||
    condition.type === "exists" ||
    condition.type === "!exists"
  ) {
    condition.value = "";
  }
}

// 新增 includes/!includes 校验函数
function validateIncludesValue(condition) {
  if (["includes", "!includes"].includes(condition.type)) {
    try {
      const arr = JSON.parse(condition.value);
      if (!Array.isArray(arr)) {
        ElMessage.error('集合类型的值必须是数组格式，如 ["abc", "lmd"]');
      }
    } catch (e) {
      ElMessage.error('集合类型的值必须是合法的 JSON 数组，如 ["abc", "lmd"]');
    }
  }
}

// 新增 match 校验函数
function validateMatchValue(condition) {
  if (condition.type === "match") {
    if (!condition.value || !condition.value.trim()) {
      ElMessage.error("正则表达式不能为空");
      return;
    }
    try {
      const parser = new RegExpParser();
      parser.parsePattern(condition.value);
    } catch (e) {
      ElMessage.error("请输入合法的正则表达式");
      return;
    }
  }
}

const loading = ref(false);

// 转换条件连接类型
function convertConditionConnectType(conditions) {
  if (!Array.isArray(conditions)) return [];
  // 深拷贝，避免污染原数据
  const result = conditions.map(item => ({ ...item }));
  for (let i = result.length - 1; i > 0; i--) {
    result[i].connect_type = result[i - 1].connect_type;
  }
  if (result.length > 0) {
    delete result[0].connect_type;
  }
  return result;
}

//去掉event_name的引号和转义符
function normalizeEventName(eventName) {
  if (!eventName) return "";

  // 尝试JSON解析，处理类似 "\\"test_20250722162223\\"" 的情况
  try {
    const parsed = JSON.parse(eventName);
    if (typeof parsed === "string") {
      return parsed;
    }
  } catch (e) {
    // JSON解析失败，继续使用原来的逻辑
  }

  // 去除首尾的引号和转义符
  if (
    typeof eventName === "string" &&
    eventName.startsWith('"') &&
    eventName.endsWith('"')
  ) {
    // 去掉首尾引号
    eventName = eventName.slice(1, -1);
  }
  // 再去掉所有转义符
  eventName = eventName.replace(/\\"/g, '"');
  return eventName;
}

// 获取其他组已选中的 scriptIds，当剧本版本选中后禁用选项以防重复选择
function getDisabledScriptIds(idx) {
  // 其他组已选中的 scriptIds
  return formDataList.value
    .filter((_, i) => i !== idx)
    .flatMap(item => item.scriptIds);
}

function parseEventNameFields(event_name) {
  // 支持多种格式的解析，但界面显示时统一去掉 .log 前缀
  const reg = /to_string!\(\.log\.(\w+)\)|to_string!\(\.(\w+)\)|"([^"]*)"/g;
  const result = [];
  let match;
  let hasMatches = false;
  while ((match = reg.exec(event_name))) {
    hasMatches = true;
    if (match[1]) {
      // to_string!(.log.xxx) 格式 - 去掉 .log 前缀显示
      result.push(match[1]);
    } else if (match[2]) {
      // to_string!(.xxx) 格式
      result.push(match[2]);
    } else if (match[3]) {
      // "自定义内容" 格式
      result.push(match[3]);
    }
  }
  // 如果没有匹配到任何格式，直接返回原始字符串
  if (!hasMatches) {
    return [event_name];
  }
  return result;
}
</script>
