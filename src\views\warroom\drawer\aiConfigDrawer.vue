<template>
  <el-dialog
    v-model="visible"
    title="AI 配置"
    width="800px"
    @close="handleClose"
  >
    <div style="padding: 20px; text-align: center">
      <p>这里是AI配置的内容。</p>
    </div>
    <template #footer>
      <el-button type="primary" @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineExpose } from "vue";

const visible = ref(false);

function open() {
  visible.value = true;
}
function handleClose() {
  visible.value = false;
}

defineExpose({ open });
</script>
