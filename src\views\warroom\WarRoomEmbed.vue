<template>
  <div class="war-room-root">
    <iframe :src="iframeSrc" class="war-room-iframe" frameborder="0" />
  </div>
</template>

<script lang="ts" setup>
import { computed } from "vue";

const iframeSrc = computed(() => {
  return `http://localhost:8848/#/embed-warroom`;
});
</script>

<style scoped>
.war-room-root {
  height: 100%;
  min-height: 100vh;
  overflow: auto;
  box-sizing: border-box;
}

.war-room-iframe {
  width: 100%;
  height: 100%;
  border: none;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
</style>

<!-- 

<iframe
  src="http://localhost:8848/#/embed-warroom"
  style="width: 100%; height: 100vh; border: none; display: block; overflow: hidden"
  allow="fullscreen"
  scrolling="no"
  onload="this.style.height=(this.contentWindow.document.body.scrollHeight)+'px';"
/>

-->
