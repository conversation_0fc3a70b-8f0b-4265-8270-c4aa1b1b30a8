import { defineStore } from "pinia";
import { ref } from "vue";
import { allPermission, getRoleList } from "@/api/role";

interface RoleResponse {
  code: number;
  data: {
    roles: Array<{
      id: string;
      ctime: string;
      utime: string;
      deleted: boolean;
      name: string;
      description: string;
      permissions: Array<string>;
    }>;
    total: number;
    page: number;
    size: number;
    total_pages: number;
  };
}

interface PermissionResponse {
  code: number;
  data: {
    permissions: Array<{
      id: string;
      deleted: string;
      model: string;
      description: string;
      Permissions: Array<string>;
    }>;
  };
}

export const useRoleableStore = defineStore("roleable", () => {
  const permissionsList = ref<
    Array<PermissionResponse["data"]["permissions"][0]>
  >([]);
  const roleList = ref<
    Array<{
      label: string;
      value: string;
      description: string;
      permissions: string[];
    }>
  >([]);
  const loading = ref(false);

  // 获取权限列表
  const fetchPermissionsList = async () => {
    try {
      const res = (await allPermission({})) as PermissionResponse;
      console.log(res);
      permissionsList.value = res.data.permissions;
    } catch (error) {
      console.error("获取权限列表失败:", error);
    }
  };

  // 获取角色列表（自动分页获取所有数据）
  const fetchRoleList = async () => {
    try {
      loading.value = true;
      roleList.value = []; // 清空现有数据
      let currentPage = 1;
      const pageSize = 300; // 增大每页请求数量，减少请求次数
      let hasMorePages = true;
      while (hasMorePages) {
        const response = (await getRoleList({
          page: currentPage,
          size: pageSize
        })) as RoleResponse;
        if (response.code !== 0) {
          console.error("获取角色列表出错，错误码:", response.code);
          break;
        }
        // 处理当前页数据
        const pageRoles = response.data.roles.map(item => ({
          label: item.name,
          value: item.id,
          description: item.description,
          permissions: item.permissions
        }));
        roleList.value = [...roleList.value, ...pageRoles];
        // 判断是否已到达最后一页
        if (currentPage >= response.data.total_pages) {
          hasMorePages = false;
        }
        currentPage++;
      }
      console.log("角色列表加载完成，共获取", roleList.value.length, "条数据");
    } catch (error) {
      console.error("获取角色列表失败:", error);
    } finally {
      loading.value = false;
    }
  };

  return {
    permissionsList,
    roleList,
    loading,
    fetchPermissionsList,
    fetchRoleList
  };
});
