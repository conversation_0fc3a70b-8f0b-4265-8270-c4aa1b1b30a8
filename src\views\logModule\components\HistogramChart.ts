import * as echarts from "echarts";

export interface HistogramData {
  time: string;
  value: number;
  fullTime: string;
}

// 新增：定义返回类型
export interface ProcessedHistogramData {
  data: HistogramData[];
  intervalType: "hour" | "day";
}

export const processHistogramData = (logData: any): ProcessedHistogramData => {
  try {
    if (
      !logData ||
      !logData.columns ||
      !logData.rows ||
      logData.rows.length === 0
    ) {
      return { data: [], intervalType: "hour" };
    }

    const timestampIndex = logData.columns.findIndex(
      (column: any) => column.name === "timestamp"
    );

    if (timestampIndex === -1) {
      console.warn("查询结果中未找到 'timestamp' 字段");
      return { data: [], intervalType: "hour" };
    }

    const timestamps: Date[] = [];
    logData.rows.forEach((row: any) => {
      const timestamp = row[timestampIndex];
      if (timestamp) {
        let date: Date;
        if (typeof timestamp === "string") {
          date = new Date(timestamp);
        } else if (typeof timestamp === "number") {
          date =
            timestamp > 1000000000000
              ? new Date(timestamp)
              : new Date(timestamp * 1000);
        } else {
          date = new Date(timestamp);
        }
        if (!isNaN(date.getTime())) {
          timestamps.push(date);
        }
      }
    });

    if (timestamps.length === 0) {
      return { data: [], intervalType: "hour" };
    }

    const minTime = new Date(Math.min(...timestamps.map(t => t.getTime())));
    const maxTime = new Date(Math.max(...timestamps.map(t => t.getTime())));

    // 修改：根据时间跨度决定聚合粒度
    const timeSpan = maxTime.getTime() - minTime.getTime();
    const twoDaysInMillis = 2 * 24 * 60 * 60 * 1000;
    const intervalType = timeSpan > twoDaysInMillis ? "day" : "hour";

    const timeMap = new Map<string, { value: number; fullTime: string }>();
    const pad = (n: number) => String(n).padStart(2, "0");

    if (intervalType === "hour") {
      const startHour = new Date(minTime);
      startHour.setUTCMinutes(0, 0, 0);
      const endHour = new Date(maxTime);
      endHour.setUTCMinutes(0, 0, 0);
      endHour.setUTCHours(endHour.getUTCHours() + 1);

      const currentHour = new Date(startHour);
      while (currentHour <= endHour) {
        const intervalKey = currentHour.toISOString();
        const timeStr = `${pad(currentHour.getUTCHours())}:00:00`;
        const fullTime = `${currentHour.getUTCFullYear()}-${pad(
          currentHour.getUTCMonth() + 1
        )}-${pad(currentHour.getUTCDate())} ${timeStr}`;
        timeMap.set(intervalKey, { value: 0, fullTime: fullTime });
        currentHour.setUTCHours(currentHour.getUTCHours() + 1);
      }
    } else {
      // 按天聚合
      const startDay = new Date(minTime);
      startDay.setUTCHours(0, 0, 0, 0);
      const endDay = new Date(maxTime);
      endDay.setUTCHours(0, 0, 0, 0);

      const currentDay = new Date(startDay);
      while (currentDay <= endDay) {
        const intervalKey = currentDay.toISOString();
        const fullTime = `${currentDay.getUTCFullYear()}-${pad(
          currentDay.getUTCMonth() + 1
        )}-${pad(currentDay.getUTCDate())}`;
        timeMap.set(intervalKey, { value: 0, fullTime: fullTime });
        currentDay.setUTCDate(currentDay.getUTCDate() + 1);
      }
    }

    timestamps.forEach(date => {
      const intervalStartDate = new Date(date);
      if (intervalType === "hour") {
        intervalStartDate.setUTCMinutes(0, 0, 0);
      } else {
        intervalStartDate.setUTCHours(0, 0, 0, 0);
      }
      const intervalKey = intervalStartDate.toISOString();
      if (timeMap.has(intervalKey)) {
        timeMap.get(intervalKey)!.value += 1;
      }
    });

    const histogramData: HistogramData[] = Array.from(timeMap.entries())
      .map(([intervalKey, { value, fullTime }]) => ({
        time: intervalKey,
        value,
        fullTime
      }))
      .sort((a, b) => a.time.localeCompare(b.time));

    return { data: histogramData, intervalType };
  } catch (error) {
    console.error("处理直方图数据失败:", error);
    return { data: [], intervalType: "hour" };
  }
};

export const getHistogramOption = (
  processedData: ProcessedHistogramData // 修改：接收新的数据结构
) => {
  const { data, intervalType } = processedData; // 修改：解构数据和类型
  const values = data.map(item => item.value);
  const maxValue = values.length ? Math.max(...values) : 4;
  const dynamicMax = Math.ceil(maxValue + 1);
  const dynamicInterval = maxValue <= 4 ? 1 : Math.ceil((maxValue + 1) / 4);

  const option = {
    title: {
      text: "",
      left: "left",
      textStyle: {
        fontSize: 14,
        color: "#333"
      }
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "15%",
      top: "10%",
      containLabel: true
    },
    xAxis: {
      type: "time",
      axisLabel: {
        // 修改：动态格式化X轴标签
        formatter: (value: number) => {
          const date = new Date(value);
          const MM = String(date.getUTCMonth() + 1).padStart(2, "0");
          const DD = String(date.getUTCDate()).padStart(2, "0");
          if (intervalType === "hour") {
            const HH = String(date.getUTCHours()).padStart(2, "0");
            const mm = String(date.getUTCMinutes()).padStart(2, "0");
            return `${MM}-${DD}\n${HH}:${mm}`; // 使用 \n 进行换行
          } else {
            return `${MM}-${DD}`;
          }
        },
        fontSize: 10,
        color: "#666"
      },
      axisLine: { lineStyle: { color: "#e0e0e0" } }
    },
    yAxis: {
      type: "value",
      min: 0,
      max: dynamicMax,
      interval: dynamicInterval,
      axisLabel: {
        fontSize: 10,
        color: "#666"
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: "#f0f0f0",
          type: "solid"
        }
      }
    },
    series: [
      {
        type: "bar",
        data: data.map(item => [item.time, item.value]),
        barWidth: "60%",
        itemStyle: {
          color: "#409EFF",
          borderRadius: [2, 2, 0, 0]
        },
        emphasis: {
          itemStyle: {
            color: "#337ecc"
          }
        }
      }
    ],
    tooltip: {
      trigger: "axis",
      axisPointer: { type: "shadow" },
      // 修改：动态格式化Tooltip
      formatter: function (params: any) {
        if (params[0] && params[0].data) {
          const date = new Date(params[0].data[0]);
          const pad = (n: number) => String(n).padStart(2, "0");
          const formatDate = (d: Date) =>
            `${d.getUTCFullYear()}-${pad(d.getUTCMonth() + 1)}-${pad(
              d.getUTCDate()
            )}`;

          if (intervalType === "hour") {
            const endDate = new Date(date.getTime() + 60 * 60 * 1000 - 1);
            const formatTime = (d: Date) =>
              `${pad(d.getUTCHours())}:${pad(d.getUTCMinutes())}:${pad(
                d.getUTCSeconds()
              )}`;
            const dateStr = formatDate(date);
            const startTimeStr = formatTime(date);
            const endTimeStr = formatTime(endDate);
            return `日期: ${dateStr}<br/>时间范围: ${startTimeStr} - ${endTimeStr}<br/>日志数量: ${params[0].data[1]}`;
          } else {
            const dateStr = formatDate(date);
            return `日期: ${dateStr}<br/>日志数量: ${params[0].data[1]}`;
          }
        }
        return "";
      }
    }
  };
  return option;
};

export const initHistogramChart = (
  container: HTMLElement,
  logData?: any
): echarts.ECharts => {
  const existingChart = echarts.getInstanceByDom(container);
  if (existingChart) {
    existingChart.dispose();
  }

  const chart = echarts.init(container);

  // 修改：调整调用方式
  const processedData = processHistogramData(logData);
  const option = getHistogramOption(processedData);

  chart.setOption(option);

  window.addEventListener("resize", () => {
    chart.resize();
  });

  return chart;
};
