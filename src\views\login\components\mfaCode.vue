<script lang="ts" setup>
import { ref } from "vue";
import { message } from "@/utils/message";
import { mfaAuth } from "@/api/userList";
import { useRouter } from "vue-router";
import { getTopMenu, initRouter } from "@/router/utils";
import { useUserStoreHook } from "@/store/modules/user";
import { setToken } from "@/utils/auth"; // 导入userKey
import { useRefreshTokenStore } from "@/store/modules/RefreshToken"; // 导入刷新token的store

const router = useRouter();
const visible = ref(false);
const currentUser = ref<any>(null);
const code = ref("");

// 打开弹窗
const openDialog = (row: any) => {
  currentUser.value = row;
  visible.value = true;
};

// 确认开启多因素认证
const handleConfirm = () => {
  console.log(code.value);
  mfaAuth({
    code: code.value
  }).then((res: any) => {
    console.log(res);
    if (res.code === 0) {
      setToken({
        accessToken: res.data["aioe-token"],
        refreshToken: res.data["aioe-token"],
        expires: new Date(Date.now() + 30 * 60 * 1000) // 设置30分钟过期，保持为Date对象
      });
      useRefreshTokenStore().startRefreshTimer(); // 启动刷新token的定时器
      message("多因素认证成功", { type: "success" });
      visible.value = false;
      // 多因素认证成功后，初始化路由并跳转到首页
      initRouter().then(() => {
        router.push(getTopMenu(true).path);
      });
    } else {
      message(res.message || "多因素认证失败", { type: "error" });
    }
  });
};

// 取消多因素认证
const handleCancel = () => {
  visible.value = false;
  // 退出登录
  useUserStoreHook().logOut();
  // 跳转到登录页
  router.push("/login");
  message("已取消多因素认证，请重新登录", { type: "warning" });
};

defineExpose({
  openDialog
});
</script>

<template>
  <el-dialog v-model="visible" title="多因素认证" width="400px">
    <div class="text-center">
      <p class="mb-4">请输入验证码</p>
      <el-input v-model="code"  />
      <p class="mt-4 text-gray-500">请在输入验证码，然后点击确定登录</p>
    </div>
    <template #footer>
      <el-button type="primary" @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </template>
  </el-dialog>
</template>
