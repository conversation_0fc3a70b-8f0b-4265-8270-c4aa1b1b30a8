.binding-container {
  /* :deep(.binding-dialog) 及其子样式 */
  :deep(.binding-dialog) {
    .el-dialog {
      display: flex;
      flex-direction: column;
      max-height: 85vh;
      margin: 5vh auto !important;
    }

    .el-dialog__body {
      padding: 20px 0px;
      margin: 0;
      overflow: hidden;
    }

    .el-dialog__header,
    .el-dialog__footer {
      box-sizing: border-box;
      padding-top: var(--el-dialog-padding-primary);
      text-align: right;
    }
  }

  .binding-main-layout {
    display: flex;
    flex-direction: row;
    gap: 32px;
  }

  .binding-edit-area {
    flex: 2;
    min-width: 0;
    height: 70vh;
    overflow: hidden;
  }

  .preview-col {
    flex: 1;
    min-width: 320px;
    max-width: 480px;
  }

  .dialog-content-multi {
    display: flex;
    flex-direction: column;
    gap: 28px;

    .row-group {
      display: grid;
      grid-template-columns: 1fr 100px;
      align-items: flex-start;
      margin-bottom: 16px;
      position: relative;
      // 除了最后一个，底部加线
      &:not(:last-child)::after {
        content: "";
        display: block;
        position: absolute;
        left: 0;
        right: 0;
        bottom: -16px; // 与 margin-bottom 保持一致
        height: 1px;
        background: #e4e7ed; // Element Plus 默认分割线色
        opacity: 0.8;
      }
    }

    .form-col {
      width: 100%;
      padding-right: 10px;
    }

    .preview-col {
      width: 100%;
    }

    .action-col {
      width: 100px;
      display: flex;
      align-items: center;
      height: 100%;
    }
  }

  .dialog-footer {
    text-align: right;
  }

  :deep(.el-select) {
    width: 100%;
  }

  .condition-builder {
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 15px;
    width: 100%;

    .conditions-container {
      .add-condition-wrapper {
        margin-bottom: 20px;
        text-align: center;
      }

      .condition-section {
        margin-bottom: 20px;
        padding: 15px;
        border: 1px solid #ebeef5;
        border-radius: 4px;
        background-color: #fafafa;

        .condition-item {
          margin-bottom: 10px;

          &:last-child {
            margin-bottom: 0;
          }

          .condition-row {
            display: flex;
            gap: 10px;
            align-items: center;

            .condition-connect {
              width: 100px;
              flex-shrink: 0;
            }

            .condition-type {
              width: 160px;
              flex-shrink: 0;
            }

            .condition-name,
            .condition-value {
              flex: 1;
              min-width: 0;
            }
          }
        }
      }
    }
  }

  .condition-preview {
    background-color: #1e1e1e;
    border-radius: 6px;
    padding: 16px;

    h4 {
      position: sticky;
      top: 0;
      margin: 0 0 12px 0;
      font-size: 14px;
      color: #d4d4d4;
      padding-bottom: 8px;
      border-bottom: 1px solid #333;
      background-color: #1e1e1e;
      z-index: 1;
    }

    pre {
      margin: 0;
      white-space: pre-wrap;
      font-size: 13px;
      line-height: 1.6;
      font-family: "Monaco", "Menlo", "Consolas", monospace;
      color: #ffffff;

      .key {
        color: #9cdcfe;
      }

      .string {
        color: #ce9178;
      }

      .punctuation {
        color: #808080;
      }

      .number {
        color: #b5cea8;
      }

      .boolean {
        color: #569cd6;
      }
    }
  }

  .custom-prefix-icon {
    height: 100%;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    transition: background 0.2s,
    color 0.2s;
    color: var(--el-text-color-placeholder); // 图标默认色
    border-radius: 4px;
    padding: 2px 6px; // 让块有点内边距

    &:hover {
      background: #2c7fe4; // 块背景变蓝
      color: #fff; // 图标变白
    }
  }

  .custom-name-preview-block {
    background: #1e1e1e;
    border-radius: 6px;
    padding: 12px 16px;
    min-height: 38px;
    width: 100%;
    overflow-x: auto;
    margin-bottom: 0;
  }

  .custom-name-preview-content {
    margin: 0;
    white-space: pre-wrap;
    font-size: 13px;
    line-height: 1.6;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    min-height: 24px;
    color: #fff;
    justify-content: flex-start;
    text-align: left;
  }

  .custom-name-preview-tag {
    display: inline-block;
    background: #23272e;
    color: #9cdcfe;
    border-radius: 4px;
    padding: 2px 12px;
    margin: 4px 8px 4px 0;
    font-size: 13px;
    font-family: inherit;
  }

  .custom-name-preview-empty {
    color: #888;
    font-style: italic;
    text-align: center;
    width: 100%;
  }
}
