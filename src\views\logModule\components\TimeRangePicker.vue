<template>
  <div>
    <el-popover
      ref="popoverRef"
      :visible="popoverVisible"
      placement="bottom-start"
      :width="500"
      trigger="click"
      :hide-after="0"
      :show-arrow="false"
      popper-class="time-range-popover"
    >
      <template #reference>
        <el-button
          size="small"
          style="
            min-width: 100px;
            max-width: 140px;
            height: 32px;
            justify-content: space-between;
          "
          @click="popoverVisible = !popoverVisible"
        >
          <span
            style="
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1;
            "
          >
            {{ displayText }}
          </span>
          <el-icon style="margin-left: 4px; flex-shrink: 0">
            <ArrowDown />
          </el-icon>
        </el-button>
      </template>
      <el-tabs v-model="activeTab" type="card">
        <el-tab-pane label="相对时间" name="relative">
          <div class="relative-time-grid">
            <el-button
              v-for="item in relativeTimeOptions"
              :key="item.value"
              size="small"
              style="margin: 0; height: 32px"
              @click="selectRelativeTime(item)"
            >
              {{ item.label }}
            </el-button>
          </div>
        </el-tab-pane>
        <el-tab-pane label="绝对时间" name="absolute">
          <el-date-picker
            v-model="absoluteTimeComputed"
            type="datetimerange"
            size="small"
            style="height: 32px"
            range-separator="到"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-tab-pane>
      </el-tabs>
    </el-popover>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted } from "vue";
import { ArrowDown } from "@element-plus/icons-vue";

// --- Props and Emits ---
const props = defineProps({
  // 父组件通过 v-model 绑定的值
  modelValue: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(["update:modelValue"]);

// --- 内部UI状态 ---
const popoverVisible = ref(false);
const activeTab = ref("relative");
const forceUpdate = ref(0); // 用于强制更新UI
const displayText = ref("请选择时间范围"); // 直接控制显示文本

// --- 初始化函数 ---
const initializeDisplayText = () => {
  if (props.modelValue === null) {
    displayText.value = "全部时间";
    return;
  }
  if (Array.isArray(props.modelValue) && props.modelValue.length === 2) {
    const [start, end] = props.modelValue.map(d => new Date(d));
    const matchedRelativeOption = relativeTimeOptions.find(
      option =>
        !option.isAll &&
        Math.abs(end.getTime() - start.getTime() - option.value) < 1000
    );
    if (matchedRelativeOption) {
      displayText.value = `${matchedRelativeOption.label}  至  现在`;
    } else {
      displayText.value = `${formatDate(start)} - ${formatDate(end)}`;
    }
  } else {
    displayText.value = "请选择时间范围";
  }
};

// 组件挂载时初始化显示文本
onMounted(() => {
  initializeDisplayText();
});

// --- 辅助函数和静态数据 ---
const formatDate = date => {
  if (!date) return "";
  const Y = date.getFullYear();
  const M = (date.getMonth() + 1).toString().padStart(2, "0");
  const D = date.getDate().toString().padStart(2, "0");
  const h = date.getHours().toString().padStart(2, "0");
  const m = date.getMinutes().toString().padStart(2, "0");
  const s = date.getSeconds().toString().padStart(2, "0");
  return `${Y}-${M}-${D} ${h}:${m}:${s}`;
};

const relativeTimeOptions = [
  { label: "全部", value: -1, isAll: true },
  { label: "近15分钟", value: 15 * 60 * 1000 },
  { label: "近30分钟", value: 30 * 60 * 1000 },
  { label: "近1小时", value: 60 * 60 * 1000 },
  { label: "近12小时", value: 12 * 60 * 60 * 1000 },
  { label: "近24小时", value: 24 * 60 * 60 * 1000 },
  { label: "近7天", value: 7 * 24 * 60 * 60 * 1000 }
];

// --- 用户操作 ---
// 选择相对时间：计算范围并 emit 更新
const selectRelativeTime = async item => {
  if (item.isAll) {
    emit("update:modelValue", null);
    displayText.value = "全部时间";
  } else {
    const now = new Date();
    const start = new Date(now.getTime() - item.value);
    const newRange = [start, now];
    emit("update:modelValue", newRange);
    displayText.value = `${item.label}  至  现在`;
  }
  popoverVisible.value = false;
  forceUpdate.value++;
  await nextTick();
};

// --- 计算属性 (核心逻辑) ---

// 计算属性1：用于 el-date-picker 的 v-model
// 这是一个带有 getter 和 setter 的特殊计算属性，用于实现双向绑定
const absoluteTimeComputed = computed({
  // get：决定 el-date-picker 显示什么值
  get() {
    // 检查当前 modelValue 是否是绝对时间，如果是，则返回该值
    // 如果是相对时间，则返回空数组，清空选择器
    if (props.modelValue === null) {
      return [];
    }
    if (Array.isArray(props.modelValue) && props.modelValue.length === 2) {
      const [start, end] = props.modelValue.map(d => new Date(d));
      const isRelative = relativeTimeOptions.some(
        option =>
          !option.isAll &&
          Math.abs(end.getTime() - start.getTime() - option.value) < 1000
      );
      return isRelative ? [] : props.modelValue;
    }
    return [];
  },
  // set：当用户在 el-date-picker 中选择了新日期时触发
  set(newRange) {
    // 只负责 emit 更新，将数据交给父组件
    emit("update:modelValue", newRange || []);

    // 直接更新显示文本
    if (newRange && newRange.length === 2) {
      const [start, end] = newRange;
      displayText.value = `${formatDate(start)} - ${formatDate(end)}`;
    } else {
      displayText.value = "请选择时间范围";
    }

    popoverVisible.value = false;
  }
});

// 计算属性2：用于按钮上显示的文本
// 它的值完全依赖于 props.modelValue，确保显示与数据源同步
const displayValue = computed(() => {
  forceUpdate.value;
  if (props.modelValue === null) {
    return "全部时间";
  }
  if (Array.isArray(props.modelValue) && props.modelValue.length === 2) {
    const [start, end] = props.modelValue.map(d => new Date(d));
    const matchedRelativeOption = relativeTimeOptions.find(
      option =>
        !option.isAll &&
        Math.abs(end.getTime() - start.getTime() - option.value) < 1000
    );
    if (matchedRelativeOption) {
      return `${matchedRelativeOption.label}  至  现在`;
    } else {
      return `${formatDate(start)} - ${formatDate(end)}`;
    }
  }
  return "请选择时间范围";
});
</script>

<style scoped>
.relative-time-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 8px;
  padding: 10px;
}
</style>

<style>
.time-range-popover {
  z-index: 2000;
}
</style>
