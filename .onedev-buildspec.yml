version: 40
jobs:
- name: ci
  jobExecutor: aioe-builder
  steps:
  - !CheckoutStep
    name: 拉取最新代码
    cloneCredential: !DefaultCredential {}
    withLfs: false
    withSubmodules: false
    checkoutPath: '@project_path@'
    condition: SUCCESSFUL
    optional: false
  - !BuildImageStep
    name: 构建镜像
    buildPath: '@project_path@'
    dockerfile: '@project_path@/Dockerfile'
    output: !RegistryOutput
      tags: reg.hongcloud.net/lion_web:@tag@
    platforms: linux/amd64,linux/arm64
    condition: SUCCESSFUL
    optional: false
  triggers:
  - !TagCreateTrigger
    branches: main
    projects: lion_web
  retryCondition: never
  maxRetries: 3
  retryDelay: 30
  timeout: 14400
- name: cd
  jobExecutor: tgy-deploy
  steps:
  - !CommandStep
    name: 更新docker-compose配置
    runInContainer: false
    interpreter: !DefaultInterpreter
      commands: |
        NEW_VER=@tag@
        sed -i -E "s#(reg\.hongcloud\.net/lion_web:)v[0-9]+\.[0-9]+\.[0-9]+#\1${NEW_VER}#" /opt/lion/docker-compose.yml
        docker-compose -f /opt/lion/docker-compose.yml --env-file /opt/lion/.env up -d
    useTTY: true
    condition: SUCCESSFUL
    optional: false
  triggers:
  - !DependencyFinishedTrigger
    projects: lion_web
  jobDependencies:
  - jobName: ci
    requireSuccessful: false
    artifacts: '**'
  retryCondition: never
  maxRetries: 3
  retryDelay: 30
  timeout: 14400
