<template>
  <div>
    <el-dialog
      v-model="permissionDialogVisible"
      class="permission"
      title="剧本权限分配"
      top="5vh"
      width="700px"
    >
      <div class="playbookTitle">
        <el-tag>剧本名称:</el-tag>
        <div class="text">{{ playbookData.name }}</div>
      </div>

      <el-tabs v-model="activeName">
        <el-tab-pane label="用户权限" name="user">
          <el-table v-if="allUserList" :data="allUserList || []" border>
            <el-table-column label="用户" width="200px" align="center">
              <template #default="{ row }">
                <div>{{ row.display_name }}({{ row.username }})</div>
              </template>
            </el-table-column>
            <el-table-column label="编辑" align="center">
              <template #default="{ row }">
                <el-checkbox
                  v-model="row.permissions.u"
                  :disabled="
                    row.id == '43782755-6382-4f42-82a2-5e557370d858'
                      ? true
                      : false
                  "
                />
              </template>
            </el-table-column>
            <el-table-column label="执行" align="center">
              <template #default="{ row }">
                <el-checkbox
                  v-model="row.permissions.e"
                  :disabled="
                    row.id == '43782755-6382-4f42-82a2-5e557370d858'
                      ? true
                      : false
                  "
                />
              </template>
            </el-table-column>
            <el-table-column label="权限分配" align="center">
              <template #default="{ row }">
                <el-checkbox
                  v-model="row.permissions.p"
                  :disabled="
                    row.id == '43782755-6382-4f42-82a2-5e557370d858'
                      ? true
                      : false
                  "
                />
              </template>
            </el-table-column>
            <el-table-column label="删除" align="center">
              <template #default="{ row }">
                <el-checkbox
                  v-model="row.permissions.d"
                  :disabled="
                    row.id == '43782755-6382-4f42-82a2-5e557370d858'
                      ? true
                      : false
                  "
                />
              </template>
            </el-table-column>
            <el-table-column label="查看" align="center">
              <template #default="{ row }">
                <el-checkbox
                  v-model="row.permissions.r"
                  :disabled="
                    row.id == '43782755-6382-4f42-82a2-5e557370d858'
                      ? true
                      : false
                  "
                />
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="角色权限" name="role">
          <el-table v-if="allRoleList" :data="allRoleList || []" border>
            <el-table-column label="用户" width="200px" align="center">
              <template #default="{ row }">
                <div>{{ row.name }}</div>
              </template>
            </el-table-column>
            <el-table-column label="编辑" align="center">
              <template #default="{ row }">
                <el-checkbox
                  v-model="row.permissions.u"
                  :disabled="row.id == '111111' ? true : false"
                />
              </template>
            </el-table-column>
            <el-table-column label="执行" align="center">
              <template #default="{ row }">
                <el-checkbox
                  v-model="row.permissions.e"
                  :disabled="row.id == '111111' ? true : false"
                />
              </template>
            </el-table-column>
            <el-table-column label="权限分配" align="center">
              <template #default="{ row }">
                <el-checkbox
                  v-model="row.permissions.p"
                  :disabled="row.id == '111111' ? true : false"
                />
              </template>
            </el-table-column>
            <el-table-column label="删除" align="center">
              <template #default="{ row }">
                <el-checkbox
                  v-model="row.permissions.d"
                  :disabled="row.id == '111111' ? true : false"
                />
              </template>
            </el-table-column>
            <el-table-column label="查看" align="center">
              <template #default="{ row }">
                <el-checkbox
                  v-model="row.permissions.r"
                  :disabled="row.id == '111111' ? true : false"
                />
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <!-- <el-tabs style="width: 100%">
        <el-tab-pane label="用户授权" name="first">
          <el-table :data="allUserList" style="width: 100%">
            <el-table-column label="用户">
              <el-table-column type="selection">
                <template #default="{ row }">
                  <div>{{ row.display_name }}({{ row.username }})</div>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="编辑">
              <el-table-column type="selection" />
            </el-table-column>
            <el-table-column label="执行">
              <el-table-column type="selection" />
            </el-table-column>
            <el-table-column label="权限分配">
              <el-table-column type="selection" />
            </el-table-column>
            <el-table-column label="删除">
              <el-table-column type="selection" />
            </el-table-column>
            <el-table-column label="查看">
              <el-table-column type="selection" />
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="角色授权" name="second">
          <el-table>
            <el-table-column type="selection" label="编辑" />
            <el-table-column type="selection" label="执行" />
            <el-table-column type="selection" label="权限分配" />
            <el-table-column type="selection" label="删除" />
            <el-table-column type="selection" label="查看" />
          </el-table>
        </el-tab-pane>
      </el-tabs> -->

      <template #footer>
        <div>
          <el-button @click="permissionDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="savePlaybookPermission()">
            提交
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import {
  ElMessage,
  type TransferDirection,
  type TransferKey
} from "element-plus";
import {
  apiGetPlaybookPermissionsSelect,
  apiPlaybookPermissionsAdd
} from "@/api/playbook";
import { apiuserAccountList, apiUserRoleList } from "@/api/userList";
import usePlaybookStore from "@/store/modules/playbook";

const playbookData = ref();
const permissionDialogVisible = ref(false);
const selected = ref(0);
const playbookTitle = ref();
const actions = ref([]);
const actionList = ref([]);
const data = ref();
const userValue = ref([]);
const roleValue = ref([]);
const allUserList = ref([]);
const allRoleList = ref([]);
const playbookStore = usePlaybookStore();
const isLoading = ref(true);
const activeName = ref("user");
const playbookPermissions = ref();

onMounted(() => {
  getAllUserList();
  getAllRoleList();
});

//打开PermissionDialog对话框
const openPermissionDialog = async (data: any) => {
  activeName.value = "user";
  playbookData.value = data;
  console.log(playbookData.value);
  //每次打开时，初始化用户和角色权限
  allRoleList.value.forEach(item => {
    item.permissions = {
      r: false,
      u: false,
      d: false,
      e: false,
      p: false
    };
  });
  allRoleList.value.forEach(item => {
    item.permissions = {
      r: false,
      u: false,
      d: false,
      e: false,
      p: false
    };
  });
  //为了加载时，用户不会看见数据变化闪了一下而加的await
  await getPlaybookPermissionsSelect();
  permissionDialogVisible.value = true;
};

// //获取全部用户和角色信息
// const getAllUserAndRoleList = async () => {
//   let res: any = await apiPlaybookInit({});
//   allUserList.value = res.data.users;
//   allRoleList.value = res.data.roles;
//   allUserList.value = allUserList.value.map(item => ({
//     key: item.id,
//     label: item.display_name,
//     name: item.username,
//     disabled: false
//   }));
//   allRoleList.value = allRoleList.value.map(item => ({
//     key: item.id,
//     label: item.name,
//     disabled: false
//   }));
// };

//获取全部用户信息
const getAllUserList = async () => {
  let res: any = await apiuserAccountList({});
  allUserList.value = res.data.users;
  allUserList.value = allUserList.value.map(user => ({
    ...user,
    permissions: {
      r: false,
      u: false,
      d: false,
      e: false,
      p: false
    }
  }));
  console.log("allUserList.value:", allUserList.value);
};

//获取全部角色信息
const getAllRoleList = async () => {
  let res: any = await apiUserRoleList({});
  allRoleList.value = res.data.roles;
  allRoleList.value = allRoleList.value.map(role => ({
    ...role,
    permissions: {
      r: false,
      u: false,
      d: false,
      e: false,
      p: false
    }
  }));
  console.log("allRoleList.value:", allRoleList.value);
};

//获取剧本权限（需要剧本查看权限）
const getPlaybookPermissionsSelect = async () => {
  let res: any = await apiGetPlaybookPermissionsSelect({
    playbook_id: playbookData.value.id
  });
  if (res.code == 0) {
    playbookPermissions.value = res.data;
    roleValue.value = playbookPermissions.value.filter(
      item => item.users === null
    );
    userValue.value = playbookPermissions.value.filter(
      item => item.roles === null
    );
    userValue.value = userValue.value.map(item => transformPermissions(item));
    roleValue.value = roleValue.value.map(item => transformPermissions(item));
    console.log("userValue.value:", userValue.value);
    console.log("roleValue.value:", roleValue.value);
    userValue.value.forEach(item => {
      allUserList.value.find(user => {
        if (user.id === item.users.id) {
          user.permissions = item.permissions;
        }
      });
    });
    roleValue.value.forEach(item => {
      allRoleList.value.find(role => {
        if (role.id === item.roles.id) {
          role.permissions = item.permissions;
        }
      });
    });
  }
};

//定义完整的权限列表
const FULL_PERMISSIONS = ["r", "u", "d", "e", "p"];

function transformPermissions(data) {
  const permissionsSet = new Set(data.permissions || []);
  const permissionsObj = {};

  FULL_PERMISSIONS.forEach(permission => {
    permissionsObj[permission] = permissionsSet.has(permission); // O(1)
  });

  return {
    ...data,
    permissions: permissionsObj
  };
}

//保存剧本权限
const savePlaybookPermission = async () => {
  console.log("allUserList.value:", allUserList.value);
  console.log("allRoleList.value:", allRoleList.value);
  const usersPermissionsDict = allUserList.value.reduce((acc, item) => {
    acc[item.id] = Object.keys(item.permissions) // 步骤1：提取权限键
      .filter(key => item.permissions[key]); // 步骤2：过滤值为true的键
    return acc; // 步骤3：返回累积对象
  }, {}); // 初始值：空对象
  const rolesPermissionsDict = allRoleList.value.reduce((acc, item) => {
    acc[item.id] = Object.keys(item.permissions) // 步骤1：提取权限键
      .filter(key => item.permissions[key]); // 步骤2：过滤值为true的键
    return acc; // 步骤3：返回累积对象
  }, {}); // 初始值：空对象
  permissionDialogVisible.value = false;
  let res: any = await apiPlaybookPermissionsAdd({
    playbook_id: playbookData.value.id,
    users: usersPermissionsDict,
    roles: rolesPermissionsDict
  });
  if (res.code === 0) {
    ElMessage.success("权限分配成功");
  } else {
    ElMessage.error("权限分配失败");
  }
};

const handleChange = (
  value: TransferKey[],
  direction: TransferDirection,
  movedKeys: TransferKey[]
) => {
  console.log(value, direction, movedKeys);
};

defineExpose({
  openPermissionDialog
});
</script>

<style lang="scss" scoped>
.permission {
  width: 500px;

  .playbookTitle {
    display: flex;
    align-items: center;

    .el-tag {
      width: auto;
      margin-bottom: 10px;
    }

    .text {
      padding: 0 0 10px 10px; //上右下左
    }
  }

  .userTransfer {
    display: flex;
    justify-content: center;
  }

  .roleTransfer {
    display: flex;
    justify-content: center;
  }
}

.transfer-container {
  margin-bottom: 20px;

  :deep(.el-transfer) {
    width: 100%;
    display: flex;
    align-items: flex-start;
    justify-content: center;

    .el-transfer-panel {
      flex: 1;
      width: auto;
      max-width: none;
      margin: 0 10px;
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 4px;

      .el-transfer-panel__header {
        padding: 8px 12px;
        background-color: var(--el-fill-color-light);
        border-bottom: 1px solid var(--el-border-color-lighter);

        .el-checkbox {
          color: var(--el-text-color-regular);
        }
      }

      .el-transfer-panel__body {
        height: 240px;

        .el-transfer-panel__list {
          height: 200px;
          overflow-y: auto;

          &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
          }

          &::-webkit-scrollbar-thumb {
            border-radius: 3px;
            background-color: var(--el-border-color);
          }

          &::-webkit-scrollbar-track {
            background-color: var(--el-fill-color-lighter);
          }
        }

        .el-transfer-panel__filter {
          margin: 10px;

          .el-input__wrapper {
            padding: 0 8px;
          }
        }

        .transfer-item {
          padding: 4px 0;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .username {
            font-weight: 500;
          }

          .account {
            color: var(--el-text-color-secondary);
            font-size: 12px;
          }

          .description {
            color: var(--el-text-color-secondary);
            font-size: 12px;
            margin-left: 4px;
          }
        }
      }
    }

    .el-transfer__buttons {
      padding: 0 20px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin: auto 10px;

      .el-button {
        margin: 5px 0;
      }
    }
  }

  .transfer-footer {
    padding: 6px 0;
    text-align: center;
    color: var(--el-text-color-secondary);
    font-size: 12px;
    border-top: 1px solid var(--el-border-color-lighter);
  }
}
</style>
