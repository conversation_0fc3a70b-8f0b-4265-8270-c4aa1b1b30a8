<template>
  <el-popover :width="500" placement="left-start">
    <template #reference>
      <el-icon>
        <QuestionFilled />
      </el-icon>
    </template>
    <template #default>
      <div style="display: flex; flex-direction: column; gap: 16px">
        <div>
          <p>普通 - 常用剧本</p>
          <p>签到 - 签到类型的用于签到排班中使用</p>
          <p>去重 - 去重类型的用于事件接入配置去重剧本中使用</p>
          <p>
            系统 -
            系统类型的剧本用于系统层面需要配置剧本中使用，系统类型的剧本只有创建者、管理员、拥有模块管理查看权限的才能在剧本列表中查看到，执行不受限
          </p>
        </div>
      </div>
    </template>
  </el-popover>
</template>

<script lang="ts" setup>
import { QuestionFilled } from "@element-plus/icons-vue";
</script>
