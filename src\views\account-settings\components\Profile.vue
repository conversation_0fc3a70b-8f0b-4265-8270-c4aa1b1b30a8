<script lang="ts" setup>
import { onMounted, reactive, ref } from "vue";
import { message } from "@/utils/message";
import type { FormInstance } from "element-plus";
import { deviceDetection } from "@pureadmin/utils";
import { useUserStoreHook } from "@/store/modules/user";
import { editUserInfo, getUserList } from "@/api/userList";

defineOptions({
  name: "Profile"
});

// 获取用户信息
const userStore = useUserStoreHook();
const userInfoFormRef = ref<FormInstance>();

// 从接口获取最新的用户数据
const userInfos = reactive({
  username: "",
  email: "",
  phone: "",
  password: "",
  permit_id: ""
});

onMounted(() => {
  getUserList({ username: userStore.username }).then((res: any) => {
    if (res.code === 0 && res.data.data.length > 0) {
      const userData = res.data.data[0];
      // 使用接口返回的最新数据填充表单
      userInfos.username = userData.username;
      userInfos.email = userData.email;
      userInfos.phone = userData.phone;
      userInfos.permit_id = userData.permit_ip || "";
    }
  });
});

// 邮箱自动补全
function queryEmail(queryString, callback) {
  const emailList = [
    { value: "@qq.com" },
    { value: "@126.com" },
    { value: "@163.com" }
  ];
  let results = [];
  let queryList = [];
  emailList.map(item =>
    queryList.push({ value: queryString.split("@")[0] + item.value })
  );
  results = queryString
    ? queryList.filter(
        item =>
          item.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0
      )
    : queryList;
  callback(results);
}

// 更新信息
const onSubmit = async (formEl: FormInstance) => {
  await formEl.validate((valid, fields) => {
    if (valid) {
      // 过滤掉空值
      const submitData = Object.entries(userInfos).reduce(
        (acc, [key, value]) => {
          if (value !== "" && value !== null && value !== undefined) {
            acc[key] = value;
          }
          return acc;
        },
        {}
      );
      // 调用更新信息接口
      editUserInfo(submitData).then(res => {
        console.log(res);
        message("更新个人信息成功", { type: "success" });
      });
    } else {
      console.log("error submit!", fields);
    }
  });
};
</script>

<template>
  <div
    :class="[
      'min-w-[180px]',
      deviceDetection() ? 'max-w-[100%]' : 'max-w-[70%]'
    ]"
  >
    <h3 class="my-8">个人信息</h3>
    <el-form ref="userInfoFormRef" :model="userInfos" label-position="top">
      <el-form-item label="用户名" prop="username">
        <el-input
          v-model="userInfos.username"
          disabled

        />
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input v-model="userInfos.password"  />
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-autocomplete
          v-model="userInfos.email"
          :fetch-suggestions="queryEmail"
          :trigger-on-focus="false"
          class="w-full"
          clearable

        />
      </el-form-item>
      <el-form-item label="联系电话">
        <el-input
          v-model="userInfos.phone"
          clearable

        />
      </el-form-item>
      <el-form-item>
        <template #label>
          <span class="flex items-center">
            登录IP白名单:
            <el-tooltip
              :hide-after="0"
              content="只有白名单内的IP地址能够登录该账号"
              placement="top"
            >
              <el-icon class="ml-2 cursor-help"><QuestionFilled /></el-icon>
            </el-tooltip>
          </span>
        </template>
        <el-input
          v-model="userInfos.permit_id"
          autosize

          type="textarea"
        />
      </el-form-item>
      <el-button type="primary" @click="onSubmit(userInfoFormRef)">
        保存
      </el-button>
    </el-form>
  </div>
</template>
