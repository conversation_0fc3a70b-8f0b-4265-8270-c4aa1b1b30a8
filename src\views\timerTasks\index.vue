<template>
  <div class="timer-tasks">
    <el-card>
      <div class="header-actions">
        <div class="search-area">
          <Perms :value="['timed:r']">
            <el-input
              v-model="searchQuery"
              clearable
              placeholder="搜索任务名称, 任务名称, 更新人..."
              style="width: 300px"
              @change="handleSearch"
              @clear="handleClear"
            >
              <template #append>
                <el-button @click="handleSearch">
                  <el-icon>
                    <Search />
                  </el-icon>
                </el-button>
              </template>
            </el-input>
          </Perms>
        </div>
        <Perms :value="['timed:c']">
          <el-button type="primary" @click="handleOpenDialog(false)"
            >新建
          </el-button>
        </Perms>
      </div>
      <el-table :data="tableData" border style="width: 100%; margin: 20px 0">
        <el-table-column label="任务名称" prop="name" />
        <el-table-column label="剧本/动作名称">
          <template #default="{ row }">
            <span>
              {{ row.playbook || row.action_input?.action_name || "-" }}
            </span>
            <span v-if="row.playbook_remark">
              {{ `（${row.playbook_remark}）` }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="enable" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.enable"
              :active-text="'启用'"
              :active-value="1"
              :before-change="() => handleStatusChange(row)"
              :inactive-text="'禁用'"
              :inactive-value="0"
              :loading="row.statusLoading"
              inline-prompt
            />
          </template>
        </el-table-column>
        <el-table-column label="执行策略" prop="tactics" width="200">
          <template #default="{ row }">
            <span>{{ row.tactics }}</span>
          </template>
        </el-table-column>
        <el-table-column label="更新人" prop="updator" width="200">
          <template #default="scope">
            {{ scope.row.updator.display_name }}({{
              scope.row.updator.username
            }})
          </template>
        </el-table-column>
        <el-table-column label="更新时间" prop="utime" width="200">
          <template #default="scope">
            {{ formatTime(scope.row.utime) }}
          </template>
        </el-table-column>
        <el-table-column label="调用成功率" prop="success_rate" width="160">
          <template #header>
            <div class="flex items-center">
              <span>调用成功率</span>
              <el-tooltip
                :hide-after="0"
                effect="dark"
                placement="top"
                raw-content
              >
                <template #content>
                  <div class="tooltip-content">
                    <div class="font-bold mb-2">
                      [周期成功率] 采样规则和计算公式
                    </div>
                    <div class="mb-2">采样规则:</div>
                    <div class="ml-2 mb-1">
                      近24小时执行次数 > 10, 则 "分母" 为近24小时执行总数
                    </div>
                    <div class="ml-2 mb-2">
                      近24小时执行次数 ≤ 10, 则 "分母" 为近10次执行任务
                    </div>
                    <div class="mb-2">计算公式:</div>
                    <div class="ml-2">
                      成功次数(分子) / 分母 * 100 = 周期成功率 (%)
                    </div>
                  </div>
                </template>
                <el-icon class="ml-1 cursor-help">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
          <template #default="{ row }">
            <el-tag
              :type="
                row.success_rate === null
                  ? 'info'
                  : parseFloat(row.success_rate) < 30
                    ? 'danger'
                    : parseFloat(row.success_rate) < 80
                      ? 'warning'
                      : 'success'
              "
            >
              {{ row.success_rate === null ? "未知" : row.success_rate }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          label="最新执行结果"
          prop="result"
          width="120"
        >
          <template #default="scope">
            <el-tag v-if="scope.row.result === 'success'" type="success"
              >成功
            </el-tag>
            <el-tag v-else-if="scope.row.result === 'fail'" type="danger"
              >失败
            </el-tag>
            <el-tag v-else type="info">未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240">
          <template #default="{ row }">
            <Perms :value="['timed:u']">
              <el-tooltip :hide-after="0" content="编辑" placement="top">
                <el-button link type="primary" @click="handleEdit(row)">
                  <IconifyIconOffline
                    height="20"
                    icon="mingcute:edit-line"
                    width="20"
                  />
                </el-button>
              </el-tooltip>
            </Perms>
            <Perms :value="['timed:d']">
              <el-tooltip :hide-after="0" content="删除" placement="top">
                <span class="ml-3">
                  <el-popconfirm
                    cancel-button-text="取消"
                    confirm-button-text="确认"
                    title="确认要删除这个任务吗？"
                    @confirm="handleDelete(row)"
                  >
                    <template #reference>
                      <el-button link type="danger">
                        <IconifyIconOffline
                          height="20"
                          icon="icon-park-outline:delete"
                          width="20"
                        />
                      </el-button>
                    </template>
                  </el-popconfirm>
                </span>
              </el-tooltip>
            </Perms>
            <Perms :value="['timed:r']">
              <el-tooltip :hide-after="0" content="执行记录" placement="top">
                <span class="ml-3">
                  <el-button link type="primary" @click="handleExecute(row)">
                    <IconifyIconOffline
                      height="20"
                      icon="icon-park-outline:notes"
                      width="20"
                    />
                  </el-button>
                </span>
              </el-tooltip>
            </Perms>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <div class="pagination-info" />
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :background="true"
          :page-sizes="[15, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    <!-- 编辑弹窗组件 -->
    <EditTasks
      v-model:visible="dialogVisible"
      :is-edit="isEditMode"
      :task-data="currentTask"
      @success="timedList(currentPage, pageSize, searchQuery)"
    />
    <TasksDetails v-model:visible="detailsDialogVisible" :task="detailsTask" />
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import { QuestionFilled, Search } from "@element-plus/icons-vue";
import EditTasks from "./components/EditTasks.vue";
import { deleteTimed, getTimedList, updateTimed } from "@/api/timed";
import { ElMessage } from "element-plus";
import TasksDetails from "./components/TasksDetails.vue";
import IconifyIconOffline from "@/components/ReIcon/src/iconifyIconOffline";
// 搜索查询
const searchQuery = ref("");
// 分页相关
const currentPage = ref(1);
const pageSize = ref(15);
const total = ref(0);

// 弹窗控制
const dialogVisible = ref(false);
const isEditMode = ref(false);
const currentTask = ref({});
// 新增：执行记录弹窗控制
const detailsDialogVisible = ref(false);
const detailsTask = ref({});

// 表格数据
const tableData = ref([]);

// 打开弹窗
const handleOpenDialog = (isEdit, row) => {
  isEditMode.value = isEdit;
  if (isEdit && row) {
    currentTask.value = { ...row };
  } else {
    currentTask.value = {};
  }
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = row => {
  handleOpenDialog(true, row);
};

// 处理删除
const handleDelete = async row => {
  const res = await deleteTimed({ id: row.id });
  if (res.code === 0) {
    ElMessage.success("删除成功");
    timedList(currentPage.value, pageSize.value, searchQuery.value);
  } else {
    ElMessage.error(res.msg);
  }
};

// 处理执行记录
const handleExecute = row => {
  detailsTask.value = row;
  detailsDialogVisible.value = true;
};

// 处理状态变更
const handleStatusChange = async row => {
  try {
    const newStatus = row.enable === 1 ? 0 : 1;
    const res = await updateTimed({ id: row.id, enable: newStatus });
    if (res.code === 0) {
      ElMessage.success("状态更新成功");
      timedList(currentPage.value, pageSize.value, searchQuery.value);
      return true;
    } else {
      return false;
    }
  } catch (error) {
    return false;
  }
};

// 处理分页大小变化
const handleSizeChange = val => {
  pageSize.value = val;
  // 重新加载数据
  timedList(currentPage.value, pageSize.value, searchQuery.value);
};

// 处理页码变化
const handleCurrentChange = val => {
  currentPage.value = val;
  // 重新加载数据
  timedList(currentPage.value, pageSize.value, searchQuery.value);
};

onMounted(() => {
  // 初始加载数据
  timedList(currentPage.value, pageSize.value, searchQuery.value);
});

// 加载列表数据
const timedList = async (page, size, keyword) => {
  let res = await getTimedList({
    page: page,
    size: size,
    keyword: keyword
  });
  if (res.code === 0) {
    tableData.value = res.data.items;
    total.value = res.data.total;
  }
};

// 时间格式化函数
const formatTime = timestamp => {
  if (!timestamp) return "";
  const date = new Date(timestamp);
  return date
    .toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false
    })
    .replace(/\//g, "-");
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1; // 重置到第一页
  timedList(currentPage.value, pageSize.value, searchQuery.value);
};

// 处理清除
const handleClear = () => {
  searchQuery.value = "";
  currentPage.value = 1; // 重置到第一页
  timedList(currentPage.value, pageSize.value, "");
};
</script>

<style lang="scss" scoped>
.timer-tasks {
  padding: 0;
}

.header-actions {
  display: flex;
  justify-content: space-between;
}

.search-area {
  display: flex;
}

// 添加分页容器样式
.pagination-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16px;

  .pagination-info {
    color: #606266;
  }
}

.mr-2 {
  margin-right: 8px;
}

.tooltip-content {
  font-size: 14px;
  line-height: 1.5;
  max-width: 400px;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>
