import { ActionScriptRun } from "@/api/warroom";
import { ElMessage } from "element-plus";

export async function executeDrawerAction(
  id: string,
  type: string,
  Obj: any,
  data: any
) {
  if (type === "flow") {
    // 构造flowData对象
    const flowData = {
      id,
      type,
      playbook_input: {
        flow_id: Obj.version_id,
        flow_input: data.flow_input
      }
    };
    const playRes = (await ActionScriptRun({
      id: flowData.id,
      type: type,
      playbook_input: flowData.playbook_input
    })) as any;
    if (playRes.code === 0) {
      ElMessage.success("执行成功");
    } else {
      ElMessage.error("执行失败");
    }
    console.log("flowData", flowData);
    return playRes;
  } else {
    // 构建action_input对象
    const actionsDara = {
      id,
      type,
      action_input: {
        ...data
      }
    };
    console.log("actionsDara", actionsDara);
    const actionRes = (await ActionScriptRun(actionsDara)) as any;
    if (actionRes.code === 0) {
      ElMessage.success("执行成功");
    } else {
      ElMessage.error("执行失败");
    }
    return actionRes;
  }
}
