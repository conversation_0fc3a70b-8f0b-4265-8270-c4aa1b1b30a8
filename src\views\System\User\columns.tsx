import { ref } from "vue";
import { utils, writeFile } from "xlsx";
import { message } from "@/utils/message";
import { getUserList } from "@/api/userList";

export function useColumns() {
  const dataList = ref([]);
  const totals = ref(0);
  const currentPage = ref(1);

  // 初始化加载
  getUserList({ page: currentPage.value, size: 15 }).then((res: any) => {
    dataList.value = res.data.data;
    totals.value = res.data.total; // 使用总条数，而不是总页数
  });

  // 表格的表头内容
  const columns: TableColumnList = [
    {
      label: "用户名",
      prop: "username"
    },
    {
      label: "姓名",
      prop: "display_name"
    },
    {
      label: "角色",
      prop: "roles",
      formatter: (row: any) => {
        return Array.isArray(row.roles) ? row.roles.join(", ") : "";
      }
    },
    {
      label: "手机号",
      prop: "phone"
    },
    {
      label: "邮箱",
      prop: "email"
    },
    {
      label: "账号状态",
      prop: "disabled",
      slot: "disabled"
    },
    {
      label: "多因素认证",
      prop: "mfa_enable",
      slot: "mfa_enable"
    },
    {
      label: "修改时间",
      prop: "ctime",
      formatter: (row: any) => {
        return new Date(row.ctime)
          .toLocaleString("zh-CN", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
            hour: "2-digit",
            minute: "2-digit",
            second: "2-digit",
            hour12: false
          })
          .replace(/\//g, "-");
      }
    },
    {
      label: "创建时间",
      prop: "utime",
      formatter: (row: any) => {
        return new Date(row.utime)
          .toLocaleString("zh-CN", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
            hour: "2-digit",
            minute: "2-digit",
            second: "2-digit",
            hour12: false
          })
          .replace(/\//g, "-");
      }
    },
    {
      label: "操作",
      fixed: "right",
      width: 160,
      slot: "operation"
    }
  ];
  // 导出excel
  const exportExcel = () => {
    const res = dataList.value.map(item => {
      const arr = [];
      columns.forEach(column => {
        arr.push(item[column.prop as string]);
      });
      return arr;
    });
    const titleList = [];
    columns.forEach(column => {
      titleList.push(column.label);
    });
    res.unshift(titleList);
    const workSheet = utils.aoa_to_sheet(res);
    const workBook = utils.book_new();
    utils.book_append_sheet(workBook, workSheet, "数据报表");
    writeFile(workBook, "pure-admin-table.xlsx");
    message("导出成功", {
      type: "success"
    });
  };
  // 获取用户列表
  const fetchUserData = (page: number, size: number, username: string) => {
    currentPage.value = page;
    const params: any = { page, size };
    if (username) {
      params.username = username;
    }
    getUserList(params)
      .then((res: any) => {
        dataList.value = res.data.data;
        totals.value = res.data.total;
      })
      .catch(error => {
        message(error.response.data.message, {
          type: "error"
        });
      });
  };
  // 清空搜索
  const ClearSearch = () => {
    fetchUserData(1, 15, "");
  };
  // 修改每页条数
  const handleSizeChange = (size: number) => {
    fetchUserData(1, size, "");
  };
  return {
    columns,
    dataList,
    totals,
    exportExcel,
    fetchUserData,
    ClearSearch,
    handleSizeChange,
    currentPage
  };
}
