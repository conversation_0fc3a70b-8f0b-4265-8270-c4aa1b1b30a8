// timestamp: string; => ctime, utime ?
// 群聊/私聊/系统/公告
export enum MessageType {
  PUBLIC = "public",
  PRIVATE = "private",
  SYSTEM = "system",
  ANNOUNCEMENT = "announcement",
  RUNLOG = "runlog" //执行结果信息
}

export interface User {
  id: string;
  username: string;
  display_name?: string | null;
  online_status?: boolean;
  roles: string[]; // 角色信息,一个用户可能有多个角色身份
}

export interface Room {
  id: string;
  name: string;
  creator_id: string;
  is_default?: boolean;
}

export interface Message {
  run_id: any;
  message_id: string;
  type: MessageType;
  sender_id?: string;
  sender_name?: string;
  receiver_id?: string;
  room_id?: string;
  content: string;
  image_content?: string;
  timestamp: string;
  is_recalled?: boolean;
  is_pinned?: boolean;
}

export interface Announcement {
  id: string;
  content: string;
  image_content?: string;
  timestamp: string;
  sender_name: string;
  room_id: string;
}

export interface NewAnnouncementMessage {
  type: "new_announcement";
  id: string;
  content: string;
  image_content?: string;
  timestamp: string;
  sender_name: string;
  room_id: string;
}

export interface AnnouncementList {
  type: "announcement_list";
  announcements: Announcement[];
}

export interface SystemMessage {
  type: "system";
  content: string;
  timestamp: string;
}

export interface UserListMessage {
  type: "user_list";
  users: User[];
}

export interface HeartbeatMessage {
  type: "heartbeat";
  status: "ping" | "pong";
}

export interface RoomListMessage {
  type: "room_list";
  rooms: Room[];
}
